{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAcD,4DAA4D;AAC5D,MAAM,qCAAe,OAAO,GAAG,CAAC;AAKzB,SAAS;IACd,IAAI,SAAS,OAAO,WAAW,eAAe,MAAM,CAAC,mCAAa,IAE5D,OAAO,cAAc,eAAgB,CAAA,UAAU,QAAQ,IAAI,UAAU,YAAY,AAAD,KACjF;IAEL,IAAI;QACF,KAAK,cAAc,CAAC,kBAAkB,CAAC;YAAC;SAAO;IACjD,EAAE,OAAM;QACN,SAAS;IACX;IACA,OAAO;gBACL;QACA,WAAW,CAAA,GAAA,yCAAI,EAAE,UAAU,QAAQ;IACrC;AACF;AAEA,IAAI,sCAAgB;AACpB,IAAI,kCAAY,IAAI;AAEpB,SAAS;IACP,sCAAgB;IAChB,KAAK,IAAI,YAAY,gCACnB,SAAS;AAEb;AAKO,SAAS;IACd,IAAI,QAAQ,CAAA,GAAA,eAAO;IACnB,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,eAAO,EAAE;IAEjD,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,gCAAU,IAAI,KAAK,GACrB,OAAO,gBAAgB,CAAC,kBAAkB;QAG5C,gCAAU,GAAG,CAAC;QAEd,OAAO;YACL,gCAAU,MAAM,CAAC;YACjB,IAAI,gCAAU,IAAI,KAAK,GACrB,OAAO,mBAAmB,CAAC,kBAAkB;QAEjD;IACF,GAAG,EAAE;IAEL,0EAA0E;IAC1E,kFAAkF;IAClF,IAAI,OACF,OAAO;QACL,QAAQ;QACR,WAAW;IACb;IAGF,OAAO;AACT", "sources": ["packages/@react-aria/i18n/src/useDefaultLocale.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Direction} from '@react-types/shared';\nimport {isRTL} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport interface Locale {\n  /** The [BCP47](https://www.ietf.org/rfc/bcp/bcp47.txt) language code for the locale. */\n  locale: string,\n  /** The writing direction for the locale. */\n  direction: Direction\n}\n\n// Locale passed from server by PackageLocalizationProvider.\nconst localeSymbol = Symbol.for('react-aria.i18n.locale');\n\n/**\n * Gets the locale setting of the browser.\n */\nexport function getDefaultLocale(): Locale {\n  let locale = typeof window !== 'undefined' && window[localeSymbol]\n    // @ts-ignore\n    || (typeof navigator !== 'undefined' && (navigator.language || navigator.userLanguage))\n    || 'en-US';\n\n  try {\n    Intl.DateTimeFormat.supportedLocalesOf([locale]);\n  } catch {\n    locale = 'en-US';\n  }\n  return {\n    locale,\n    direction: isRTL(locale) ? 'rtl' : 'ltr'\n  };\n}\n\nlet currentLocale = getDefaultLocale();\nlet listeners = new Set<(locale: Locale) => void>();\n\nfunction updateLocale() {\n  currentLocale = getDefaultLocale();\n  for (let listener of listeners) {\n    listener(currentLocale);\n  }\n}\n\n/**\n * Returns the current browser/system language, and updates when it changes.\n */\nexport function useDefaultLocale(): Locale {\n  let isSSR = useIsSSR();\n  let [defaultLocale, setDefaultLocale] = useState(currentLocale);\n\n  useEffect(() => {\n    if (listeners.size === 0) {\n      window.addEventListener('languagechange', updateLocale);\n    }\n\n    listeners.add(setDefaultLocale);\n\n    return () => {\n      listeners.delete(setDefaultLocale);\n      if (listeners.size === 0) {\n        window.removeEventListener('languagechange', updateLocale);\n      }\n    };\n  }, []);\n\n  // We cannot determine the browser's language on the server, so default to\n  // en-US. This will be updated after hydration on the client to the correct value.\n  if (isSSR) {\n    return {\n      locale: 'en-US',\n      direction: 'ltr'\n    };\n  }\n\n  return defaultLocale;\n}\n"], "names": [], "version": 3, "file": "useDefaultLocale.module.js.map"}