{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAQD,MAAM,8BAAQ,IAAI;AAClB,SAAS,0CAAoB,OAAyB;IACpD,IAAI,aAAa,4BAAM,GAAG,CAAC;IAC3B,IAAI,CAAC,YAAY;QACf,aAAa,IAAI,CAAA,GAAA,wBAAgB,EAAE;QACnC,4BAAM,GAAG,CAAC,SAAS;IACrB;IAEA,OAAO;AACT;AAQO,SAAS,0CAAoB,OAAyB;IAC3D,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,yCAAQ;IACvB,IAAI,aAAa,CAAA,GAAA,cAAM,EAAE,IAAM,0CAAoB,UAAU;QAAC;KAAQ;IACtE,IAAI,YAAY,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,CAAA,GAAA,uBAAe,EAAE,QAAQ,aAAa;QAAC;QAAQ;KAAW;IAC5F,OAAO,CAAA,GAAA,kBAAU,EAAE,CAAC,KAAK,YAAc,UAAU,MAAM,CAAC,KAAK,YAAY;QAAC;KAAU;AACtF", "sources": ["packages/@react-aria/i18n/src/useMessageFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {LocalizedStrings, MessageDictionary, MessageFormatter} from '@internationalized/message';\nimport {useCallback, useMemo} from 'react';\nimport {useLocale} from './context';\n\nexport type FormatMessage = (key: string, variables?: {[key: string]: any}) => string;\n\nconst cache = new WeakMap();\nfunction getCachedDictionary(strings: LocalizedStrings) {\n  let dictionary = cache.get(strings);\n  if (!dictionary) {\n    dictionary = new MessageDictionary(strings);\n    cache.set(strings, dictionary);\n  }\n\n  return dictionary;\n}\n\n/**\n * Handles formatting ICU Message strings to create localized strings for the current locale.\n * Automatically updates when the locale changes, and handles caching of messages for performance.\n * @param strings - A mapping of languages to strings by key.\n * @deprecated - use useLocalizedStringFormatter instead.\n */\nexport function useMessageFormatter(strings: LocalizedStrings): FormatMessage {\n  let {locale} = useLocale();\n  let dictionary = useMemo(() => getCachedDictionary(strings), [strings]);\n  let formatter = useMemo(() => new MessageFormatter(locale, dictionary), [locale, dictionary]);\n  return useCallback((key, variables) => formatter.format(key, variables), [formatter]);\n}\n"], "names": [], "version": 3, "file": "useMessageFormatter.module.js.map"}