{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAWM,SAAS,0CAAmB,UAA+B,CAAC,CAAC;IAClE,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,yCAAQ;IACvB,OAAO,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,CAAA,GAAA,sBAAc,EAAE,QAAQ,UAAU;QAAC;QAAQ;KAAQ;AAC9E", "sources": ["packages/@react-aria/i18n/src/useNumberFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {NumberFormatOptions, NumberFormatter} from '@internationalized/number';\nimport {useLocale} from './context';\nimport {useMemo} from 'react';\n\n/**\n * Provides localized number formatting for the current locale. Automatically updates when the locale changes,\n * and handles caching of the number formatter for performance.\n * @param options - Formatting options.\n */\nexport function useNumberFormatter(options: NumberFormatOptions = {}): Intl.NumberFormat {\n  let {locale} = useLocale();\n  return useMemo(() => new NumberFormatter(locale, options), [locale, options]);\n}\n"], "names": [], "version": 3, "file": "useNumberFormatter.module.js.map"}