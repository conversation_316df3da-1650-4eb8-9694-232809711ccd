{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAUM,SAAS,yCAAiB,UAAkC,CAAC,CAAC;IACnE,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,mCAAQ;IACvB,OAAO,CAAA,GAAA,oBAAM,EAAE,IAAM,IAAI,KAAK,UAAU,CAAC,QAAQ,UAAU;QAAC;QAAQ;KAAQ;AAC9E", "sources": ["packages/@react-aria/i18n/src/useListFormatter.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useLocale} from './context';\nimport {useMemo} from 'react';\n\n/**\n * Provides localized list formatting for the current locale. Automatically updates when the locale changes,\n * and handles caching of the list formatter for performance.\n * @param options - Formatting options.\n */\nexport function useListFormatter(options: Intl.ListFormatOptions = {}): Intl.ListFormat {\n  let {locale} = useLocale();\n  return useMemo(() => new Intl.ListFormat(locale, options), [locale, options]);\n}\n"], "names": [], "version": 3, "file": "useListFormatter.main.js.map"}