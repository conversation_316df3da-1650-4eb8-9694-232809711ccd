{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAMD,MAAM,8BAAQ,IAAI;AAClB,SAAS,0CAAiE,OAA+B;IACvG,IAAI,aAAa,4BAAM,GAAG,CAAC;IAC3B,IAAI,CAAC,YAAY;QACf,aAAa,IAAI,CAAA,GAAA,gCAAwB,EAAE;QAC3C,4BAAM,GAAG,CAAC,SAAS;IACrB;IAEA,OAAO;AACT;AAKO,SAAS,0CAA4F,OAA+B,EAAE,WAAoB;IAC/J,OAAO,AAAC,eAAe,CAAA,GAAA,gCAAwB,EAAE,6BAA6B,CAAC,gBAAiB,0CAAoB;AACtH;AAOO,SAAS,0CAA2F,OAA+B,EAAE,WAAoB;IAC9J,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,yCAAQ;IACvB,IAAI,aAAa,0CAA6B,SAAS;IACvD,OAAO,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,CAAA,GAAA,+BAAuB,EAAE,QAAQ,aAAa;QAAC;QAAQ;KAAW;AAC7F", "sources": ["packages/@react-aria/i18n/src/useLocalizedStringFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {LocalizedString, LocalizedStringDictionary, LocalizedStringFormatter, LocalizedStrings} from '@internationalized/string';\nimport {useLocale} from './context';\nimport {useMemo} from 'react';\n\nconst cache = new WeakMap();\nfunction getCachedDictionary<K extends string, T extends LocalizedString>(strings: LocalizedStrings<K, T>): LocalizedStringDictionary<K, T> {\n  let dictionary = cache.get(strings);\n  if (!dictionary) {\n    dictionary = new LocalizedStringDictionary(strings);\n    cache.set(strings, dictionary);\n  }\n\n  return dictionary;\n}\n\n/**\n * Returns a cached LocalizedStringDictionary for the given strings.\n */\nexport function useLocalizedStringDictionary<K extends string = string, T extends LocalizedString = string>(strings: LocalizedStrings<K, T>, packageName?: string): LocalizedStringDictionary<K, T> {\n  return (packageName && LocalizedStringDictionary.getGlobalDictionaryForPackage(packageName)) || getCachedDictionary(strings);\n}\n\n/**\n * Provides localized string formatting for the current locale. Supports interpolating variables,\n * selecting the correct pluralization, and formatting numbers. Automatically updates when the locale changes.\n * @param strings - A mapping of languages to localized strings by key.\n */\nexport function useLocalizedStringFormatter<K extends string = string, T extends LocalizedString = string>(strings: LocalizedStrings<K, T>, packageName?: string): LocalizedStringFormatter<K, T> {\n  let {locale} = useLocale();\n  let dictionary = useLocalizedStringDictionary(strings, packageName);\n  return useMemo(() => new LocalizedStringFormatter(locale, dictionary), [locale, dictionary]);\n}\n"], "names": [], "version": 3, "file": "useLocalizedStringFormatter.module.js.map"}