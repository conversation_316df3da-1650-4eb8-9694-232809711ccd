{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAkBM,SAAS,yCAAU,OAA8B;IACtD,IAAI,WAAW,CAAA,GAAA,yCAAU,EAAE;QACzB,OAAO;QACP,GAAG,OAAO;IACZ;IAEA,mFAAmF;IACnF,IAAI,aAAa,CAAA,GAAA,kBAAU,EAAE,CAAC,QAAQ;QACpC,IAAI,UAAU,MAAM,KAAK,GACvB,OAAO;QAGT,gDAAgD;QAChD,kEAAkE;QAClE,SAAS,OAAO,SAAS,CAAC;QAC1B,YAAY,UAAU,SAAS,CAAC;QAChC,OAAO,SAAS,OAAO,CAAC,OAAO,KAAK,CAAC,GAAG,UAAU,MAAM,GAAG,eAAe;IAC5E,GAAG;QAAC;KAAS;IAEb,IAAI,WAAW,CAAA,GAAA,kBAAU,EAAE,CAAC,QAAQ;QAClC,IAAI,UAAU,MAAM,KAAK,GACvB,OAAO;QAGT,SAAS,OAAO,SAAS,CAAC;QAC1B,YAAY,UAAU,SAAS,CAAC;QAChC,OAAO,SAAS,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC,UAAU,MAAM,GAAG,eAAe;IAC1E,GAAG;QAAC;KAAS;IAEb,IAAI,WAAW,CAAA,GAAA,kBAAU,EAAE,CAAC,QAAQ;QAClC,IAAI,UAAU,MAAM,KAAK,GACvB,OAAO;QAGT,SAAS,OAAO,SAAS,CAAC;QAC1B,YAAY,UAAU,SAAS,CAAC;QAEhC,IAAI,OAAO;QACX,IAAI,WAAW,UAAU,MAAM;QAC/B,MAAO,OAAO,YAAY,OAAO,MAAM,EAAE,OAAQ;YAC/C,IAAI,QAAQ,OAAO,KAAK,CAAC,MAAM,OAAO;YACtC,IAAI,SAAS,OAAO,CAAC,WAAW,WAAW,GACzC,OAAO;QAEX;QAEA,OAAO;IACT,GAAG;QAAC;KAAS;IAEb,OAAO,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;wBACpB;sBACA;sBACA;QACF,CAAA,GAAI;QAAC;QAAY;QAAU;KAAS;AACtC", "sources": ["packages/@react-aria/i18n/src/useFilter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useMemo} from 'react';\nimport {useCollator} from './useCollator';\n\nexport interface Filter {\n  /** Returns whether a string starts with a given substring. */\n  startsWith(string: string, substring: string): boolean,\n  /** Returns whether a string ends with a given substring. */\n  endsWith(string: string, substring: string): boolean,\n  /** Returns whether a string contains a given substring. */\n  contains(string: string, substring: string): boolean\n}\n\n/**\n * Provides localized string search functionality that is useful for filtering or matching items\n * in a list. Options can be provided to adjust the sensitivity to case, diacritics, and other parameters.\n */\nexport function useFilter(options?: Intl.CollatorOptions): Filter {\n  let collator = useCollator({\n    usage: 'search',\n    ...options\n  });\n\n  // TODO(later): these methods don't currently support the ignorePunctuation option.\n  let startsWith = useCallback((string, substring) => {\n    if (substring.length === 0) {\n      return true;\n    }\n\n    // Normalize both strings so we can slice safely\n    // TODO: take into account the ignorePunctuation option as well...\n    string = string.normalize('NFC');\n    substring = substring.normalize('NFC');\n    return collator.compare(string.slice(0, substring.length), substring) === 0;\n  }, [collator]);\n\n  let endsWith = useCallback((string, substring) => {\n    if (substring.length === 0) {\n      return true;\n    }\n\n    string = string.normalize('NFC');\n    substring = substring.normalize('NFC');\n    return collator.compare(string.slice(-substring.length), substring) === 0;\n  }, [collator]);\n\n  let contains = useCallback((string, substring) => {\n    if (substring.length === 0) {\n      return true;\n    }\n\n    string = string.normalize('NFC');\n    substring = substring.normalize('NFC');\n\n    let scan = 0;\n    let sliceLen = substring.length;\n    for (; scan + sliceLen <= string.length; scan++) {\n      let slice = string.slice(scan, scan + sliceLen);\n      if (collator.compare(substring, slice) === 0) {\n        return true;\n      }\n    }\n\n    return false;\n  }, [collator]);\n\n  return useMemo(() => ({\n    startsWith,\n    endsWith,\n    contains\n  }), [startsWith, endsWith, contains]);\n}\n"], "names": [], "version": 3, "file": "useFilter.module.js.map"}