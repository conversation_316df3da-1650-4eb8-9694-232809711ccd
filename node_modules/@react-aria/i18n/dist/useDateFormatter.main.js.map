{"mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AAgBM,SAAS,0CAAiB,OAA8B;IAC7D,yGAAyG;IACzG,UAAU,CAAA,GAAA,iCAAU,EAAE,oBAAA,qBAAA,UAAW,CAAC,GAAG;IACrC,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,mCAAQ;IACvB,OAAO,CAAA,GAAA,oBAAM,EAAE,IAAM,IAAI,CAAA,GAAA,0CAAY,EAAE,QAAQ,UAAU;QAAC;QAAQ;KAAQ;AAC5E;AAEA,SAAS,8BAAQ,CAAuB,EAAE,CAAuB;IAC/D,IAAI,MAAM,GACR,OAAO;IAGT,IAAI,QAAQ,OAAO,IAAI,CAAC;IACxB,IAAI,QAAQ,OAAO,IAAI,CAAC;IACxB,IAAI,MAAM,MAAM,KAAK,MAAM,MAAM,EAC/B,OAAO;IAGT,KAAK,IAAI,OAAO,MAAO;QACrB,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EACnB,OAAO;IAEX;IAEA,OAAO;AACT", "sources": ["packages/@react-aria/i18n/src/useDateFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DateFormatter} from '@internationalized/date';\nimport {useDeepMemo} from '@react-aria/utils';\nimport {useLocale} from './context';\nimport {useMemo} from 'react';\n\nexport interface DateFormatterOptions extends Intl.DateTimeFormatOptions {\n  calendar?: string\n}\n\n/**\n * Provides localized date formatting for the current locale. Automatically updates when the locale changes,\n * and handles caching of the date formatter for performance.\n * @param options - Formatting options.\n */\nexport function useDateFormatter(options?: DateFormatterOptions): DateFormatter {\n  // Reuse last options object if it is shallowly equal, which allows the useMemo result to also be reused.\n  options = useDeepMemo(options ?? {}, isEqual);\n  let {locale} = useLocale();\n  return useMemo(() => new DateFormatter(locale, options), [locale, options]);\n}\n\nfunction isEqual(a: DateFormatterOptions, b: DateFormatterOptions) {\n  if (a === b) {\n    return true;\n  }\n\n  let aKeys = Object.keys(a);\n  let bKeys = Object.keys(b);\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n\n  for (let key of aKeys) {\n    if (b[key] !== a[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "names": [], "version": 3, "file": "useDateFormatter.main.js.map"}