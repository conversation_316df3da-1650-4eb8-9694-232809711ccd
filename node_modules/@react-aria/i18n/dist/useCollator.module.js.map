{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAID,IAAI,8BAAQ,IAAI;AAOT,SAAS,0CAAY,OAA8B;IACxD,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,yCAAQ;IAEvB,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,4BAAM,GAAG,CAAC,WACZ,OAAO,4BAAM,GAAG,CAAC;IAGnB,IAAI,YAAY,IAAI,KAAK,QAAQ,CAAC,QAAQ;IAC1C,4BAAM,GAAG,CAAC,UAAU;IACpB,OAAO;AACT", "sources": ["packages/@react-aria/i18n/src/useCollator.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useLocale} from './context';\n\nlet cache = new Map<string, Intl.Collator>();\n\n/**\n * Provides localized string collation for the current locale. Automatically updates when the locale changes,\n * and handles caching of the collator for performance.\n * @param options - Collator options.\n */\nexport function useCollator(options?: Intl.CollatorOptions): Intl.Collator {\n  let {locale} = useLocale();\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (cache.has(cacheKey)) {\n    return cache.get(cacheKey)!;\n  }\n\n  let formatter = new Intl.Collator(locale, options);\n  cache.set(cacheKey, formatter);\n  return formatter;\n}\n"], "names": [], "version": 3, "file": "useCollator.module.js.map"}