{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAED,8CAA8C;AAC9C,MAAM,oCAAc,IAAI,IAAI;IAAC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;CAAO;AAC5G,MAAM,kCAAY,IAAI,IAAI;IAAC;IAAM;IAAM;IAAO;IAAO;IAAO;IAAO;IAAM;IAAM;IAAO;IAAM;IAAM;IAAO;IAAO;IAAO;IAAM;IAAM;IAAM;IAAM;CAAK;AAK7I,SAAS,0CAAM,YAAoB;IACxC,kFAAkF;IAClF,IAAI,KAAK,MAAM,EAAE;QACf,IAAI,SAAS,IAAI,KAAK,MAAM,CAAC,cAAc,QAAQ;QAEnD,6DAA6D;QAC7D,6GAA6G;QAC7G,2GAA2G;QAC3G,IAAI,WAAW,OAAO,OAAO,WAAW,KAAK,aAAa,OAAO,WAAW,KAAK,OAAO,QAAQ;QAChG,IAAI,UACF,OAAO,SAAS,SAAS,KAAK;QAGhC,oCAAoC;QACpC,uGAAuG;QACvG,IAAI,OAAO,MAAM,EACf,OAAO,kCAAY,GAAG,CAAC,OAAO,MAAM;IAExC;IAEA,gEAAgE;IAChE,IAAI,OAAO,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE;IACrC,OAAO,gCAAU,GAAG,CAAC;AACvB", "sources": ["packages/@react-aria/i18n/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// https://en.wikipedia.org/wiki/Right-to-left\nconst RTL_SCRIPTS = new Set(['Arab', 'Syrc', 'Samr', 'Mand', 'Thaa', 'Mend', 'Nkoo', 'Adlm', 'Rohg', 'Hebr']);\nconst RTL_LANGS = new Set(['ae', 'ar', 'arc', 'bcc', 'bqi', 'ckb', 'dv', 'fa', 'glk', 'he', 'ku', 'mzn', 'nqo', 'pnb', 'ps', 'sd', 'ug', 'ur', 'yi']);\n\n/**\n * Determines if a locale is read right to left using [Intl.Locale]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale}.\n */\nexport function isRTL(localeString: string): boolean {\n  // If the Intl.Locale API is available, use it to get the locale's text direction.\n  if (Intl.Locale) {\n    let locale = new Intl.Locale(localeString).maximize();\n\n    // Use the text info object to get the direction if possible.\n    // @ts-ignore - this was implemented as a property by some browsers before it was standardized as a function.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/getTextInfo\n    let textInfo = typeof locale.getTextInfo === 'function' ? locale.getTextInfo() : locale.textInfo;\n    if (textInfo) {\n      return textInfo.direction === 'rtl';\n    }\n\n    // Fallback: guess using the script.\n    // This is more accurate than guessing by language, since languages can be written in multiple scripts.\n    if (locale.script) {\n      return RTL_SCRIPTS.has(locale.script);\n    }\n  }\n\n  // If not, just guess by the language (first part of the locale)\n  let lang = localeString.split('-')[0];\n  return RTL_LANGS.has(lang);\n}\n"], "names": [], "version": 3, "file": "utils.main.js.map"}