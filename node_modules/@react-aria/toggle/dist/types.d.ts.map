{"mappings": ";;;;AAmBA;IACE,+CAA+C;IAC/C,UAAU,EAAE,oBAAoB,gBAAgB,CAAC,CAAC;IAClD,+CAA+C;IAC/C,UAAU,EAAE,oBAAoB,gBAAgB,CAAC,CAAC;IAClD,sCAAsC;IACtC,UAAU,EAAE,OAAO,CAAC;IACpB,gDAAgD;IAChD,SAAS,EAAE,OAAO,CAAC;IACnB,sCAAsC;IACtC,UAAU,EAAE,OAAO,CAAC;IACpB,uCAAuC;IACvC,UAAU,EAAE,OAAO,CAAC;IACpB,qCAAqC;IACrC,SAAS,EAAE,OAAO,CAAA;CACnB;AAED;;GAEG;AACH,0BAA0B,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,UAAU,CAsFzH;AChHD,YAAY,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC", "sources": ["packages/@react-aria/toggle/src/packages/@react-aria/toggle/src/useToggle.ts", "packages/@react-aria/toggle/src/packages/@react-aria/toggle/src/index.ts", "packages/@react-aria/toggle/src/index.ts"], "sourcesContent": [null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport {useToggle} from './useToggle';\nexport type {AriaToggleProps} from '@react-types/checkbox';\nexport type {ToggleAria} from './useToggle';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}