{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAyBM,SAAS,0CAAa,KAAqB;IAChD,IAAI,WAAW,CAAA,GAAA,qBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI;IACJ,iGAAiG;IACjG,0CAA0C;IAC1C,IAAI,MAAM,WAAW,KAAK,YACxB,kBAAkB;IAEpB,4EAA4E;IAC5E,IAAI,MAAM,WAAW,KAAK,MACxB,OAAO;QACL,gBAAgB;YACd,GAAG,QAAQ;YACX,MAAM;YACN,oBAAoB;QACtB;IACF;IAEF,OAAO;QAAC,gBAAgB;IAAQ;AAClC", "sources": ["packages/@react-aria/separator/src/useSeparator.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMAttributes, DOMProps, Orientation} from '@react-types/shared';\nimport {filterDOMProps} from '@react-aria/utils';\n\nexport interface SeparatorProps extends DOMProps, AriaLabelingProps {\n  /**\n   * The orientation of the separator.\n   * @default 'horizontal'\n   */\n  orientation?: Orientation,\n  /** The HTML element type that will be used to render the separator. */\n  elementType?: string\n}\n\nexport interface SeparatorAria {\n  /** Props for the separator element. */\n  separatorProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for a separator.\n * A separator is a visual divider between two groups of content,\n * e.g. groups of menu items or sections of a page.\n */\nexport function useSeparator(props: SeparatorProps): SeparatorAria {\n  let domProps = filterDOMProps(props, {labelable: true});\n  let ariaOrientation;\n  // if orientation is horizontal, aria-orientation default is horizontal, so we leave it undefined\n  // if it's vertical, we need to specify it\n  if (props.orientation === 'vertical') {\n    ariaOrientation = 'vertical';\n  }\n  // hr elements implicitly have role = separator and a horizontal orientation\n  if (props.elementType !== 'hr') {\n    return {\n      separatorProps: {\n        ...domProps,\n        role: 'separator',\n        'aria-orientation': ariaOrientation\n      }\n    };\n  }\n  return {separatorProps: domProps};\n}\n"], "names": [], "version": 3, "file": "useSeparator.module.js.map"}