{"mappings": ";AAeA,+BAAgC,SAAQ,QAAQ,EAAE,iBAAiB;IACjE;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B,uEAAuE;IACvE,WAAW,CAAC,EAAE,MAAM,CAAA;CACrB;AAED;IACE,uCAAuC;IACvC,cAAc,EAAE,aAAa,CAAA;CAC9B;AAED;;;;GAIG;AACH,6BAA6B,KAAK,EAAE,cAAc,GAAG,aAAa,CAmBjE", "sources": ["packages/@react-aria/separator/src/packages/@react-aria/separator/src/useSeparator.ts", "packages/@react-aria/separator/src/packages/@react-aria/separator/src/index.ts", "packages/@react-aria/separator/src/index.ts"], "sourcesContent": [null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport type {SeparatorProps, SeparatorAria} from './useSeparator';\nexport {useSeparator} from './useSeparator';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}