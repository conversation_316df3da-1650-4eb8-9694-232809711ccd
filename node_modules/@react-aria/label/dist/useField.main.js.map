{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAoBM,SAAS,0CAAS,KAAqB;IAC5C,IAAI,eAAC,WAAW,gBAAE,YAAY,aAAE,SAAS,mBAAE,eAAe,EAAC,GAAG;IAC9D,IAAI,cAAC,UAAU,cAAE,UAAU,EAAC,GAAG,CAAA,GAAA,kCAAO,EAAE;IAExC,IAAI,gBAAgB,CAAA,GAAA,+BAAQ,EAAE;QAAC,QAAQ;QAAc,QAAQ;QAAe;QAAW;KAAgB;IACvG,IAAI,iBAAiB,CAAA,GAAA,+BAAQ,EAAE;QAAC,QAAQ;QAAc,QAAQ;QAAe;QAAW;KAAgB;IAExG,aAAa,CAAA,GAAA,gCAAS,EAAE,YAAY;QAClC,oBAAoB;YAClB;YACA,0LAA0L;YAC1L;YACA,KAAK,CAAC,mBAAmB;SAC1B,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;IACjC;IAEA,OAAO;oBACL;oBACA;QACA,kBAAkB;YAChB,IAAI;QACN;QACA,mBAAmB;YACjB,IAAI;QACN;IACF;AACF", "sources": ["packages/@react-aria/label/src/useField.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, HelpTextProps, Validation} from '@react-types/shared';\nimport {LabelAria, LabelAriaProps, useLabel} from './useLabel';\nimport {mergeProps, useSlotId} from '@react-aria/utils';\n\nexport interface AriaFieldProps extends LabelAriaProps, HelpTextProps, Omit<Validation<any>, 'isRequired'> {}\n\nexport interface FieldAria extends LabelAria {\n  /** Props for the description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for input fields.\n * Fields accept user input, gain context from their label, and may display a description or error message.\n * @param props - Props for the Field.\n */\nexport function useField(props: AriaFieldProps): FieldAria {\n  let {description, errorMessage, isInvalid, validationState} = props;\n  let {labelProps, fieldProps} = useLabel(props);\n\n  let descriptionId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n  let errorMessageId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n\n  fieldProps = mergeProps(fieldProps, {\n    'aria-describedby': [\n      descriptionId,\n      // Use aria-describedby for error message because aria-errormessage is unsupported using VoiceOver or NVDA. See https://github.com/adobe/react-spectrum/issues/1346#issuecomment-740136268\n      errorMessageId,\n      props['aria-describedby']\n    ].filter(Boolean).join(' ') || undefined\n  });\n\n  return {\n    labelProps,\n    fieldProps,\n    descriptionProps: {\n      id: descriptionId\n    },\n    errorMessageProps: {\n      id: errorMessageId\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useField.main.js.map"}