{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AA6BM,SAAS,0CAAe,KAA2B;IACxD,IAAI,WAAC,OAAO,EAAE,cAAc,SAAS,EAAC,GAAG;IACzC,IAAI,YAAY,CAAA,GAAA,2BAAI;IAEpB,OAAO;QACL,WAAW;YACT,MAAM;QACR;QACA,cAAc,UAAU;YACtB,gEAAgE;YAChE,4EAA4E;YAC5E,mDAAmD;YACnD,IAAI;YACJ,MAAM;QACR,IAAI,CAAC;QACL,YAAY;YACV,MAAM;YACN,cAAc;YACd,mBAAmB,UAAU,YAAY;QAC3C;IACF;AACF", "sources": ["packages/@react-aria/menu/src/useMenuSection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\nimport {ReactNode} from 'react';\nimport {useId} from '@react-aria/utils';\n\nexport interface AriaMenuSectionProps {\n  /** The heading for the section. */\n  heading?: ReactNode,\n  /** An accessibility label for the section. Required if `heading` is not present. */\n  'aria-label'?: string\n}\n\nexport interface MenuSectionAria {\n  /** Props for the wrapper list item. */\n  itemProps: DOMAttributes,\n\n  /** Props for the heading element, if any. */\n  headingProps: DOMAttributes,\n\n  /** Props for the group element. */\n  groupProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a section in a menu.\n * See `useMenu` for more details about menus.\n * @param props - Props for the section.\n */\nexport function useMenuSection(props: AriaMenuSectionProps): MenuSectionAria {\n  let {heading, 'aria-label': ariaLabel} = props;\n  let headingId = useId();\n\n  return {\n    itemProps: {\n      role: 'presentation'\n    },\n    headingProps: heading ? {\n      // Techincally, menus cannot contain headings according to ARIA.\n      // We hide the heading from assistive technology, using role=\"presentation\",\n      // and only use it as a label for the nested group.\n      id: headingId,\n      role: 'presentation'\n    } : {},\n    groupProps: {\n      role: 'group',\n      'aria-label': ariaLabel,\n      'aria-labelledby': heading ? headingId : undefined\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useMenuSection.main.js.map"}