{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;AAqCM,SAAS,0CAAkB,KAA2B,EAAE,KAAuB,EAAE,GAA8B;IACpH,IAAI,QACF,OAAO,oBACP,UAAU,WACV,UAAU,SACX,GAAG;IAEJ,IAAI,gBAAgB,CAAA,GAAA,YAAI;IACxB,IAAI,gBAAC,YAAY,gBAAE,YAAY,EAAC,GAAG,CAAA,GAAA,wBAAgB,EAAE;cAAC;IAAI,GAAG,OAAO;IAEpE,IAAI,YAAY,CAAC;QACf,IAAI,YACF;QAGF,IAAI,YAAY,eAAe,CAAC,EAAE,MAAM,EACtC;QAGF,IAAI,OAAO,IAAI,OAAO,EACpB,OAAQ,EAAE,GAAG;YACX,KAAK;YACL,KAAK;gBACH,sHAAsH;gBACtH,qHAAqH;gBACrH,yHAAyH;gBACzH,2HAA2H;gBAC3H,IAAI,YAAY,eAAe,EAAE,kBAAkB,IACjD;YAEF,cAAc;YAChB,KAAK;gBACH,uEAAuE;gBACvE,IAAI,CAAE,CAAA,yBAAyB,CAAA,GAC7B,EAAE,eAAe;gBAEnB,EAAE,cAAc;gBAChB,MAAM,MAAM,CAAC;gBACb;YACF,KAAK;gBACH,IAAI,CAAE,CAAA,yBAAyB,CAAA,GAC7B,EAAE,eAAe;gBAEnB,EAAE,cAAc;gBAChB,MAAM,MAAM,CAAC;gBACb;YACF;gBACE,oBAAoB;gBACpB,IAAI,yBAAyB,GAC3B,EAAE,mBAAmB;QAE3B;IAEJ;IAEA,IAAI,kBAAkB,CAAA,GAAA,kCAA0B,EAAE,CAAA,GAAA,oDAAW,GAAG;IAChE,IAAI,kBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,mBAAW,EAAE;QAClC,YAAY,cAAc,YAAY;QACtC,0BAA0B,gBAAgB,MAAM,CAAC;QACjD;YACE,MAAM,KAAK;QACb;QACA;YACE,MAAM,IAAI,CAAC;QACb;IACF;IAEA,IAAI,aAA0B;QAC5B,qBAAqB;QACrB,cAAa,CAAC;YACZ,8EAA8E;YAC9E,IAAI,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,cAAc,CAAC,YAAY;gBAC5E,iGAAiG;gBACjG,CAAA,GAAA,4BAAoB,EAAE,EAAE,MAAM;gBAE9B,6DAA6D;gBAC7D,8CAA8C;gBAC9C,MAAM,IAAI,CAAC,EAAE,WAAW,KAAK,YAAY,UAAU;YACrD;QACF;QACA,SAAQ,CAAC;YACP,IAAI,EAAE,WAAW,KAAK,WAAW,CAAC,YAAY;gBAC5C,iGAAiG;gBACjG,CAAA,GAAA,4BAAoB,EAAE,EAAE,MAAM;gBAE9B,MAAM,MAAM;YACd;QACF;IACF;IAEA,6DAA6D;IAC7D,OAAO,aAAa,OAAO;IAE3B,OAAO;QACL,2OAA2O;QAC3O,kBAAkB;YAChB,GAAG,YAAY;YACf,GAAI,YAAY,UAAU,aAAa,cAAc;YACrD,IAAI;uBACJ;QACF;QACA,WAAW;YACT,GAAG,YAAY;YACf,mBAAmB;YACnB,WAAW,MAAM,aAAa,IAAI;YAClC,SAAS,MAAM,KAAK;QACtB;IACF;AACF", "sources": ["packages/@react-aria/menu/src/useMenuTrigger.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaButtonProps} from '@react-types/button';\nimport {AriaMenuOptions} from './useMenu';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusWithoutScrolling, useId} from '@react-aria/utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {MenuTriggerState} from '@react-stately/menu';\nimport {MenuTriggerType} from '@react-types/menu';\nimport {PressProps, useLongPress} from '@react-aria/interactions';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useOverlayTrigger} from '@react-aria/overlays';\n\nexport interface AriaMenuTriggerProps {\n  /** The type of menu that the menu trigger opens. */\n  type?: 'menu' | 'listbox',\n  /** Whether menu trigger is disabled. */\n  isDisabled?: boolean,\n  /** How menu is triggered. */\n  trigger?: MenuTriggerType\n}\n\nexport interface MenuTriggerAria<T> {\n  /** Props for the menu trigger element. */\n  menuTriggerProps: AriaButtonProps,\n\n  /** Props for the menu. */\n  menuProps: AriaMenuOptions<T>\n}\n\n/**\n * Provides the behavior and accessibility implementation for a menu trigger.\n * @param props - Props for the menu trigger.\n * @param state - State for the menu trigger.\n * @param ref - Ref to the HTML element trigger for the menu.\n */\nexport function useMenuTrigger<T>(props: AriaMenuTriggerProps, state: MenuTriggerState, ref: RefObject<Element | null>): MenuTriggerAria<T> {\n  let {\n    type = 'menu',\n    isDisabled,\n    trigger = 'press'\n  } = props;\n\n  let menuTriggerId = useId();\n  let {triggerProps, overlayProps} = useOverlayTrigger({type}, state, ref);\n\n  let onKeyDown = (e) => {\n    if (isDisabled) {\n      return;\n    }\n\n    if (trigger === 'longPress' && !e.altKey) {\n      return;\n    }\n\n    if (ref && ref.current) {\n      switch (e.key) {\n        case 'Enter':\n        case ' ':\n          // React puts listeners on the same root, so even if propagation was stopped, immediate propagation is still possible.\n          // useTypeSelect will handle the spacebar first if it's running, so we don't want to open if it's handled it already.\n          // We use isDefaultPrevented() instead of isPropagationStopped() because createEventHandler stops propagation by default.\n          // And default prevented means that the event was handled by something else (typeahead), so we don't want to open the menu.\n          if (trigger === 'longPress' || e.isDefaultPrevented()) {\n            return;\n          }\n          // fallthrough\n        case 'ArrowDown':\n          // Stop propagation, unless it would already be handled by useKeyboard.\n          if (!('continuePropagation' in e)) {\n            e.stopPropagation();\n          }\n          e.preventDefault();\n          state.toggle('first');\n          break;\n        case 'ArrowUp':\n          if (!('continuePropagation' in e)) {\n            e.stopPropagation();\n          }\n          e.preventDefault();\n          state.toggle('last');\n          break;\n        default:\n          // Allow other keys.\n          if ('continuePropagation' in e) {\n            e.continuePropagation();\n          }\n      }\n    }\n  };\n\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/menu');\n  let {longPressProps} = useLongPress({\n    isDisabled: isDisabled || trigger !== 'longPress',\n    accessibilityDescription: stringFormatter.format('longPressMessage'),\n    onLongPressStart() {\n      state.close();\n    },\n    onLongPress() {\n      state.open('first');\n    }\n  });\n\n  let pressProps: PressProps =  {\n    preventFocusOnPress: true,\n    onPressStart(e) {\n      // For consistency with native, open the menu on mouse/key down, but touch up.\n      if (e.pointerType !== 'touch' && e.pointerType !== 'keyboard' && !isDisabled) {\n        // Ensure trigger has focus before opening the menu so it can be restored by FocusScope on close.\n        focusWithoutScrolling(e.target as FocusableElement);\n\n        // If opened with a screen reader, auto focus the first item.\n        // Otherwise, the menu itself will be focused.\n        state.open(e.pointerType === 'virtual' ? 'first' : null);\n      }\n    },\n    onPress(e) {\n      if (e.pointerType === 'touch' && !isDisabled) {\n        // Ensure trigger has focus before opening the menu so it can be restored by FocusScope on close.\n        focusWithoutScrolling(e.target as FocusableElement);\n\n        state.toggle();\n      }\n    }\n  };\n\n  // omit onPress from triggerProps since we override it above.\n  delete triggerProps.onPress;\n\n  return {\n    // @ts-ignore - TODO we pass out both DOMAttributes AND AriaButtonProps, but useButton will discard the longPress event handlers, it's only through PressResponder magic that this works for RSP and RAC. it does not work in aria examples\n    menuTriggerProps: {\n      ...triggerProps,\n      ...(trigger === 'press' ? pressProps : longPressProps),\n      id: menuTriggerId,\n      onKeyDown\n    },\n    menuProps: {\n      ...overlayProps,\n      'aria-labelledby': menuTriggerId,\n      autoFocus: state.focusStrategy || true,\n      onClose: state.close\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useMenuTrigger.module.js.map"}