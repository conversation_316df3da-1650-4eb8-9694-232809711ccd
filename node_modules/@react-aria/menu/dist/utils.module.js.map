{"mappings": "AAAA;;;;;;;;;;CAUC,GAWM,MAAM,4CAAkD,IAAI", "sources": ["packages/@react-aria/menu/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport {TreeState} from '@react-stately/tree';\n\ninterface MenuData {\n  onClose?: () => void,\n  onAction?: (key: Key) => void,\n  shouldUseVirtualFocus?: boolean\n}\n\nexport const menuData: WeakMap<TreeState<unknown>, MenuData> = new WeakMap<TreeState<unknown>, MenuData>();\n"], "names": [], "version": 3, "file": "utils.module.js.map"}