{"mappings": ";;;;;;;;;;;;;AAiBA,MAAM,kDAA4B;AAClC,MAAM,sCAAgB;AACtB,MAAM,qCAAe;AACrB,MAAM,sCAAgB,KAAK,EAAE,GAAG,IAAI,MAAM;AAMnC,SAAS,0CAAwB,OAAoC;IAC1E,IAAI,WAAC,OAAO,cAAE,UAAU,UAAE,MAAM,cAAE,UAAU,EAAC,GAAG;IAChD,IAAI,iBAAiB,CAAA,GAAA,mBAAK,EAAsC;IAChE,IAAI,cAAc,CAAA,GAAA,mBAAK,EAAuB;IAC9C,IAAI,oBAAoB,CAAA,GAAA,mBAAK,EAAU;IACvC,IAAI,UAAU,CAAA,GAAA,mBAAK,EAA6C;IAChE,IAAI,mBAAmB,CAAA,GAAA,mBAAK,EAA6C;IACzE,IAAI,cAAc,CAAA,GAAA,mBAAK,EAAgC;IACvD,IAAI,+BAA+B,CAAA,GAAA,mBAAK,EAAU;IAClD,IAAI,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qBAAO,EAAE;IAE/D,IAAI,oBAAoB;QACtB,IAAI,WAAW,OAAO,EAAE;YACtB,YAAY,OAAO,GAAG,WAAW,OAAO,CAAC,qBAAqB;YAC9D,YAAY,OAAO,GAAG;QACxB;IACF;IACA,CAAA,GAAA,uCAAgB,EAAE;QAAC,KAAK;QAAY,UAAU;IAAiB;IAE/D,IAAI,QAAQ;QACV,wBAAwB;QACxB,6BAA6B,OAAO,GAAG;QACvC,eAAe,OAAO,GAAG;IAC3B;IAEA,IAAI,WAAW,CAAA,GAAA,mDAAqB;IAEpC,wFAAwF;IACxF,wFAAwF;IACxF,IAAI,gBAAgB,CAAA,GAAA,oCAAa,EAAE,CAAC;QAClC,IAAI,sBACF,EAAE,cAAc;IAEpB;IAEA,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,wBAAwB,QAAQ,OAAO,EACzC,AAAC,QAAQ,OAAO,CAAiB,KAAK,CAAC,aAAa,GAAG;aAEvD,AAAC,QAAQ,OAAO,CAAiB,KAAK,CAAC,aAAa,GAAG;IAE3D,GAAG;QAAC;QAAS;KAAqB;IAElC,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,UAAU,WAAW,OAAO;QAChC,IAAI,OAAO,QAAQ,OAAO;QAE1B,IAAI,cAAc,CAAC,WAAW,CAAC,UAAU,aAAa,aAAa,CAAC,MAAM;YACxE;YACA;QACF;QACA,YAAY,OAAO,GAAG,QAAQ,qBAAqB;QAEnD,IAAI,gBAAgB,CAAC;YACnB,IAAI,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,OACjD;YAGF,IAAI,cAAc,KAAK,GAAG;YAE1B,WAAW;YACX,IAAI,cAAc,kBAAkB,OAAO,GAAG,qCAC5C;YAEF,aAAa,QAAQ,OAAO;YAC5B,aAAa,iBAAiB,OAAO;YAErC,IAAI,EAAC,SAAS,MAAM,EAAE,SAAS,MAAM,EAAC,GAAG;YAEzC,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,eAAe,OAAO,GAAG;oBAAC,GAAG;oBAAQ,GAAG;gBAAM;gBAC9C;YACF;YAEA,IAAI,CAAC,YAAY,OAAO,EACtB;YAGF,IAAI,CAAC,YAAY,OAAO,EACtB,YAAY,OAAO,GAAG,SAAS,YAAY,OAAO,CAAC,KAAK,GAAG,SAAS;YAGtE,oCAAoC;YACpC,IAAI,SAAS,KAAK,qBAAqB,GAAG,IAAI,IAAI,SAAS,KAAK,qBAAqB,GAAG,KAAK,IAAI,SAAS,KAAK,qBAAqB,GAAG,GAAG,IAAI,SAAS,KAAK,qBAAqB,GAAG,MAAM,EAAE;gBAC1L;gBACA;YACF;YAEA;;;;;;MAMA,GACA,IAAI,aAAa,eAAe,OAAO,CAAC,CAAC;YACzC,IAAI,aAAa,eAAe,OAAO,CAAC,CAAC;YACzC,IAAI,aAAa,YAAY,OAAO,KAAK,UAAU,YAAY,OAAO,CAAC,IAAI,GAAG,aAAa,aAAa,YAAY,OAAO,CAAC,KAAK;YACjI,IAAI,WAAW,KAAK,KAAK,CAAC,aAAa,YAAY,OAAO,CAAC,GAAG,EAAE,cAAc;YAC9E,IAAI,cAAc,KAAK,KAAK,CAAC,aAAa,YAAY,OAAO,CAAC,MAAM,EAAE,cAAc;YACpF,IAAI,eAAe,KAAK,KAAK,CAAC,aAAa,QAAS,YAAY,OAAO,KAAK,SAAS,CAAE,CAAA,SAAS,UAAS,IAAK,SAAS;YACvH,IAAI,yBAAyB,eAAe,YAAY,eAAe;YAEvE,6BAA6B,OAAO,GAAG,yBACrC,KAAK,GAAG,CAAC,6BAA6B,OAAO,GAAG,GAAG,mDACnD,KAAK,GAAG,CAAC,6BAA6B,OAAO,GAAG,GAAG;YAErD,IAAI,6BAA6B,OAAO,IAAI,iDAC1C,wBAAwB;iBAExB,wBAAwB;YAG1B,kBAAkB,OAAO,GAAG;YAC5B,eAAe,OAAO,GAAG;gBAAC,GAAG;gBAAQ,GAAG;YAAM;YAE9C,qHAAqH;YACrH,IAAI,wBACF,QAAQ,OAAO,GAAG,WAAW;gBAC3B;gBACA,iBAAiB,OAAO,GAAG,WAAW;oBACpC,yDAAyD;oBACzD,sDAAsD;oBACtD,IAAI,SAAS,SAAS,gBAAgB,CAAC,QAAQ;oBAC/C,IAAI,UAAU,KAAK,QAAQ,CAAC,SAC1B,OAAO,aAAa,CAAC,IAAI,aAAa,eAAe;wBAAC,SAAS;wBAAM,YAAY;oBAAI;gBAEzF,GAAG;YACL,GAAG;QAEP;QAEA,OAAO,gBAAgB,CAAC,eAAe;QAEvC,kEAAkE;QAClE,gEAAgE;QAChE,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAC3B,OAAO,gBAAgB,CAAC,eAAe,eAAe;QAGxD,OAAO;YACL,OAAO,mBAAmB,CAAC,eAAe;YAC1C,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAC3B,OAAO,mBAAmB,CAAC,eAAe,eAAe;YAE3D,aAAa,QAAQ,OAAO;YAC5B,aAAa,iBAAiB,OAAO;YACrC,6BAA6B,OAAO,GAAG;QACzC;IAEF,GAAG;QAAC;QAAY;QAAQ;QAAS;QAAU;QAAyB;QAAe;KAAW;AAChG", "sources": ["packages/@react-aria/menu/src/useSafelyMouseToSubmenu.ts"], "sourcesContent": ["\nimport {RefObject} from '@react-types/shared';\nimport {useEffect, useRef, useState} from 'react';\nimport {useEffectEvent, useResizeObserver} from '@react-aria/utils';\nimport {useInteractionModality} from '@react-aria/interactions';\n\ninterface SafelyMouseToSubmenuOptions {\n  /** Ref for the parent menu. */\n  menuRef: RefObject<Element | null>,\n  /** Ref for the submenu. */\n  submenuRef: RefObject<Element | null>,\n  /** Whether the submenu is open. */\n  isOpen: boolean,\n  /** Whether this feature is disabled. */\n  isDisabled?: boolean\n}\n\nconst ALLOWED_INVALID_MOVEMENTS = 2;\nconst THROTTLE_TIME = 50;\nconst TIMEOUT_TIME = 1000;\nconst ANGLE_PADDING = Math.PI / 12; // 15°\n\n/**\n * Allows the user to move their pointer to the submenu without it closing when their mouse leaves the trigger element.\n * Prevents pointer events from going to the underlying menu if the user is moving their pointer towards the sub-menu.\n */\nexport function useSafelyMouseToSubmenu(options: SafelyMouseToSubmenuOptions): void {\n  let {menuRef, submenuRef, isOpen, isDisabled} = options;\n  let prevPointerPos = useRef<{x: number, y: number} | undefined>(undefined);\n  let submenuRect = useRef<DOMRect | undefined>(undefined);\n  let lastProcessedTime = useRef<number>(0);\n  let timeout = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);\n  let autoCloseTimeout = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);\n  let submenuSide = useRef<'left' | 'right' | undefined>(undefined);\n  let movementsTowardsSubmenuCount = useRef<number>(2);\n  let [preventPointerEvents, setPreventPointerEvents] = useState(false);\n\n  let updateSubmenuRect = () => {\n    if (submenuRef.current) {\n      submenuRect.current = submenuRef.current.getBoundingClientRect();\n      submenuSide.current = undefined;\n    }\n  };\n  useResizeObserver({ref: submenuRef, onResize: updateSubmenuRect});\n\n  let reset = () => {\n    setPreventPointerEvents(false);\n    movementsTowardsSubmenuCount.current = ALLOWED_INVALID_MOVEMENTS;\n    prevPointerPos.current = undefined;\n  };\n\n  let modality = useInteractionModality();\n\n  // Prevent mouse down over safe triangle. Clicking while pointer-events: none is applied\n  // will cause focus to move unexpectedly since it will go to an element behind the menu.\n  let onPointerDown = useEffectEvent((e: PointerEvent) => {\n    if (preventPointerEvents) {\n      e.preventDefault();\n    }\n  });\n\n  useEffect(() => {\n    if (preventPointerEvents && menuRef.current) {\n      (menuRef.current as HTMLElement).style.pointerEvents = 'none';\n    } else {\n      (menuRef.current as HTMLElement).style.pointerEvents = '';\n    }\n  }, [menuRef, preventPointerEvents]);\n\n  useEffect(() => {\n    let submenu = submenuRef.current;\n    let menu = menuRef.current;\n\n    if (isDisabled || !submenu || !isOpen || modality !== 'pointer' || !menu) {\n      reset();\n      return;\n    }\n    submenuRect.current = submenu.getBoundingClientRect();\n\n    let onPointerMove = (e: PointerEvent) => {\n      if (e.pointerType === 'touch' || e.pointerType === 'pen') {\n        return;\n      }\n\n      let currentTime = Date.now();\n\n      // Throttle\n      if (currentTime - lastProcessedTime.current < THROTTLE_TIME) {\n        return;\n      }\n      clearTimeout(timeout.current);\n      clearTimeout(autoCloseTimeout.current);\n\n      let {clientX: mouseX, clientY: mouseY} = e;\n\n      if (!prevPointerPos.current) {\n        prevPointerPos.current = {x: mouseX, y: mouseY};\n        return;\n      }\n\n      if (!submenuRect.current) {\n        return;\n      }\n\n      if (!submenuSide.current) {\n        submenuSide.current = mouseX > submenuRect.current.right ? 'left' : 'right';\n      }\n\n      // Pointer is outside of parent menu\n      if (mouseX < menu.getBoundingClientRect().left || mouseX > menu.getBoundingClientRect().right || mouseY < menu.getBoundingClientRect().top || mouseY > menu.getBoundingClientRect().bottom) {\n        reset();\n        return;\n      }\n\n      /* Check if pointer is moving towards submenu.\n        Uses the 2-argument arctangent (https://en.wikipedia.org/wiki/Atan2) to calculate:\n          - angle between previous pointer and top of submenu\n          - angle between previous pointer and bottom of submenu\n          - angle between previous pointer and current pointer (delta)\n        If the pointer delta angle value is between the top and bottom angle values, we know the pointer is moving towards the submenu.\n      */\n      let prevMouseX = prevPointerPos.current.x;\n      let prevMouseY = prevPointerPos.current.y;\n      let toSubmenuX = submenuSide.current === 'right' ? submenuRect.current.left - prevMouseX : prevMouseX - submenuRect.current.right;\n      let angleTop = Math.atan2(prevMouseY - submenuRect.current.top, toSubmenuX) + ANGLE_PADDING;\n      let angleBottom = Math.atan2(prevMouseY - submenuRect.current.bottom, toSubmenuX) - ANGLE_PADDING;\n      let anglePointer = Math.atan2(prevMouseY - mouseY, (submenuSide.current === 'left' ? -(mouseX - prevMouseX) : mouseX - prevMouseX));\n      let isMovingTowardsSubmenu = anglePointer < angleTop && anglePointer > angleBottom;\n\n      movementsTowardsSubmenuCount.current = isMovingTowardsSubmenu ?\n        Math.min(movementsTowardsSubmenuCount.current + 1, ALLOWED_INVALID_MOVEMENTS) :\n        Math.max(movementsTowardsSubmenuCount.current - 1, 0);\n\n      if (movementsTowardsSubmenuCount.current >= ALLOWED_INVALID_MOVEMENTS) {\n        setPreventPointerEvents(true);\n      } else {\n        setPreventPointerEvents(false);\n      }\n\n      lastProcessedTime.current = currentTime;\n      prevPointerPos.current = {x: mouseX, y: mouseY};\n\n      // If the pointer is moving towards the submenu, start a timeout to close if no other movements are made after 500ms.\n      if (isMovingTowardsSubmenu) {\n        timeout.current = setTimeout(() => {\n          reset();\n          autoCloseTimeout.current = setTimeout(() => {\n            // Fire a pointerover event to trigger the menu to close.\n            // Wait until pointer-events:none is no longer applied\n            let target = document.elementFromPoint(mouseX, mouseY);\n            if (target && menu.contains(target)) {\n              target.dispatchEvent(new PointerEvent('pointerover', {bubbles: true, cancelable: true}));\n            }\n          }, 100);\n        }, TIMEOUT_TIME);\n      }\n    };\n\n    window.addEventListener('pointermove', onPointerMove);\n\n    // Prevent pointer down over the safe triangle. See above comment.\n    // Do not enable in tests, because JSDom doesn't do hit testing.\n    if (process.env.NODE_ENV !== 'test') {\n      window.addEventListener('pointerdown', onPointerDown, true);\n    }\n\n    return () => {\n      window.removeEventListener('pointermove', onPointerMove);\n      if (process.env.NODE_ENV !== 'test') {\n        window.removeEventListener('pointerdown', onPointerDown, true);\n      }\n      clearTimeout(timeout.current);\n      clearTimeout(autoCloseTimeout.current);\n      movementsTowardsSubmenuCount.current = ALLOWED_INVALID_MOVEMENTS;\n    };\n\n  }, [isDisabled, isOpen, menuRef, modality, setPreventPointerEvents, onPointerDown, submenuRef]);\n}\n"], "names": [], "version": 3, "file": "useSafelyMouseToSubmenu.main.js.map"}