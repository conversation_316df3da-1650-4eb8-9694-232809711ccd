{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAkCM,SAAS,0CAAW,KAAyB,EAAE,KAAmB,EAAE,GAAkC;IAC3G,IAAI,mBACF,kBAAkB,iBAClB,SAAS,WACT,OAAO,EACP,GAAG,YACJ,GAAG;IAEJ,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,cAChF,QAAQ,IAAI,CAAC;IAGf,IAAI,WAAW,CAAA,GAAA,oCAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,2CAAgB,EAAE;QAClC,GAAG,UAAU;aACb;QACA,kBAAkB,MAAM,gBAAgB;QACxC,YAAY,MAAM,UAAU;QAC5B,cAAc,MAAM,YAAY;yBAChC;QACA,cAAc;IAChB;IAEA,CAAA,GAAA,kCAAO,EAAE,GAAG,CAAC,OAAO;QAClB,SAAS,MAAM,OAAO;QACtB,UAAU,MAAM,QAAQ;QACxB,uBAAuB,MAAM,qBAAqB;IACpD;IAEA,OAAO;QACL,WAAW,CAAA,GAAA,gCAAS,EAAE,UAAU;uBAAC;qBAAW;QAAO,GAAG;YACpD,MAAM;YACN,GAAG,SAAS;YACZ,WAAW,CAAC;oBAGR;gBAFF,gGAAgG;gBAChG,IAAI,EAAE,GAAG,KAAK,YAAY,MAAM,qBAAqB,GACnD,uBAAA,UAAU,SAAS,cAAnB,2CAAA,0BAAA,WAAsB;YAE1B;QACF;IACF;AACF", "sources": ["packages/@react-aria/menu/src/useMenu.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaMenuProps} from '@react-types/menu';\nimport {DOMAttributes, KeyboardDelegate, KeyboardEvents, RefObject} from '@react-types/shared';\nimport {filterDOMProps, mergeProps} from '@react-aria/utils';\nimport {menuData} from './utils';\nimport {TreeState} from '@react-stately/tree';\nimport {useSelectableList} from '@react-aria/selection';\n\nexport interface MenuAria {\n  /** Props for the menu element. */\n  menuProps: DOMAttributes\n}\n\nexport interface AriaMenuOptions<T> extends Omit<AriaMenuProps<T>, 'children'>, KeyboardEvents {\n  /** Whether the menu uses virtual scrolling. */\n  isVirtualized?: boolean,\n  /**\n   * An optional keyboard delegate implementation for type to select,\n   * to override the default.\n   */\n  keyboardDelegate?: KeyboardDelegate,\n  /**\n   * Whether the menu items should use virtual focus instead of being focused directly.\n   */\n  shouldUseVirtualFocus?: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a menu component.\n * A menu displays a list of actions or options that a user can choose.\n * @param props - Props for the menu.\n * @param state - State for the menu, as returned by `useListState`.\n */\nexport function useMenu<T>(props: AriaMenuOptions<T>, state: TreeState<T>, ref: RefObject<HTMLElement | null>): MenuAria {\n  let {\n    shouldFocusWrap = true,\n    onKeyDown,\n    onKeyUp,\n    ...otherProps\n  } = props;\n\n  if (!props['aria-label'] && !props['aria-labelledby'] && process.env.NODE_ENV !== 'production') {\n    console.warn('An aria-label or aria-labelledby prop is required for accessibility.');\n  }\n\n  let domProps = filterDOMProps(props, {labelable: true});\n  let {listProps} = useSelectableList({\n    ...otherProps,\n    ref,\n    selectionManager: state.selectionManager,\n    collection: state.collection,\n    disabledKeys: state.disabledKeys,\n    shouldFocusWrap,\n    linkBehavior: 'override'\n  });\n\n  menuData.set(state, {\n    onClose: props.onClose,\n    onAction: props.onAction,\n    shouldUseVirtualFocus: props.shouldUseVirtualFocus\n  });\n\n  return {\n    menuProps: mergeProps(domProps, {onKeyDown, onKeyUp}, {\n      role: 'menu',\n      ...listProps,\n      onKeyDown: (e) => {\n        // don't clear the menu selected keys if the user is presses escape since escape closes the menu\n        if (e.key !== 'Escape' || props.shouldUseVirtualFocus) {\n          listProps.onKeyDown?.(e);\n        }\n      }\n    })\n  };\n}\n"], "names": [], "version": 3, "file": "useMenu.main.js.map"}