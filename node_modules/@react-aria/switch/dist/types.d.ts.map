{"mappings": ";;;;AAkBA;IACE,2CAA2C;IAC3C,UAAU,EAAE,oBAAoB,gBAAgB,CAAC,CAAC;IAClD,mCAAmC;IACnC,UAAU,EAAE,oBAAoB,gBAAgB,CAAC,CAAC;IAClD,sCAAsC;IACtC,UAAU,EAAE,OAAO,CAAC;IACpB,gDAAgD;IAChD,SAAS,EAAE,OAAO,CAAC;IACnB,sCAAsC;IACtC,UAAU,EAAE,OAAO,CAAC;IACpB,uCAAuC;IACvC,UAAU,EAAE,OAAO,CAAA;CACpB;AAED;;;;;;GAMG;AACH,0BAA0B,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,UAAU,CAezH;AC1CD,YAAY,EAAC,eAAe,EAAC,MAAM,qBAAqB,CAAC", "sources": ["packages/@react-aria/switch/src/packages/@react-aria/switch/src/useSwitch.ts", "packages/@react-aria/switch/src/packages/@react-aria/switch/src/index.ts", "packages/@react-aria/switch/src/index.ts"], "sourcesContent": [null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {useSwitch} from './useSwitch';\n\nexport type {AriaSwitchProps} from '@react-types/switch';\nexport type {SwitchAria} from './useSwitch';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}