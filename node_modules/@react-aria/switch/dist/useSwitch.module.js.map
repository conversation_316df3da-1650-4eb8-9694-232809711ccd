{"mappings": ";;AAAA;;;;;;;;;;CAUC;AA8BM,SAAS,0CAAU,KAAsB,EAAE,KAAkB,EAAE,GAAuC;IAC3G,IAAI,cAAC,UAAU,cAAE,UAAU,cAAE,UAAU,aAAE,SAAS,cAAE,UAAU,cAAE,UAAU,EAAC,GAAG,CAAA,GAAA,gBAAQ,EAAE,OAAO,OAAO;IAEtG,OAAO;oBACL;QACA,YAAY;YACV,GAAG,UAAU;YACb,MAAM;YACN,SAAS;QACX;oBACA;mBACA;oBACA;oBACA;IACF;AACF", "sources": ["packages/@react-aria/switch/src/useSwitch.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaSwitchProps} from '@react-types/switch';\nimport {InputHTMLAttributes, LabelHTMLAttributes} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {ToggleState} from '@react-stately/toggle';\nimport {useToggle} from '@react-aria/toggle';\n\nexport interface SwitchAria {\n  /** Props for the label wrapper element. */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Whether the switch is selected. */\n  isSelected: boolean,\n  /** Whether the switch is in a pressed state. */\n  isPressed: boolean,\n  /** Whether the switch is disabled. */\n  isDisabled: boolean,\n  /** Whether the switch is read only. */\n  isReadOnly: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a switch component.\n * A switch is similar to a checkbox, but represents on/off values as opposed to selection.\n * @param props - Props for the switch.\n * @param state - State for the switch, as returned by `useToggleState`.\n * @param ref - Ref to the HTML input element.\n */\nexport function useSwitch(props: AriaSwitchProps, state: ToggleState, ref: RefObject<HTMLInputElement | null>): SwitchAria {\n  let {labelProps, inputProps, isSelected, isPressed, isDisabled, isReadOnly} = useToggle(props, state, ref);\n\n  return {\n    labelProps,\n    inputProps: {\n      ...inputProps,\n      role: 'switch',\n      checked: isSelected\n    },\n    isSelected,\n    isPressed,\n    isDisabled,\n    isReadOnly\n  };\n}\n"], "names": [], "version": 3, "file": "useSwitch.module.js.map"}