{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAoBM,SAAS,0CAAoB,GAA8B,EAAE,OAAqC;IACvG,IAAI,aAAa,oBAAA,8BAAA,QAAS,UAAU;IACpC,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,eAAO,EAAE;IAEvD,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,CAAA,gBAAA,0BAAA,IAAK,OAAO,KAAI,CAAC,YAAY;YAC/B,IAAI,SAAS;gBACX,IAAI,IAAI,OAAO,EAAE;oBACf,IAAI,SAAS,CAAA,GAAA,yCAAqB,EAAE,IAAI,OAAO,EAAE;wBAAC,UAAU;oBAAI;oBAChE,oBAAoB,CAAC,CAAC,OAAO,QAAQ;gBACvC;YACF;YAEA;YAEA,qFAAqF;YACrF,IAAI,WAAW,IAAI,iBAAiB;YACpC,SAAS,OAAO,CAAC,IAAI,OAAO,EAAE;gBAC5B,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,iBAAiB;oBAAC;oBAAY;iBAAW;YAC3C;YAEA,OAAO;gBACL,qFAAqF;gBACrF,wFAAwF;gBACxF,4FAA4F;gBAC5F,yFAAyF;gBACzF,SAAS,UAAU;YACrB;QACF;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B", "sources": ["packages/@react-aria/focus/src/useHasTabbableChild.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getFocusableTreeWalker} from './FocusScope';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from '@react-aria/utils';\nimport {useState} from 'react';\n\ninterface AriaHasTabbableChildOptions {\n  isDisabled?: boolean\n}\n\n// This was created for a special empty case of a component that can have child or\n// be empty, like Collection/Virtualizer/Table/ListView/etc. When these components\n// are empty they can have a message with a tabbable element, which is like them\n// being not empty, when it comes to focus and tab order.\n\n/**\n * Returns whether an element has a tabbable child, and updates as children change.\n * @private\n */\nexport function useHasTabbableChild(ref: RefObject<Element | null>, options?: AriaHasTabbableChildOptions): boolean {\n  let isDisabled = options?.isDisabled;\n  let [hasTabbableChild, setHasTabbableChild] = useState(false);\n\n  useLayoutEffect(() => {\n    if (ref?.current && !isDisabled) {\n      let update = () => {\n        if (ref.current) {\n          let walker = getFocusableTreeWalker(ref.current, {tabbable: true});\n          setHasTabbableChild(!!walker.nextNode());\n        }\n      };\n\n      update();\n\n      // Update when new elements are inserted, or the tabIndex/disabled attribute updates.\n      let observer = new MutationObserver(update);\n      observer.observe(ref.current, {\n        subtree: true,\n        childList: true,\n        attributes: true,\n        attributeFilter: ['tabIndex', 'disabled']\n      });\n\n      return () => {\n        // Disconnect mutation observer when a React update occurs on the top-level component\n        // so we update synchronously after re-rendering. Otherwise React will emit act warnings\n        // in tests since mutation observers fire asynchronously. The mutation observer is necessary\n        // so we also update if a child component re-renders and adds/removes something tabbable.\n        observer.disconnect();\n      };\n    }\n  });\n\n  return isDisabled ? false : hasTabbableChild;\n}\n"], "names": [], "version": 3, "file": "useHasTabbableChild.module.js.map"}