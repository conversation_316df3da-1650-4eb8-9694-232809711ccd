{"mappings": ";;;;;AAoCO,SAAS,0CAAa,QAA4B,CAAC,CAAC;IACzD,IAAI,aACF,YAAY,oBACZ,WAAW,UACX,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ,CAAA,GAAA,aAAK,EAAE;QACjB,WAAW;QACX,gBAAgB,aAAa,CAAA,GAAA,qBAAa;IAC5C;IACA,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,GAAA,eAAO,EAAE;IACvC,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,GAAA,eAAO,EAAE,IAAM,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc;IAEnH,IAAI,cAAc,CAAA,GAAA,kBAAU,EAAE,IAAM,gBAAgB,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc,GAAG,EAAE;IAEhH,IAAI,gBAAgB,CAAA,GAAA,kBAAU,EAAE,CAAA;QAC9B,MAAM,OAAO,CAAC,SAAS,GAAG;QAC1B,WAAW;QACX;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,8BAAsB,EAAE,CAAC;QACvB,MAAM,OAAO,CAAC,cAAc,GAAG;QAC/B;IACF,GAAG,EAAE,EAAE;qBAAC;IAAW;IAEnB,IAAI,cAAC,UAAU,EAAC,GAAG,CAAA,GAAA,eAAO,EAAE;QAC1B,YAAY;uBACZ;IACF;IAEA,IAAI,oBAAC,gBAAgB,EAAC,GAAG,CAAA,GAAA,qBAAa,EAAE;QACtC,YAAY,CAAC;QACb,qBAAqB;IACvB;IAEA,OAAO;mBACL;QACA,gBAAgB;QAChB,YAAY,SAAS,mBAAmB;IAC1C;AACF", "sources": ["packages/@react-aria/focus/src/useFocusRing.ts"], "sourcesContent": ["import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n"], "names": [], "version": 3, "file": "useFocusRing.module.js.map"}