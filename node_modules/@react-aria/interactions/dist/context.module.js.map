{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAWM,MAAM,4CAA+D,CAAA,GAAA,YAAI,EAAE,aAAa,CAAyB;IAAC,UAAU,KAAO;AAAC;AAC3I,0CAAsB,WAAW,GAAG", "sources": ["packages/@react-aria/interactions/src/context.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {PressProps} from './usePress';\nimport React, {MutableRefObject} from 'react';\n\ninterface IPressResponderContext extends PressProps {\n  register(): void,\n  ref?: MutableRefObject<FocusableElement>\n}\n\nexport const PressResponderContext: React.Context<IPressResponderContext> = React.createContext<IPressResponderContext>({register: () => {}});\nPressResponderContext.displayName = 'PressResponderContext';\n"], "names": [], "version": 3, "file": "context.module.js.map"}