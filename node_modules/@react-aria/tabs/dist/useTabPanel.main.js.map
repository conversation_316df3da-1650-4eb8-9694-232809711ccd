{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAmBM,SAAS,0CAAe,KAAwB,EAAE,KAA6B,EAAE,GAA8B;IACpH,qFAAqF;IACrF,2FAA2F;IAC3F,uBAAuB;IACvB,IAAI,WAAW,CAAA,GAAA,yCAAkB,EAAE,OAAO,YAAY;QAEzB;IAA7B,MAAM,KAAK,CAAA,GAAA,oCAAS,EAAE,OAAO,CAAA,YAAA,MAAM,EAAE,cAAR,uBAAA,YAAY,kBAAA,4BAAA,MAAO,WAAW,EAAE;IAC7D,MAAM,gBAAgB,CAAA,GAAA,+BAAQ,EAAE;QAAC,GAAG,KAAK;YAAE;QAAI,mBAAmB,CAAA,GAAA,oCAAS,EAAE,OAAO,kBAAA,4BAAA,MAAO,WAAW,EAAE;IAAM;IAE9G,OAAO;QACL,eAAe,CAAA,GAAA,gCAAS,EAAE,eAAe;sBACvC;YACA,MAAM;YACN,oBAAoB,KAAK,CAAC,mBAAmB;YAC7C,gBAAgB,KAAK,CAAC,eAAe;QACvC;IACF;AACF", "sources": ["packages/@react-aria/tabs/src/useTabPanel.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTabPanelProps} from '@react-types/tabs';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {generateId} from './utils';\nimport {mergeProps, useLabels} from '@react-aria/utils';\nimport {TabListState} from '@react-stately/tabs';\nimport {useHasTabbableChild} from '@react-aria/focus';\n\nexport interface TabPanelAria {\n  /** Props for the tab panel element. */\n  tabPanelProps: DOMAttributes\n}\n\n\n/**\n * Provides the behavior and accessibility implementation for a tab panel. A tab panel is a container for\n * the contents of a tab, and is shown when the tab is selected.\n */\nexport function useTabPanel<T>(props: AriaTabPanelProps, state: TabListState<T> | null, ref: RefObject<Element | null>): TabPanelAria {\n  // The tabpanel should have tabIndex=0 when there are no tabbable elements within it.\n  // Otherwise, tabbing from the focused tab should go directly to the first tabbable element\n  // within the tabpanel.\n  let tabIndex = useHasTabbableChild(ref) ? undefined : 0;\n\n  const id = generateId(state, props.id ?? state?.selectedKey, 'tabpanel');\n  const tabPanelProps = useLabels({...props, id, 'aria-labelledby': generateId(state, state?.selectedKey, 'tab')});\n\n  return {\n    tabPanelProps: mergeProps(tabPanelProps, {\n      tabIndex,\n      role: 'tabpanel',\n      'aria-describedby': props['aria-describedby'],\n      'aria-details': props['aria-details']\n    })\n  };\n}\n"], "names": [], "version": 3, "file": "useTabPanel.main.js.map"}