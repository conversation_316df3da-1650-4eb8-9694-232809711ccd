{"mappings": ";;;;;;;AAAA;;;;;;;;;;AAUA;;;;;;AAuBO,SAAS,0CAAc,KAA4B,EAAE,KAAsB,EAAE,GAAkC;IACpH,IAAI,eACF,cAAc,kCACd,qBAAqB,aACtB,GAAG;IACJ,IAAI,cACF,UAAU,EACV,kBAAkB,OAAO,gBACzB,YAAY,EACb,GAAG;IACJ,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAC1B,IAAI,WAAW,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,CAAA,GAAA,yCAAmB,EAClD,YACA,WACA,aACA,eAAe;QAAC;QAAY;QAAc;QAAa;KAAU;IAEnE,IAAI,mBAAC,eAAe,EAAC,GAAG,CAAA,GAAA,8BAAsB,EAAE;aAC9C;QACA,kBAAkB;QAClB,kBAAkB;QAClB,eAAe,uBAAuB;QACtC,wBAAwB;QACxB,WAAW;QACX,cAAc;IAChB;IAEA,+BAA+B;IAC/B,IAAI,SAAS,CAAA,GAAA,YAAI;IACjB,CAAA,GAAA,yCAAM,EAAE,GAAG,CAAC,OAAO;IAEnB,IAAI,oBAAoB,CAAA,GAAA,gBAAQ,EAAE;QAAC,GAAG,KAAK;QAAE,IAAI;IAAM;IAEvD,OAAO;QACL,cAAc;YACZ,GAAG,CAAA,GAAA,iBAAS,EAAE,iBAAiB,kBAAkB;YACjD,MAAM;YACN,oBAAoB;YACpB,UAAU;QACZ;IACF;AACF", "sources": ["packages/@react-aria/tabs/src/useTabList.ts"], "sourcesContent": ["/*\n* Copyright 2020 Adobe. All rights reserved.\n* This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License. You may obtain a copy\n* of the License at http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software distributed under\n* the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n* OF ANY KIND, either express or implied. See the License for the specific language\n* governing permissions and limitations under the License.\n*/\n\nimport {AriaTabListProps} from '@react-types/tabs';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {mergeProps, useId, useLabels} from '@react-aria/utils';\nimport {TabListState} from '@react-stately/tabs';\nimport {tabsIds} from './utils';\nimport {TabsKeyboardDelegate} from './TabsKeyboardDelegate';\nimport {useLocale} from '@react-aria/i18n';\nimport {useMemo} from 'react';\nimport {useSelectableCollection} from '@react-aria/selection';\n\nexport interface AriaTabListOptions<T> extends Omit<AriaTabListProps<T>, 'children'> {}\n\nexport interface TabListAria {\n  /** Props for the tablist container. */\n  tabListProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a tab list.\n * Tabs organize content into multiple sections and allow users to navigate between them.\n */\nexport function useTabList<T>(props: AriaTabListOptions<T>, state: TabListState<T>, ref: RefObject<HTMLElement | null>): TabListAria {\n  let {\n    orientation = 'horizontal',\n    keyboardActivation = 'automatic'\n  } = props;\n  let {\n    collection,\n    selectionManager: manager,\n    disabledKeys\n  } = state;\n  let {direction} = useLocale();\n  let delegate = useMemo(() => new TabsKeyboardDelegate(\n    collection,\n    direction,\n    orientation,\n    disabledKeys), [collection, disabledKeys, orientation, direction]);\n\n  let {collectionProps} = useSelectableCollection({\n    ref,\n    selectionManager: manager,\n    keyboardDelegate: delegate,\n    selectOnFocus: keyboardActivation === 'automatic',\n    disallowEmptySelection: true,\n    scrollRef: ref,\n    linkBehavior: 'selection'\n  });\n\n  // Compute base id for all tabs\n  let tabsId = useId();\n  tabsIds.set(state, tabsId);\n\n  let tabListLabelProps = useLabels({...props, id: tabsId});\n\n  return {\n    tabListProps: {\n      ...mergeProps(collectionProps, tabListLabelProps),\n      role: 'tablist',\n      'aria-orientation': orientation,\n      tabIndex: undefined\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useTabList.module.js.map"}