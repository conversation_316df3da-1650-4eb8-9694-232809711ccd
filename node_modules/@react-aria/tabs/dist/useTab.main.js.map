{"mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AAyBM,SAAS,0CACd,KAAmB,EACnB,KAAsB,EACtB,GAAuC;IAEvC,IAAI,OAAC,GAAG,EAAE,YAAY,aAAa,yBAAE,qBAAqB,EAAC,GAAG;IAC9D,IAAI,EAAC,kBAAkB,OAAO,eAAE,WAAW,EAAC,GAAG;IAE/C,IAAI,aAAa,QAAQ;IAEzB,IAAI,aAAa,iBAAiB,MAAM,UAAU,IAAI,MAAM,gBAAgB,CAAC,UAAU,CAAC;IACxF,IAAI,aAAC,SAAS,aAAE,SAAS,EAAC,GAAG,CAAA,GAAA,2CAAgB,EAAE;QAC7C,kBAAkB;aAClB;aACA;oBACA;+BACA;QACA,cAAc;IAChB;IAEA,IAAI,QAAQ,CAAA,GAAA,oCAAS,EAAE,OAAO,KAAK;IACnC,IAAI,aAAa,CAAA,GAAA,oCAAS,EAAE,OAAO,KAAK;IACxC,IAAI,YAAC,QAAQ,EAAC,GAAG;IAEjB,IAAI,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC;IACpC,IAAI,WAAW,CAAA,GAAA,oCAAa,EAAE,iBAAA,2BAAA,KAAM,KAAK,EAAE;QAAC,WAAW;IAAI;IAC3D,OAAO,SAAS,EAAE;IAClB,IAAI,YAAY,CAAA,GAAA,kCAAW,EAAE,iBAAA,2BAAA,KAAM,KAAK;IACxC,IAAI,kBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,kCAAW,EAAE;oBAClC;IACF,GAAG;IAEH,OAAO;QACL,UAAU,CAAA,GAAA,gCAAS,EAAE,UAAU,gBAAgB,WAAW,WAAW;YACnE,IAAI;YACJ,iBAAiB;YACjB,iBAAiB,cAAc;YAC/B,iBAAiB,aAAa,aAAa;YAC3C,UAAU,aAAa,YAAY;YACnC,MAAM;QACR;oBACA;oBACA;mBACA;IACF;AACF", "sources": ["packages/@react-aria/tabs/src/useTab.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTabProps} from '@react-types/tabs';\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {filterDOMProps, mergeProps, useLinkProps} from '@react-aria/utils';\nimport {generateId} from './utils';\nimport {TabListState} from '@react-stately/tabs';\nimport {useFocusable} from '@react-aria/focus';\nimport {useSelectableItem} from '@react-aria/selection';\n\nexport interface TabAria {\n  /** Props for the tab element. */\n  tabProps: DOMAttributes,\n  /** Whether the tab is currently selected. */\n  isSelected: boolean,\n  /** Whether the tab is disabled. */\n  isDisabled: boolean,\n  /** Whether the tab is currently in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a tab.\n * When selected, the associated tab panel is shown.\n */\nexport function useTab<T>(\n  props: AriaTabProps,\n  state: TabListState<T>,\n  ref: RefObject<FocusableElement | null>\n): TabAria {\n  let {key, isDisabled: propsDisabled, shouldSelectOnPressUp} = props;\n  let {selectionManager: manager, selectedKey} = state;\n\n  let isSelected = key === selectedKey;\n\n  let isDisabled = propsDisabled || state.isDisabled || state.selectionManager.isDisabled(key);\n  let {itemProps, isPressed} = useSelectableItem({\n    selectionManager: manager,\n    key,\n    ref,\n    isDisabled,\n    shouldSelectOnPressUp,\n    linkBehavior: 'selection'\n  });\n\n  let tabId = generateId(state, key, 'tab');\n  let tabPanelId = generateId(state, key, 'tabpanel');\n  let {tabIndex} = itemProps;\n\n  let item = state.collection.getItem(key);\n  let domProps = filterDOMProps(item?.props, {labelable: true});\n  delete domProps.id;\n  let linkProps = useLinkProps(item?.props);\n  let {focusableProps} = useFocusable({\n    isDisabled\n  }, ref);\n\n  return {\n    tabProps: mergeProps(domProps, focusableProps, linkProps, itemProps, {\n      id: tabId,\n      'aria-selected': isSelected,\n      'aria-disabled': isDisabled || undefined,\n      'aria-controls': isSelected ? tabPanelId : undefined,\n      tabIndex: isDisabled ? undefined : tabIndex,\n      role: 'tab'\n    }),\n    isSelected,\n    isDisabled,\n    isPressed\n  };\n}\n"], "names": [], "version": 3, "file": "useTab.main.js.map"}