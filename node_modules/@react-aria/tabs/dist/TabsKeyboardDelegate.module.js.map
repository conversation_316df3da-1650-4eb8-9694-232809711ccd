{"mappings": "AAAA;;;;;;;;;;CAUC,GAIM,MAAM;IAaX,aAAa,GAAQ,EAAc;QACjC,IAAI,IAAI,CAAC,aAAa,EACpB,OAAO,IAAI,CAAC,UAAU,CAAC;QAEzB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,cAAc,GAAQ,EAAc;QAClC,IAAI,IAAI,CAAC,aAAa,EACpB,OAAO,IAAI,CAAC,cAAc,CAAC;QAE7B,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB;IAGQ,WAAW,GAAQ,EAAE;YACY,gCAAA;QAAvC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAC,2BAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAxB,gDAAA,iCAAA,yBAA8B,KAAK,cAAnC,qDAAA,+BAAqC,UAAU;IACxF;IAEA,cAA0B;QACxB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;QACrC,IAAI,OAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,MACjC,MAAM,IAAI,CAAC,UAAU,CAAC;QAExB,OAAO;IACT;IAEA,aAAyB;QACvB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU;QACpC,IAAI,OAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,MACjC,MAAM,IAAI,CAAC,cAAc,CAAC;QAE5B,OAAO;IACT;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,IAAI,CAAC,YAAY,EACnB,OAAO;QAET,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,IAAI,CAAC,YAAY,EACnB,OAAO;QAET,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB;IAEA,WAAW,QAAa,EAAc;QACpC,IAAI,MAAkB;QACtB,GAAG;YACD,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;YAClC,IAAI,OAAO,MACT,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;QAErC,QAAS,OAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM;QAC9C,OAAO;IACT;IAEA,eAAe,QAAa,EAAc;QACxC,IAAI,MAAkB;QACtB,GAAG;YACD,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;YACnC,IAAI,OAAO,MACT,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU;QAEpC,QAAS,OAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM;QAC9C,OAAO;IACT;IA5EA,YAAY,UAA+B,EAAE,SAAoB,EAAE,WAAwB,EAAE,eAAyB,IAAI,KAAK,CAAE;QAC/H,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,GAAG,cAAc,SAAS,gBAAgB;QAC5D,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,YAAY,GAAG,gBAAgB;IACtC;AAwEF", "sources": ["packages/@react-aria/tabs/src/TabsKeyboardDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, Direction, Key, KeyboardDelegate, Node, Orientation} from '@react-types/shared';\n\nexport class TabsKeyboardDelegate<T> implements KeyboardDelegate {\n  private collection: Collection<Node<T>>;\n  private flipDirection: boolean;\n  private disabledKeys: Set<Key>;\n  private tabDirection: boolean;\n\n  constructor(collection: Collection<Node<T>>, direction: Direction, orientation: Orientation, disabledKeys: Set<Key> = new Set()) {\n    this.collection = collection;\n    this.flipDirection = direction === 'rtl' && orientation === 'horizontal';\n    this.disabledKeys = disabledKeys;\n    this.tabDirection = orientation === 'horizontal';\n  }\n\n  getKeyLeftOf(key: Key): Key | null {\n    if (this.flipDirection) {\n      return this.getNextKey(key);\n    }\n    return this.getPreviousKey(key);\n  }\n\n  getKeyRightOf(key: Key): Key | null {\n    if (this.flipDirection) {\n      return this.getPreviousKey(key);\n    }\n    return this.getNextKey(key);\n  }\n\n\n  private isDisabled(key: Key) {\n    return this.disabledKeys.has(key) || !!this.collection.getItem(key)?.props?.isDisabled;\n  }\n\n  getFirstKey(): Key | null {\n    let key = this.collection.getFirstKey();\n    if (key != null && this.isDisabled(key)) {\n      key = this.getNextKey(key);\n    }\n    return key;\n  }\n\n  getLastKey(): Key | null {\n    let key = this.collection.getLastKey();\n    if (key != null && this.isDisabled(key)) {\n      key = this.getPreviousKey(key);\n    }\n    return key;\n  }\n\n  getKeyAbove(key: Key): Key | null {\n    if (this.tabDirection) {\n      return null;\n    }\n    return this.getPreviousKey(key);\n  }\n\n  getKeyBelow(key: Key): Key | null {\n    if (this.tabDirection) {\n      return null;\n    }\n    return this.getNextKey(key);\n  }\n\n  getNextKey(startKey: Key): Key | null {\n    let key: Key | null = startKey;\n    do {\n      key = this.collection.getKeyAfter(key);\n      if (key == null) {\n        key = this.collection.getFirstKey();\n      }\n    } while (key != null && this.isDisabled(key));\n    return key;\n  }\n\n  getPreviousKey(startKey: Key): Key | null {\n    let key: Key | null = startKey;\n    do {\n      key = this.collection.getKeyBefore(key);\n      if (key == null) {\n        key = this.collection.getLastKey();\n      }\n    } while (key != null && this.isDisabled(key));\n    return key;\n  }\n}\n"], "names": [], "version": 3, "file": "TabsKeyboardDelegate.module.js.map"}