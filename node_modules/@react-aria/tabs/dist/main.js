var $4eeea1c984cc0628$exports = require("./useTab.main.js");
var $8db1928b18472a1f$exports = require("./useTabPanel.main.js");
var $f2b4a4926440e901$exports = require("./useTabList.main.js");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useTab", () => $4eeea1c984cc0628$exports.useTab);
$parcel$export(module.exports, "useTabPanel", () => $8db1928b18472a1f$exports.useTabPanel);
$parcel$export(module.exports, "useTabList", () => $f2b4a4926440e901$exports.useTabList);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 




//# sourceMappingURL=main.js.map
