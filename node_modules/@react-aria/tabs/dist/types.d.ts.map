{"mappings": ";;;ACoBA;IACE,iCAAiC;IACjC,QAAQ,EAAE,aAAa,CAAC;IACxB,6CAA6C;IAC7C,UAAU,EAAE,OAAO,CAAC;IACpB,mCAAmC;IACnC,UAAU,EAAE,OAAO,CAAC;IACpB,uDAAuD;IACvD,SAAS,EAAE,OAAO,CAAA;CACnB;AAED;;;GAGG;AACH,uBAAuB,CAAC,EACtB,KAAK,EAAE,YAAY,EACnB,KAAK,EAAE,aAAa,CAAC,CAAC,EACtB,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GACtC,OAAO,CAyCT;AC7DD;IACE,uCAAuC;IACvC,aAAa,EAAE,aAAa,CAAA;CAC7B;AAGD;;;GAGG;AACH,4BAA4B,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,UAAU,OAAO,GAAG,IAAI,CAAC,GAAG,YAAY,CAiBpI;AExBD,oCAAoC,CAAC,CAAE,SAAQ,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,UAAU,CAAC;CAAG;AAEvF;IACE,uCAAuC;IACvC,YAAY,EAAE,aAAa,CAAA;CAC5B;AAED;;;GAGG;AACH,2BAA2B,CAAC,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,WAAW,GAAG,IAAI,CAAC,GAAG,WAAW,CAyCnI;AC5DD,YAAY,EAAC,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAC,MAAM,mBAAmB,CAAC;AACzF,YAAY,EAAC,WAAW,EAAC,MAAM,qBAAqB,CAAC", "sources": ["packages/@react-aria/tabs/src/packages/@react-aria/tabs/src/utils.ts", "packages/@react-aria/tabs/src/packages/@react-aria/tabs/src/useTab.ts", "packages/@react-aria/tabs/src/packages/@react-aria/tabs/src/useTabPanel.ts", "packages/@react-aria/tabs/src/packages/@react-aria/tabs/src/TabsKeyboardDelegate.ts", "packages/@react-aria/tabs/src/packages/@react-aria/tabs/src/useTabList.ts", "packages/@react-aria/tabs/src/packages/@react-aria/tabs/src/index.ts", "packages/@react-aria/tabs/src/index.ts"], "sourcesContent": [null, null, null, null, null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {useTab} from './useTab';\nexport {useTabPanel} from './useTabPanel';\nexport {useTabList} from './useTabList';\nexport type {AriaTabListProps, AriaTabPanelProps, AriaTabProps} from '@react-types/tabs';\nexport type {Orientation} from '@react-types/shared';\nexport type {TabAria} from './useTab';\nexport type {TabPanelAria} from './useTabPanel';\nexport type {AriaTabListOptions, TabListAria} from './useTabList';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}