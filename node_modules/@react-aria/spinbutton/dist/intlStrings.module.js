import $894vb$arAEmodulejs from "./ar-AE.module.js";
import $894vb$bgBGmodulejs from "./bg-BG.module.js";
import $894vb$csCZmodulejs from "./cs-CZ.module.js";
import $894vb$daDKmodulejs from "./da-DK.module.js";
import $894vb$deDEmodulejs from "./de-DE.module.js";
import $894vb$elGRmodulejs from "./el-GR.module.js";
import $894vb$enUSmodulejs from "./en-US.module.js";
import $894vb$esESmodulejs from "./es-ES.module.js";
import $894vb$etEEmodulejs from "./et-EE.module.js";
import $894vb$fiFImodulejs from "./fi-FI.module.js";
import $894vb$frFRmodulejs from "./fr-FR.module.js";
import $894vb$heILmodulejs from "./he-IL.module.js";
import $894vb$hrHRmodulejs from "./hr-HR.module.js";
import $894vb$huHUmodulejs from "./hu-HU.module.js";
import $894vb$itITmodulejs from "./it-IT.module.js";
import $894vb$jaJPmodulejs from "./ja-JP.module.js";
import $894vb$koKRmodulejs from "./ko-KR.module.js";
import $894vb$ltLTmodulejs from "./lt-LT.module.js";
import $894vb$lvLVmodulejs from "./lv-LV.module.js";
import $894vb$nbNOmodulejs from "./nb-NO.module.js";
import $894vb$nlNLmodulejs from "./nl-NL.module.js";
import $894vb$plPLmodulejs from "./pl-PL.module.js";
import $894vb$ptBRmodulejs from "./pt-BR.module.js";
import $894vb$ptPTmodulejs from "./pt-PT.module.js";
import $894vb$roROmodulejs from "./ro-RO.module.js";
import $894vb$ruRUmodulejs from "./ru-RU.module.js";
import $894vb$skSKmodulejs from "./sk-SK.module.js";
import $894vb$slSImodulejs from "./sl-SI.module.js";
import $894vb$srSPmodulejs from "./sr-SP.module.js";
import $894vb$svSEmodulejs from "./sv-SE.module.js";
import $894vb$trTRmodulejs from "./tr-TR.module.js";
import $894vb$ukUAmodulejs from "./uk-UA.module.js";
import $894vb$zhCNmodulejs from "./zh-CN.module.js";
import $894vb$zhTWmodulejs from "./zh-TW.module.js";

var $a99895ee3dc79e61$exports = {};


































$a99895ee3dc79e61$exports = {
    "ar-AE": $894vb$arAEmodulejs,
    "bg-BG": $894vb$bgBGmodulejs,
    "cs-CZ": $894vb$csCZmodulejs,
    "da-DK": $894vb$daDKmodulejs,
    "de-DE": $894vb$deDEmodulejs,
    "el-GR": $894vb$elGRmodulejs,
    "en-US": $894vb$enUSmodulejs,
    "es-ES": $894vb$esESmodulejs,
    "et-EE": $894vb$etEEmodulejs,
    "fi-FI": $894vb$fiFImodulejs,
    "fr-FR": $894vb$frFRmodulejs,
    "he-IL": $894vb$heILmodulejs,
    "hr-HR": $894vb$hrHRmodulejs,
    "hu-HU": $894vb$huHUmodulejs,
    "it-IT": $894vb$itITmodulejs,
    "ja-JP": $894vb$jaJPmodulejs,
    "ko-KR": $894vb$koKRmodulejs,
    "lt-LT": $894vb$ltLTmodulejs,
    "lv-LV": $894vb$lvLVmodulejs,
    "nb-NO": $894vb$nbNOmodulejs,
    "nl-NL": $894vb$nlNLmodulejs,
    "pl-PL": $894vb$plPLmodulejs,
    "pt-BR": $894vb$ptBRmodulejs,
    "pt-PT": $894vb$ptPTmodulejs,
    "ro-RO": $894vb$roROmodulejs,
    "ru-RU": $894vb$ruRUmodulejs,
    "sk-SK": $894vb$skSKmodulejs,
    "sl-SI": $894vb$slSImodulejs,
    "sr-SP": $894vb$srSPmodulejs,
    "sv-SE": $894vb$svSEmodulejs,
    "tr-TR": $894vb$trTRmodulejs,
    "uk-UA": $894vb$ukUAmodulejs,
    "zh-CN": $894vb$zhCNmodulejs,
    "zh-TW": $894vb$zhTWmodulejs
};


export {$a99895ee3dc79e61$exports as default};
//# sourceMappingURL=intlStrings.module.js.map
