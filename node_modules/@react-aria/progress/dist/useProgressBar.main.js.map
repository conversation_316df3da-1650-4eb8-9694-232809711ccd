{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAoBM,SAAS,0CAAe,KAA2B;IACxD,IAAI,SACF,QAAQ,aACR,WAAW,aACX,WAAW,iBACX,UAAU,mBACV,eAAe,iBACf,gBAAgB;QACd,OAAO;IACT,GACD,GAAG;IAEJ,IAAI,WAAW,CAAA,GAAA,oCAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI,cAAC,UAAU,cAAE,UAAU,EAAC,GAAG,CAAA,GAAA,8BAAO,EAAE;QACtC,GAAG,KAAK;QACR,kDAAkD;QAClD,6CAA6C;QAC7C,kBAAkB;IACpB;IAEA,QAAQ,CAAA,GAAA,2BAAI,EAAE,OAAO,UAAU;IAC/B,IAAI,aAAa,AAAC,CAAA,QAAQ,QAAO,IAAM,CAAA,WAAW,QAAO;IACzD,IAAI,YAAY,CAAA,GAAA,uCAAiB,EAAE;IAEnC,IAAI,CAAC,mBAAmB,CAAC,YAAY;QACnC,IAAI,gBAAgB,cAAc,KAAK,KAAK,YAAY,aAAa;QACrE,aAAa,UAAU,MAAM,CAAC;IAChC;IAEA,OAAO;QACL,kBAAkB,CAAA,GAAA,gCAAS,EAAE,UAAU;YACrC,GAAG,UAAU;YACb,iBAAiB,kBAAkB,YAAY;YAC/C,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB,kBAAkB,YAAY;YAChD,MAAM;QACR;oBACA;IACF;AACF", "sources": ["packages/@react-aria/progress/src/useProgressBar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaProgressBarProps} from '@react-types/progress';\nimport {clamp, filterDOMProps, mergeProps} from '@react-aria/utils';\nimport {DOMAttributes} from '@react-types/shared';\nimport {useLabel} from '@react-aria/label';\nimport {useNumberFormatter} from '@react-aria/i18n';\n\nexport interface ProgressBarAria {\n  /** Props for the progress bar container element. */\n  progressBarProps: DOMAttributes,\n  /** Props for the progress bar's visual label element (if any). */\n  labelProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for a progress bar component.\n * Progress bars show either determinate or indeterminate progress of an operation\n * over time.\n */\nexport function useProgressBar(props: AriaProgressBarProps): ProgressBarAria {\n  let {\n    value = 0,\n    minValue = 0,\n    maxValue = 100,\n    valueLabel,\n    isIndeterminate,\n    formatOptions = {\n      style: 'percent'\n    }\n  } = props;\n\n  let domProps = filterDOMProps(props, {labelable: true});\n  let {labelProps, fieldProps} = useLabel({\n    ...props,\n    // Progress bar is not an HTML input element so it\n    // shouldn't be labeled by a <label> element.\n    labelElementType: 'span'\n  });\n\n  value = clamp(value, minValue, maxValue);\n  let percentage = (value - minValue) / (maxValue - minValue);\n  let formatter = useNumberFormatter(formatOptions);\n\n  if (!isIndeterminate && !valueLabel) {\n    let valueToFormat = formatOptions.style === 'percent' ? percentage : value;\n    valueLabel = formatter.format(valueToFormat);\n  }\n\n  return {\n    progressBarProps: mergeProps(domProps, {\n      ...fieldProps,\n      'aria-valuenow': isIndeterminate ? undefined : value,\n      'aria-valuemin': minValue,\n      'aria-valuemax': maxValue,\n      'aria-valuetext': isIndeterminate ? undefined : valueLabel as string,\n      role: 'progressbar'\n    }),\n    labelProps\n  };\n}\n"], "names": [], "version": 3, "file": "useProgressBar.main.js.map"}