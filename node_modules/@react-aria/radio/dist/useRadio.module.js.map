{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AA+BM,SAAS,0CAAS,KAAqB,EAAE,KAAsB,EAAE,GAAuC;IAC7G,IAAI,SACF,KAAK,YACL,QAAQ,EACR,cAAc,SAAS,EACvB,mBAAmB,cAAc,gBACjC,YAAY,cACZ,UAAU,iBACV,aAAa,WACb,OAAO,aACP,SAAS,WACT,OAAO,EACR,GAAG;IAEJ,MAAM,aAAa,MAAM,UAAU,IAAI,MAAM,UAAU;IAEvD,IAAI,cAAc,YAAY;IAC9B,IAAI,eAAe,aAAa,QAAQ,kBAAkB;IAC1D,IAAI,CAAC,eAAe,CAAC,gBAAgB,QAAQ,GAAG,CAAC,QAAQ,KAAK,cAC5D,QAAQ,IAAI,CAAC;IAGf,IAAI,UAAU,MAAM,aAAa,KAAK;IAEtC,IAAI,WAAW,CAAC;QACd,EAAE,eAAe;QACjB,MAAM,gBAAgB,CAAC;IACzB;IAEA,uFAAuF;IACvF,IAAI,cAAC,UAAU,aAAE,SAAS,EAAC,GAAG,CAAA,GAAA,eAAO,EAAE;sBACrC;oBACA;uBACA;iBACA;mBACA;iBACA;oBACA;IACF;IAEA,mCAAmC;IACnC,IAAI,EAAC,YAAY,UAAU,EAAE,WAAW,cAAc,EAAC,GAAG,CAAA,GAAA,eAAO,EAAE;sBACjE;oBACA;uBACA;mBACA;iBACA;oBACA;QACA,SAAQ,CAAC;gBAGP;YAFA,oBAAA,8BAAA,QAAU;YACV,MAAM,gBAAgB,CAAC;aACvB,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,KAAK;QACpB;IACF;IAEA,IAAI,kBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,mBAAW,EAAE,CAAA,GAAA,iBAAS,EAAE,OAAO;QACpD,SAAS,IAAM,MAAM,mBAAmB,CAAC;IAC3C,IAAI;IACJ,IAAI,eAAe,CAAA,GAAA,iBAAS,EAAE,YAAY;IAC1C,IAAI,WAAW,CAAA,GAAA,qBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI,WAA+B;IACnC,IAAI,MAAM,aAAa,IAAI,MACzB;QAAA,IAAI,MAAM,aAAa,KAAK,OAC1B,WAAW;IACb,OACK,IAAI,MAAM,gBAAgB,KAAK,SAAS,MAAM,gBAAgB,IAAI,MACvE,WAAW;IAEb,IAAI,YACF,WAAW;IAGb,IAAI,QAAC,IAAI,QAAE,IAAI,iBAAE,aAAa,kBAAE,cAAc,sBAAE,kBAAkB,EAAC,GAAG,CAAA,GAAA,yCAAa,EAAE,GAAG,CAAC;IACzF,CAAA,GAAA,mBAAW,EAAE,KAAK,MAAM,oBAAoB,EAAE,MAAM,gBAAgB;IACpE,CAAA,GAAA,wBAAgB,EAAE;4BAAC;IAAkB,GAAG,OAAO;IAE/C,OAAO;QACL,YAAY,CAAA,GAAA,iBAAS,EAAE,YAAY;YAAC,SAAS,CAAA,IAAK,EAAE,cAAc;QAAE;QACpE,YAAY,CAAA,GAAA,iBAAS,EAAE,UAAU;YAC/B,GAAG,YAAY;YACf,MAAM;kBACN;kBACA;sBACA;YACA,UAAU;YACV,UAAU,MAAM,UAAU,IAAI,uBAAuB;qBACrD;mBACA;sBACA;YACA,oBAAoB;gBAClB,KAAK,CAAC,mBAAmB;gBACzB,MAAM,SAAS,GAAG,iBAAiB;gBACnC;aACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;QACjC;oBACA;QACA,YAAY;QACZ,WAAW,aAAa;IAC1B;AACF", "sources": ["packages/@react-aria/radio/src/useRadio.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaRadioProps} from '@react-types/radio';\nimport {filterDOMProps, mergeProps, useFormReset} from '@react-aria/utils';\nimport {InputHTMLAttributes, LabelHTMLAttributes} from 'react';\nimport {radioGroupData} from './utils';\nimport {RadioGroupState} from '@react-stately/radio';\nimport {RefObject} from '@react-types/shared';\nimport {useFocusable, usePress} from '@react-aria/interactions';\nimport {useFormValidation} from '@react-aria/form';\n\nexport interface RadioAria {\n  /** Props for the label wrapper element. */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Whether the radio is disabled. */\n  isDisabled: boolean,\n  /** Whether the radio is currently selected. */\n  isSelected: boolean,\n  /** Whether the radio is in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for an individual\n * radio button in a radio group.\n * @param props - Props for the radio.\n * @param state - State for the radio group, as returned by `useRadioGroupState`.\n * @param ref - Ref to the HTML input element.\n */\nexport function useRadio(props: AriaRadioProps, state: RadioGroupState, ref: RefObject<HTMLInputElement | null>): RadioAria {\n  let {\n    value,\n    children,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby,\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress,\n    onPressUp,\n    onClick\n  } = props;\n\n  const isDisabled = props.isDisabled || state.isDisabled;\n\n  let hasChildren = children != null;\n  let hasAriaLabel = ariaLabel != null || ariaLabelledby != null;\n  if (!hasChildren && !hasAriaLabel && process.env.NODE_ENV !== 'production') {\n    console.warn('If you do not provide children, you must specify an aria-label for accessibility');\n  }\n\n  let checked = state.selectedValue === value;\n\n  let onChange = (e) => {\n    e.stopPropagation();\n    state.setSelectedValue(value);\n  };\n\n  // Handle press state for keyboard interactions and cases where labelProps is not used.\n  let {pressProps, isPressed} = usePress({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress,\n    onPressUp,\n    onClick,\n    isDisabled\n  });\n\n  // Handle press state on the label.\n  let {pressProps: labelProps, isPressed: isLabelPressed} = usePress({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPressUp,\n    onClick,\n    isDisabled,\n    onPress(e) {\n      onPress?.(e);\n      state.setSelectedValue(value);\n      ref.current?.focus();\n    }\n  });\n\n  let {focusableProps} = useFocusable(mergeProps(props, {\n    onFocus: () => state.setLastFocusedValue(value)\n  }), ref);\n  let interactions = mergeProps(pressProps, focusableProps);\n  let domProps = filterDOMProps(props, {labelable: true});\n  let tabIndex: number | undefined = -1;\n  if (state.selectedValue != null) {\n    if (state.selectedValue === value) {\n      tabIndex = 0;\n    }\n  } else if (state.lastFocusedValue === value || state.lastFocusedValue == null) {\n    tabIndex = 0;\n  }\n  if (isDisabled) {\n    tabIndex = undefined;\n  }\n\n  let {name, form, descriptionId, errorMessageId, validationBehavior} = radioGroupData.get(state)!;\n  useFormReset(ref, state.defaultSelectedValue, state.setSelectedValue);\n  useFormValidation({validationBehavior}, state, ref);\n\n  return {\n    labelProps: mergeProps(labelProps, {onClick: e => e.preventDefault()}),\n    inputProps: mergeProps(domProps, {\n      ...interactions,\n      type: 'radio',\n      name,\n      form,\n      tabIndex,\n      disabled: isDisabled,\n      required: state.isRequired && validationBehavior === 'native',\n      checked,\n      value,\n      onChange,\n      'aria-describedby': [\n        props['aria-describedby'],\n        state.isInvalid ? errorMessageId : null,\n        descriptionId\n      ].filter(Boolean).join(' ') || undefined\n    }),\n    isDisabled,\n    isSelected: checked,\n    isPressed: isPressed || isLabelPressed\n  };\n}\n"], "names": [], "version": 3, "file": "useRadio.module.js.map"}