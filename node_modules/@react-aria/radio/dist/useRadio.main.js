var $eeb149278aae5c67$exports = require("./utils.main.js");
var $gWC9A$reactariautils = require("@react-aria/utils");
var $gWC9A$reactariainteractions = require("@react-aria/interactions");
var $gWC9A$reactariaform = require("@react-aria/form");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useRadio", () => $e184702b1b7f1863$export$37b0961d2f4751e2);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 



function $e184702b1b7f1863$export$37b0961d2f4751e2(props, state, ref) {
    let { value: value, children: children, 'aria-label': ariaLabel, 'aria-labelledby': ariaLabelledby, onPressStart: onPressStart, onPressEnd: onPressEnd, onPressChange: onPressChange, onPress: onPress, onPressUp: onPressUp, onClick: onClick } = props;
    const isDisabled = props.isDisabled || state.isDisabled;
    let hasChildren = children != null;
    let hasAriaLabel = ariaLabel != null || ariaLabelledby != null;
    if (!hasChildren && !hasAriaLabel && process.env.NODE_ENV !== 'production') console.warn('If you do not provide children, you must specify an aria-label for accessibility');
    let checked = state.selectedValue === value;
    let onChange = (e)=>{
        e.stopPropagation();
        state.setSelectedValue(value);
    };
    // Handle press state for keyboard interactions and cases where labelProps is not used.
    let { pressProps: pressProps, isPressed: isPressed } = (0, $gWC9A$reactariainteractions.usePress)({
        onPressStart: onPressStart,
        onPressEnd: onPressEnd,
        onPressChange: onPressChange,
        onPress: onPress,
        onPressUp: onPressUp,
        onClick: onClick,
        isDisabled: isDisabled
    });
    // Handle press state on the label.
    let { pressProps: labelProps, isPressed: isLabelPressed } = (0, $gWC9A$reactariainteractions.usePress)({
        onPressStart: onPressStart,
        onPressEnd: onPressEnd,
        onPressChange: onPressChange,
        onPressUp: onPressUp,
        onClick: onClick,
        isDisabled: isDisabled,
        onPress (e) {
            var _ref_current;
            onPress === null || onPress === void 0 ? void 0 : onPress(e);
            state.setSelectedValue(value);
            (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.focus();
        }
    });
    let { focusableProps: focusableProps } = (0, $gWC9A$reactariainteractions.useFocusable)((0, $gWC9A$reactariautils.mergeProps)(props, {
        onFocus: ()=>state.setLastFocusedValue(value)
    }), ref);
    let interactions = (0, $gWC9A$reactariautils.mergeProps)(pressProps, focusableProps);
    let domProps = (0, $gWC9A$reactariautils.filterDOMProps)(props, {
        labelable: true
    });
    let tabIndex = -1;
    if (state.selectedValue != null) {
        if (state.selectedValue === value) tabIndex = 0;
    } else if (state.lastFocusedValue === value || state.lastFocusedValue == null) tabIndex = 0;
    if (isDisabled) tabIndex = undefined;
    let { name: name, form: form, descriptionId: descriptionId, errorMessageId: errorMessageId, validationBehavior: validationBehavior } = (0, $eeb149278aae5c67$exports.radioGroupData).get(state);
    (0, $gWC9A$reactariautils.useFormReset)(ref, state.defaultSelectedValue, state.setSelectedValue);
    (0, $gWC9A$reactariaform.useFormValidation)({
        validationBehavior: validationBehavior
    }, state, ref);
    return {
        labelProps: (0, $gWC9A$reactariautils.mergeProps)(labelProps, {
            onClick: (e)=>e.preventDefault()
        }),
        inputProps: (0, $gWC9A$reactariautils.mergeProps)(domProps, {
            ...interactions,
            type: 'radio',
            name: name,
            form: form,
            tabIndex: tabIndex,
            disabled: isDisabled,
            required: state.isRequired && validationBehavior === 'native',
            checked: checked,
            value: value,
            onChange: onChange,
            'aria-describedby': [
                props['aria-describedby'],
                state.isInvalid ? errorMessageId : null,
                descriptionId
            ].filter(Boolean).join(' ') || undefined
        }),
        isDisabled: isDisabled,
        isSelected: checked,
        isPressed: isPressed || isLabelPressed
    };
}


//# sourceMappingURL=useRadio.main.js.map
