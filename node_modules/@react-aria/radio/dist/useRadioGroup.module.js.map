{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AA6BM,SAAS,0CAAc,KAA0B,EAAE,KAAsB;IAC9E,IAAI,QACF,IAAI,QACJ,IAAI,cACJ,UAAU,cACV,UAAU,cACV,UAAU,eACV,cAAc,gCACd,qBAAqB,QACtB,GAAG;IACJ,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAE1B,IAAI,aAAC,SAAS,oBAAE,gBAAgB,qBAAE,iBAAiB,EAAC,GAAG,MAAM,iBAAiB;IAC9E,IAAI,cAAC,UAAU,cAAE,UAAU,oBAAE,gBAAgB,qBAAE,iBAAiB,EAAC,GAAG,CAAA,GAAA,eAAO,EAAE;QAC3E,GAAG,KAAK;QACR,iDAAiD;QACjD,6CAA6C;QAC7C,kBAAkB;QAClB,WAAW,MAAM,SAAS;QAC1B,cAAc,MAAM,YAAY,IAAI;IACtC;IAEA,IAAI,WAAW,CAAA,GAAA,qBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,yEAAyE;IACzE,wEAAwE;IACxE,8CAA8C;IAC9C,IAAI,oBAAC,gBAAgB,EAAC,GAAG,CAAA,GAAA,qBAAa,EAAE;QACtC,cAAa,CAAC;gBACZ;aAAA,gBAAA,MAAM,MAAM,cAAZ,oCAAA,mBAAA,OAAe;YACf,IAAI,CAAC,MAAM,aAAa,EACtB,MAAM,mBAAmB,CAAC;QAE9B;QACA,eAAe,MAAM,OAAO;QAC5B,qBAAqB,MAAM,aAAa;IAC1C;IAEA,IAAI,YAAY,CAAC;QACf,IAAI;QACJ,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,IAAI,cAAc,SAAS,gBAAgB,YACzC,UAAU;qBAEV,UAAU;gBAEZ;YACF,KAAK;gBACH,IAAI,cAAc,SAAS,gBAAgB,YACzC,UAAU;qBAEV,UAAU;gBAEZ;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE;QACJ;QACA,EAAE,cAAc;QAChB,IAAI,SAAS,CAAA,GAAA,6BAAqB,EAAE,EAAE,aAAa,EAAE;YACnD,MAAM,EAAE,MAAM;YACd,QAAQ,CAAC,OAAS,gBAAgB,CAAA,GAAA,qBAAa,EAAE,MAAM,gBAAgB,IAAI,KAAK,IAAI,KAAK;QAC3F;QACA,IAAI;QACJ,IAAI,YAAY,QAAQ;YACtB,WAAW,OAAO,QAAQ;YAC1B,IAAI,CAAC,UAAU;gBACb,OAAO,WAAW,GAAG,EAAE,aAAa;gBACpC,WAAW,OAAO,UAAU;YAC9B;QACF,OAAO;YACL,WAAW,OAAO,YAAY;YAC9B,IAAI,CAAC,UAAU;gBACb,OAAO,WAAW,GAAG,EAAE,aAAa;gBACpC,WAAW,OAAO,SAAS;YAC7B;QACF;QACA,IAAI,UAAU;YACZ,iFAAiF;YACjF,SAAS,KAAK;YACd,MAAM,gBAAgB,CAAC,SAAS,KAAK;QACvC;IACF;IAEA,IAAI,YAAY,CAAA,GAAA,YAAI,EAAE;IACtB,CAAA,GAAA,yCAAa,EAAE,GAAG,CAAC,OAAO;QACxB,MAAM;cACN;QACA,eAAe,iBAAiB,EAAE;QAClC,gBAAgB,kBAAkB,EAAE;4BACpC;IACF;IAEA,OAAO;QACL,iBAAiB,CAAA,GAAA,iBAAS,EAAE,UAAU;YACpC,iDAAiD;YACjD,MAAM;uBACN;YACA,gBAAgB,MAAM,SAAS,IAAI;YACnC,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,iBAAiB,cAAc;YAC/B,iBAAiB,cAAc;YAC/B,iBAAiB,cAAc;YAC/B,oBAAoB;YACpB,GAAG,UAAU;YACb,GAAG,gBAAgB;QACrB;oBACA;0BACA;2BACA;mBACA;0BACA;2BACA;IACF;AACF", "sources": ["packages/@react-aria/radio/src/useRadioGroup.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaRadioGroupProps} from '@react-types/radio';\nimport {DOMAttributes, ValidationResult} from '@react-types/shared';\nimport {filterDOMProps, getOwnerWindow, mergeProps, useId} from '@react-aria/utils';\nimport {getFocusableTreeWalker} from '@react-aria/focus';\nimport {radioGroupData} from './utils';\nimport {RadioGroupState} from '@react-stately/radio';\nimport {useField} from '@react-aria/label';\nimport {useFocusWithin} from '@react-aria/interactions';\nimport {useLocale} from '@react-aria/i18n';\n\nexport interface RadioGroupAria extends ValidationResult {\n  /** Props for the radio group wrapper element. */\n  radioGroupProps: DOMAttributes,\n  /** Props for the radio group's visible label (if any). */\n  labelProps: DOMAttributes,\n  /** Props for the radio group description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the radio group error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a radio group component.\n * Radio groups allow users to select a single item from a list of mutually exclusive options.\n * @param props - Props for the radio group.\n * @param state - State for the radio group, as returned by `useRadioGroupState`.\n */\nexport function useRadioGroup(props: AriaRadioGroupProps, state: RadioGroupState): RadioGroupAria {\n  let {\n    name,\n    form,\n    isReadOnly,\n    isRequired,\n    isDisabled,\n    orientation = 'vertical',\n    validationBehavior = 'aria'\n  } = props;\n  let {direction} = useLocale();\n\n  let {isInvalid, validationErrors, validationDetails} = state.displayValidation;\n  let {labelProps, fieldProps, descriptionProps, errorMessageProps} = useField({\n    ...props,\n    // Radio group is not an HTML input element so it\n    // shouldn't be labeled by a <label> element.\n    labelElementType: 'span',\n    isInvalid: state.isInvalid,\n    errorMessage: props.errorMessage || validationErrors\n  });\n\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  // When the radio group loses focus, reset the focusable radio to null if\n  // there is no selection. This allows tabbing into the group from either\n  // direction to go to the first or last radio.\n  let {focusWithinProps} = useFocusWithin({\n    onBlurWithin(e) {\n      props.onBlur?.(e);\n      if (!state.selectedValue) {\n        state.setLastFocusedValue(null);\n      }\n    },\n    onFocusWithin: props.onFocus,\n    onFocusWithinChange: props.onFocusChange\n  });\n\n  let onKeyDown = (e) => {\n    let nextDir;\n    switch (e.key) {\n      case 'ArrowRight':\n        if (direction === 'rtl' && orientation !== 'vertical') {\n          nextDir = 'prev';\n        } else {\n          nextDir = 'next';\n        }\n        break;\n      case 'ArrowLeft':\n        if (direction === 'rtl' && orientation !== 'vertical') {\n          nextDir = 'next';\n        } else {\n          nextDir = 'prev';\n        }\n        break;\n      case 'ArrowDown':\n        nextDir = 'next';\n        break;\n      case 'ArrowUp':\n        nextDir = 'prev';\n        break;\n      default:\n        return;\n    }\n    e.preventDefault();\n    let walker = getFocusableTreeWalker(e.currentTarget, {\n      from: e.target,\n      accept: (node) => node instanceof getOwnerWindow(node).HTMLInputElement && node.type === 'radio'\n    });\n    let nextElem;\n    if (nextDir === 'next') {\n      nextElem = walker.nextNode();\n      if (!nextElem) {\n        walker.currentNode = e.currentTarget;\n        nextElem = walker.firstChild();\n      }\n    } else {\n      nextElem = walker.previousNode();\n      if (!nextElem) {\n        walker.currentNode = e.currentTarget;\n        nextElem = walker.lastChild();\n      }\n    }\n    if (nextElem) {\n      // Call focus on nextElem so that keyboard navigation scrolls the radio into view\n      nextElem.focus();\n      state.setSelectedValue(nextElem.value);\n    }\n  };\n\n  let groupName = useId(name);\n  radioGroupData.set(state, {\n    name: groupName,\n    form,\n    descriptionId: descriptionProps.id,\n    errorMessageId: errorMessageProps.id,\n    validationBehavior\n  });\n\n  return {\n    radioGroupProps: mergeProps(domProps, {\n      // https://www.w3.org/TR/wai-aria-1.2/#radiogroup\n      role: 'radiogroup',\n      onKeyDown,\n      'aria-invalid': state.isInvalid || undefined,\n      'aria-errormessage': props['aria-errormessage'],\n      'aria-readonly': isReadOnly || undefined,\n      'aria-required': isRequired || undefined,\n      'aria-disabled': isDisabled || undefined,\n      'aria-orientation': orientation,\n      ...fieldProps,\n      ...focusWithinProps\n    }),\n    labelProps,\n    descriptionProps,\n    errorMessageProps,\n    isInvalid,\n    validationErrors,\n    validationDetails\n  };\n}\n"], "names": [], "version": 3, "file": "useRadioGroup.module.js.map"}