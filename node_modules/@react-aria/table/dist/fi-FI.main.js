module.exports = {
    "ascending": `nouseva`,
    "ascendingSort": (args)=>`lajiteltu sarakkeen ${args.columnName} mukaan nousevassa j\xe4rjestyksess\xe4`,
    "columnSize": (args)=>`${args.value} pikseli\xe4`,
    "descending": `laskeva`,
    "descendingSort": (args)=>`lajiteltu sarakkeen ${args.columnName} mukaan laskevassa j\xe4rjestyksess\xe4`,
    "resizerDescription": `<PERSON><PERSON><PERSON> koon muutos painamalla Enter-n\xe4pp\xe4int\xe4`,
    "select": `Valitse`,
    "selectAll": `<PERSON><PERSON><PERSON> kaikki`,
    "sortable": `lajiteltava sarake`
};


//# sourceMappingURL=fi-FI.main.js.map
