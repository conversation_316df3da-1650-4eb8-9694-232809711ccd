module.exports = {
    "ascending": `t\xf5usev j\xe4rjestus`,
    "ascendingSort": (args)=>`sorditud veeru j\xe4rgi ${args.columnName} t\xf5usvas j\xe4rjestuses`,
    "columnSize": (args)=>`${args.value} pikslit`,
    "descending": `laskuv j\xe4rjestus`,
    "descendingSort": (args)=>`sorditud veeru j\xe4rgi ${args.columnName} laskuvas j\xe4rjestuses`,
    "resizerDescription": `<PERSON>uruse muutmise alustamiseks vajutage klahvi Enter`,
    "select": `Vali`,
    "selectAll": `Vali k\xf5ik`,
    "sortable": `sorditav veerg`
};


//# sourceMappingURL=et-EE.main.js.map
