module.exports = {
    "ascending": `\u{5E2}\u{5D5}\u{5DC}\u{5D4}`,
    "ascendingSort": (args)=>`\u{5DE}\u{5D5}\u{5D9}\u{5DF} \u{5DC}\u{5E4}\u{5D9} \u{5E2}\u{5DE}\u{5D5}\u{5D3}\u{5D4} ${args.columnName} \u{5D1}\u{5E1}\u{5D3}\u{5E8} \u{5E2}\u{5D5}\u{5DC}\u{5D4}`,
    "columnSize": (args)=>`${args.value} \u{5E4}\u{5D9}\u{5E7}\u{5E1}\u{5DC}\u{5D9}\u{5DD}`,
    "descending": `\u{5D9}\u{5D5}\u{5E8}\u{5D3}`,
    "descendingSort": (args)=>`\u{5DE}\u{5D5}\u{5D9}\u{5DF} \u{5DC}\u{5E4}\u{5D9} \u{5E2}\u{5DE}\u{5D5}\u{5D3}\u{5D4} ${args.columnName} \u{5D1}\u{5E1}\u{5D3}\u{5E8} \u{5D9}\u{5D5}\u{5E8}\u{5D3}`,
    "resizerDescription": `\u{5D4}\u{5E7}\u{5E9} Enter \u{5DB}\u{5D3}\u{5D9} \u{5DC}\u{5E9}\u{5E0}\u{5D5}\u{5EA} \u{5D0}\u{5EA} \u{5D4}\u{5D2}\u{5D5}\u{5D3}\u{5DC}`,
    "select": `\u{5D1}\u{5D7}\u{5E8}`,
    "selectAll": `\u{5D1}\u{5D7}\u{5E8} \u{5D4}\u{5DB}\u{5D5}\u{5DC}`,
    "sortable": `\u{5E2}\u{5DE}\u{5D5}\u{5D3}\u{5D4} \u{5E9}\u{5E0}\u{5D9}\u{5EA}\u{5DF} \u{5DC}\u{5DE}\u{5D9}\u{5D9}\u{5DF}`
};


//# sourceMappingURL=he-IL.main.js.map
