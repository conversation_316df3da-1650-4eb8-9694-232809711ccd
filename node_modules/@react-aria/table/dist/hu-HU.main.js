module.exports = {
    "ascending": `n\xf6vekv\u{151}`,
    "ascendingSort": (args)=>`rendezve a(z) ${args.columnName} oszlop szerint, n\xf6vekv\u{151} sorrendben`,
    "columnSize": (args)=>`${args.value} k\xe9ppont`,
    "descending": `cs\xf6kken\u{151}`,
    "descendingSort": (args)=>`rendezve a(z) ${args.columnName} oszlop szerint, cs\xf6kken\u{151} sorrendben`,
    "resizerDescription": `<PERSON>yom<PERSON> le az Enter billenty\u{171}t az \xe1tm\xe9retez\xe9s megkezd\xe9s\xe9hez`,
    "select": `Kijel\xf6l\xe9s`,
    "selectAll": `\xd6sszes kijel\xf6l\xe9se`,
    "sortable": `rendezend\u{151} oszlop`
};


//# sourceMappingURL=hu-HU.main.js.map
