module.exports = {
    "ascending": `ascendente`,
    "ascendingSort": (args)=>`ordenado por columna ${args.columnName} en sentido ascendente`,
    "columnSize": (args)=>`${args.value} p\xedxeles`,
    "descending": `descendente`,
    "descendingSort": (args)=>`ordenado por columna ${args.columnName} en orden descendente`,
    "resizerDescription": `Pulse Intro para empezar a redimensionar`,
    "select": `<PERSON><PERSON><PERSON>onar`,
    "selectAll": `Se<PERSON>ccionar todos`,
    "sortable": `columna ordenable`
};


//# sourceMappingURL=es-ES.main.js.map
