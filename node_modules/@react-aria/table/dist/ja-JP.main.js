module.exports = {
    "ascending": `\u{6607}\u{9806}`,
    "ascendingSort": (args)=>`\u{5217} ${args.columnName} \u{3092}\u{6607}\u{9806}\u{3067}\u{4E26}\u{3079}\u{66FF}\u{3048}`,
    "columnSize": (args)=>`${args.value} \u{30D4}\u{30AF}\u{30BB}\u{30EB}`,
    "descending": `\u{964D}\u{9806}`,
    "descendingSort": (args)=>`\u{5217} ${args.columnName} \u{3092}\u{964D}\u{9806}\u{3067}\u{4E26}\u{3079}\u{66FF}\u{3048}`,
    "resizerDescription": `Enter \u{30AD}\u{30FC}\u{3092}\u{62BC}\u{3057}\u{3066}\u{30B5}\u{30A4}\u{30BA}\u{5909}\u{66F4}\u{3092}\u{958B}\u{59CB}`,
    "select": `\u{9078}\u{629E}`,
    "selectAll": `\u{3059}\u{3079}\u{3066}\u{9078}\u{629E}`,
    "sortable": `\u{4E26}\u{3079}\u{66FF}\u{3048}\u{53EF}\u{80FD}\u{306A}\u{5217}`
};


//# sourceMappingURL=ja-JP.main.js.map
