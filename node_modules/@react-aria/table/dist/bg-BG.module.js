var $cb80dcce530985b9$exports = {};
$cb80dcce530985b9$exports = {
    "ascending": `\u{432}\u{44A}\u{437}\u{445}\u{43E}\u{434}\u{44F}\u{449}`,
    "ascendingSort": (args)=>`\u{441}\u{43E}\u{440}\u{442}\u{438}\u{440}\u{430}\u{43D}\u{43E} \u{43F}\u{43E} \u{43A}\u{43E}\u{43B}\u{43E}\u{43D}\u{430} ${args.columnName} \u{432}\u{44A}\u{432} \u{432}\u{44A}\u{437}\u{445}\u{43E}\u{434}\u{44F}\u{449} \u{440}\u{435}\u{434}`,
    "columnSize": (args)=>`${args.value} \u{43F}\u{438}\u{43A}\u{441}\u{435}\u{43B}\u{430}`,
    "descending": `\u{43D}\u{438}\u{437}\u{445}\u{43E}\u{434}\u{44F}\u{449}`,
    "descendingSort": (args)=>`\u{441}\u{43E}\u{440}\u{442}\u{438}\u{440}\u{430}\u{43D}\u{43E} \u{43F}\u{43E} \u{43A}\u{43E}\u{43B}\u{43E}\u{43D}\u{430} ${args.columnName} \u{432} \u{43D}\u{438}\u{437}\u{445}\u{43E}\u{434}\u{44F}\u{449} \u{440}\u{435}\u{434}`,
    "resizerDescription": `\u{41D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{435}\u{442}\u{435} \u{201E}Enter\u{201C}, \u{437}\u{430} \u{434}\u{430} \u{437}\u{430}\u{43F}\u{43E}\u{447}\u{43D}\u{435}\u{442}\u{435} \u{434}\u{430} \u{43F}\u{440}\u{435}\u{43E}\u{440}\u{430}\u{437}\u{43C}\u{435}\u{440}\u{44F}\u{432}\u{430}\u{442}\u{435}`,
    "select": `\u{418}\u{437}\u{431}\u{435}\u{440}\u{435}\u{442}\u{435}`,
    "selectAll": `\u{418}\u{437}\u{431}\u{435}\u{440}\u{435}\u{442}\u{435} \u{432}\u{441}\u{438}\u{447}\u{43A}\u{43E}`,
    "sortable": `\u{441}\u{43E}\u{440}\u{442}\u{438}\u{440}\u{430}\u{449}\u{430} \u{43A}\u{43E}\u{43B}\u{43E}\u{43D}\u{430}`
};


export {$cb80dcce530985b9$exports as default};
//# sourceMappingURL=bg-BG.module.js.map
