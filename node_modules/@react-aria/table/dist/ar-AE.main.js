module.exports = {
    "ascending": `\u{62A}\u{635}\u{627}\u{639}\u{62F}\u{64A}`,
    "ascendingSort": (args)=>`\u{62A}\u{631}\u{62A}\u{64A}\u{628} \u{62D}\u{633}\u{628} \u{627}\u{644}\u{639}\u{645}\u{648}\u{62F} ${args.columnName} \u{628}\u{62A}\u{631}\u{62A}\u{64A}\u{628} \u{62A}\u{635}\u{627}\u{639}\u{62F}\u{64A}`,
    "columnSize": (args)=>`${args.value} \u{628}\u{627}\u{644}\u{628}\u{643}\u{633}\u{644}`,
    "descending": `\u{62A}\u{646}\u{627}\u{632}\u{644}\u{64A}`,
    "descendingSort": (args)=>`\u{62A}\u{631}\u{62A}\u{64A}\u{628} \u{62D}\u{633}\u{628} \u{627}\u{644}\u{639}\u{645}\u{648}\u{62F} ${args.columnName} \u{628}\u{62A}\u{631}\u{62A}\u{64A}\u{628} \u{62A}\u{646}\u{627}\u{632}\u{644}\u{64A}`,
    "resizerDescription": `\u{627}\u{636}\u{63A}\u{637} \u{639}\u{644}\u{649} \u{645}\u{641}\u{62A}\u{627}\u{62D} Enter \u{644}\u{628}\u{62F}\u{621} \u{62A}\u{63A}\u{64A}\u{64A}\u{631} \u{627}\u{644}\u{62D}\u{62C}\u{645}`,
    "select": `\u{62A}\u{62D}\u{62F}\u{64A}\u{62F}`,
    "selectAll": `\u{62A}\u{62D}\u{62F}\u{64A}\u{62F} \u{627}\u{644}\u{643}\u{644}`,
    "sortable": `\u{639}\u{645}\u{648}\u{62F} \u{642}\u{627}\u{628}\u{644} \u{644}\u{644}\u{62A}\u{631}\u{62A}\u{64A}\u{628}`
};


//# sourceMappingURL=ar-AE.main.js.map
