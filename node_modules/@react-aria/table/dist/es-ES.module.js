var $8cc39eb66c2bf220$exports = {};
$8cc39eb66c2bf220$exports = {
    "ascending": `ascendente`,
    "ascendingSort": (args)=>`ordenado por columna ${args.columnName} en sentido ascendente`,
    "columnSize": (args)=>`${args.value} p\xedxeles`,
    "descending": `descendente`,
    "descendingSort": (args)=>`ordenado por columna ${args.columnName} en orden descendente`,
    "resizerDescription": `Pulse Intro para empezar a redimensionar`,
    "select": `Se<PERSON>ccionar`,
    "selectAll": `Seleccionar todos`,
    "sortable": `columna ordenable`
};


export {$8cc39eb66c2bf220$exports as default};
//# sourceMappingURL=es-ES.module.js.map
