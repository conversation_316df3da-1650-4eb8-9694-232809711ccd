module.exports = {
    "ascending": `croissant`,
    "ascendingSort": (args)=>`tri\xe9 en fonction de la colonne\xa0${args.columnName} par ordre croissant`,
    "columnSize": (args)=>`${args.value}\xa0pixels`,
    "descending": `d\xe9croissant`,
    "descendingSort": (args)=>`tri\xe9 en fonction de la colonne\xa0${args.columnName} par ordre d\xe9croissant`,
    "resizerDescription": `Appuyez sur Entr\xe9e pour commencer le redimensionnement.`,
    "select": `S\xe9lectionner`,
    "selectAll": `S\xe9lectionner tout`,
    "sortable": `colonne triable`
};


//# sourceMappingURL=fr-FR.main.js.map
