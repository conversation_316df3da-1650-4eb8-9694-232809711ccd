module.exports = {
    "ascending": `aufsteigend`,
    "ascendingSort": (args)=>`sortiert nach Spalte ${args.columnName} in aufsteigender Reihenfolge`,
    "columnSize": (args)=>`${args.value} Pixel`,
    "descending": `absteigend`,
    "descendingSort": (args)=>`sortiert nach Spalte ${args.columnName} in absteigender Reihenfolge`,
    "resizerDescription": `Eingabetaste zum Starten der Gr\xf6\xdfen\xe4nderung dr\xfccken`,
    "select": `Ausw\xe4hlen`,
    "selectAll": `Alles ausw\xe4hlen`,
    "sortable": `sortierbare Spalte`
};


//# sourceMappingURL=de-DE.main.js.map
