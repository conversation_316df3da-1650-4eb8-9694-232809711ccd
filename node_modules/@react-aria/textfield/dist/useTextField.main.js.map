{"mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;AAwGM,SAAS,yCACd,KAA8B,EAC9B,GAA0B;IAE1B,IAAI,oBACF,mBAAmB,qBACnB,aAAa,mBACb,aAAa,mBACb,aAAa,aACb,OAAO,4BACP,qBAAqB,QACtB,GAAG;IACJ,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,2CAAiB,EAAU,MAAM,KAAK,EAAE,MAAM,YAAY,IAAI,IAAI,MAAM,QAAQ;IACxG,IAAI,kBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,yCAAW,EAA+B,OAAO;IACxE,IAAI,kBAAkB,CAAA,GAAA,8CAAqB,EAAE;QAC3C,GAAG,KAAK;eACR;IACF;IACA,IAAI,aAAC,SAAS,oBAAE,gBAAgB,qBAAE,iBAAiB,EAAC,GAAG,gBAAgB,iBAAiB;IACxF,IAAI,cAAC,UAAU,cAAE,UAAU,oBAAE,gBAAgB,qBAAE,iBAAiB,EAAC,GAAG,CAAA,GAAA,8BAAO,EAAE;QAC3E,GAAG,KAAK;mBACR;QACA,cAAc,MAAM,YAAY,IAAI;IACtC;IACA,IAAI,WAAW,CAAA,GAAA,oCAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,MAAM,iBAAiB;cACrB;QACA,SAAS,MAAM,OAAO;IACxB;IAEA,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,qBAAO,EAAE;QACZ;IAAlB,CAAA,GAAA,kCAAW,EAAE,KAAK,CAAA,sBAAA,MAAM,YAAY,cAAlB,iCAAA,sBAAsB,cAAc;IACtD,CAAA,GAAA,sCAAgB,EAAE,OAAO,iBAAiB;IAE1C,CAAA,GAAA,sBAAQ,EAAE;QACR,wGAAwG;QACxG,gGAAgG;QAChG,4FAA4F;QAC5F,2FAA2F;QAC3F,wBAAwB;QACxB,iDAAiD;QACjD,iDAAiD;QACjD,IAAI,IAAI,OAAO,YAAY,CAAA,GAAA,oCAAa,EAAE,IAAI,OAAO,EAAE,mBAAmB,EAAE;YAC1E,IAAI,QAAQ,IAAI,OAAO;YACvB,OAAO,cAAc,CAAC,OAAO,gBAAgB;gBAC3C,KAAK,IAAM,MAAM,KAAK;gBACtB,KAAK,KAAO;gBACZ,cAAc;YAChB;QACF;IACF,GAAG;QAAC;KAAI;IAER,OAAO;oBACL;QACA,YAAY,CAAA,GAAA,gCAAS,EACnB,UACA,qBAAqB,UAAU,iBAAiB,WAChD;YACE,UAAU;YACV,UAAU;YACV,UAAU,cAAc,uBAAuB;YAC/C,iBAAiB,AAAC,cAAc,uBAAuB,UAAW;YAClE,gBAAgB,aAAa;YAC7B,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,yBAAyB,KAAK,CAAC,wBAAwB;YACvD,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,iBAAiB,KAAK,CAAC,gBAAgB;YACvC,iBAAiB,KAAK,CAAC,gBAAgB;mBACvC;YACA,UAAU,CAAC,IAAqC,SAAS,EAAE,MAAM,CAAC,KAAK;YACvE,cAAc,MAAM,YAAY;YAChC,gBAAgB,MAAM,cAAc;YACpC,WAAW,MAAM,SAAS;YAC1B,WAAW,MAAM,SAAS;YAC1B,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;YAC9B,WAAW,MAAM,SAAS;YAC1B,aAAa,MAAM,WAAW;YAC9B,YAAY,MAAM,UAAU;YAC5B,CAAC,SAAS,CAAA,GAAA,sCAAI,EAAE,OAAO,EAAE,OAAO,KAAK,iBAAiB,eAAe,EAAE,MAAM,YAAY;YAEzF,mBAAmB;YACnB,QAAQ,MAAM,MAAM;YACpB,OAAO,MAAM,KAAK;YAClB,SAAS,MAAM,OAAO;YAEtB,qBAAqB;YACrB,kBAAkB,MAAM,gBAAgB;YACxC,oBAAoB,MAAM,kBAAkB;YAC5C,qBAAqB,MAAM,mBAAmB;YAE9C,mBAAmB;YACnB,UAAU,MAAM,QAAQ;YAExB,eAAe;YACf,eAAe,MAAM,aAAa;YAClC,SAAS,MAAM,OAAO;YACtB,GAAG,cAAc;YACjB,GAAG,UAAU;QACf;0BAEF;2BACA;mBACA;0BACA;2BACA;IACF;AACF", "sources": ["packages/@react-aria/textfield/src/useTextField.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTextFieldProps} from '@react-types/textfield';\nimport {DOMAttributes, ValidationResult} from '@react-types/shared';\nimport {filterDOMProps, getOwnerWindow, mergeProps, useFormReset} from '@react-aria/utils';\nimport React, {\n  ChangeEvent,\n  HTMLAttributes,\n  type JSX,\n  LabelHTMLAttributes,\n  RefObject,\n  useEffect,\n  useState\n} from 'react';\nimport {useControlledState} from '@react-stately/utils';\nimport {useField} from '@react-aria/label';\nimport {useFocusable} from '@react-aria/interactions';\nimport {useFormValidation} from '@react-aria/form';\nimport {useFormValidationState} from '@react-stately/form';\n\n/**\n * A map of HTML element names and their interface types.\n * For example `'a'` -> `HTMLAnchorElement`.\n */\ntype IntrinsicHTMLElements = {\n  [K in keyof IntrinsicHTMLAttributes]: IntrinsicHTMLAttributes[K] extends HTMLAttributes<infer T> ? T : never\n};\n\n/**\n * A map of HTML element names and their attribute interface types.\n * For example `'a'` -> `AnchorHTMLAttributes<HTMLAnchorElement>`.\n */\ntype IntrinsicHTMLAttributes = JSX.IntrinsicElements;\n\ntype DefaultElementType = 'input';\n\n/**\n * The intrinsic HTML element names that `useTextField` supports; e.g. `input`,\n * `textarea`.\n */\ntype TextFieldIntrinsicElements = keyof Pick<IntrinsicHTMLElements, 'input' | 'textarea'>;\n\n/**\n * The HTML element interfaces that `useTextField` supports based on what is\n * defined for `TextFieldIntrinsicElements`; e.g. `HTMLInputElement`,\n * `HTMLTextAreaElement`.\n */\ntype TextFieldHTMLElementType = Pick<IntrinsicHTMLElements, TextFieldIntrinsicElements>;\n\n/**\n * The HTML attributes interfaces that `useTextField` supports based on what\n * is defined for `TextFieldIntrinsicElements`; e.g. `InputHTMLAttributes`,\n * `TextareaHTMLAttributes`.\n */\ntype TextFieldHTMLAttributesType = Pick<IntrinsicHTMLAttributes, TextFieldIntrinsicElements>;\n\n/**\n * The type of `inputProps` returned by `useTextField`; e.g. `InputHTMLAttributes`,\n * `TextareaHTMLAttributes`.\n */\ntype TextFieldInputProps<T extends TextFieldIntrinsicElements> = TextFieldHTMLAttributesType[T];\n\nexport interface AriaTextFieldOptions<T extends TextFieldIntrinsicElements> extends AriaTextFieldProps<TextFieldHTMLElementType[T]> {\n  /**\n   * The HTML element used to render the input, e.g. 'input', or 'textarea'.\n   * It determines whether certain HTML attributes will be included in `inputProps`.\n   * For example, [`type`](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-type).\n   * @default 'input'\n   */\n  inputElementType?: T,\n  /**\n   * Controls whether inputted text is automatically capitalized and, if so, in what manner.\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autocapitalize).\n   */\n  autoCapitalize?: 'off' | 'none' | 'on' | 'sentences' | 'words' | 'characters',\n  /**\n   * An enumerated attribute that defines what action label or icon to preset for the enter key on virtual keyboards. See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/enterkeyhint).\n   */\n  enterKeyHint?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send'\n}\n\n/**\n * The type of `ref` object that can be passed to `useTextField` based on the given\n * intrinsic HTML element name; e.g.`RefObject<HTMLInputElement>`,\n * `RefObject<HTMLTextAreaElement>`.\n */\ntype TextFieldRefObject<T extends TextFieldIntrinsicElements> = RefObject<TextFieldHTMLElementType[T] | null>;\n\nexport interface TextFieldAria<T extends TextFieldIntrinsicElements = DefaultElementType> extends ValidationResult {\n  /** Props for the input element. */\n  inputProps: TextFieldInputProps<T>,\n  /** Props for the text field's visible label element, if any. */\n  labelProps: DOMAttributes | LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the text field's description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the text field's error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a text field.\n * @param props - Props for the text field.\n * @param ref - Ref to the HTML input or textarea element.\n */\nexport function useTextField<T extends TextFieldIntrinsicElements = DefaultElementType>(\n  props: AriaTextFieldOptions<T>,\n  ref: TextFieldRefObject<T>\n): TextFieldAria<T> {\n  let {\n    inputElementType = 'input',\n    isDisabled = false,\n    isRequired = false,\n    isReadOnly = false,\n    type = 'text',\n    validationBehavior = 'aria'\n  } = props;\n  let [value, setValue] = useControlledState<string>(props.value, props.defaultValue || '', props.onChange);\n  let {focusableProps} = useFocusable<TextFieldHTMLElementType[T]>(props, ref);\n  let validationState = useFormValidationState({\n    ...props,\n    value\n  });\n  let {isInvalid, validationErrors, validationDetails} = validationState.displayValidation;\n  let {labelProps, fieldProps, descriptionProps, errorMessageProps} = useField({\n    ...props,\n    isInvalid,\n    errorMessage: props.errorMessage || validationErrors\n  });\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  const inputOnlyProps = {\n    type,\n    pattern: props.pattern\n  };\n\n  let [initialValue] = useState(value);\n  useFormReset(ref, props.defaultValue ?? initialValue, setValue);\n  useFormValidation(props, validationState, ref);\n\n  useEffect(() => {\n    // This works around a React/Chrome bug that prevents textarea elements from validating when controlled.\n    // We prevent React from updating defaultValue (i.e. children) of textarea when `value` changes,\n    // which causes Chrome to skip validation. Only updating `value` is ok in our case since our\n    // textareas are always controlled. React is planning on removing this synchronization in a\n    // future major version.\n    // https://github.com/facebook/react/issues/19474\n    // https://github.com/facebook/react/issues/11896\n    if (ref.current instanceof getOwnerWindow(ref.current).HTMLTextAreaElement) {\n      let input = ref.current;\n      Object.defineProperty(input, 'defaultValue', {\n        get: () => input.value,\n        set: () => {},\n        configurable: true\n      });\n    }\n  }, [ref]);\n\n  return {\n    labelProps,\n    inputProps: mergeProps(\n      domProps,\n      inputElementType === 'input' ? inputOnlyProps : undefined,\n      {\n        disabled: isDisabled,\n        readOnly: isReadOnly,\n        required: isRequired && validationBehavior === 'native',\n        'aria-required': (isRequired && validationBehavior === 'aria') || undefined,\n        'aria-invalid': isInvalid || undefined,\n        'aria-errormessage': props['aria-errormessage'],\n        'aria-activedescendant': props['aria-activedescendant'],\n        'aria-autocomplete': props['aria-autocomplete'],\n        'aria-haspopup': props['aria-haspopup'],\n        'aria-controls': props['aria-controls'],\n        value,\n        onChange: (e: ChangeEvent<HTMLInputElement>) => setValue(e.target.value),\n        autoComplete: props.autoComplete,\n        autoCapitalize: props.autoCapitalize,\n        maxLength: props.maxLength,\n        minLength: props.minLength,\n        name: props.name,\n        form: props.form,\n        placeholder: props.placeholder,\n        inputMode: props.inputMode,\n        autoCorrect: props.autoCorrect,\n        spellCheck: props.spellCheck,\n        [parseInt(React.version, 10) >= 17 ? 'enterKeyHint' : 'enterkeyhint']: props.enterKeyHint,\n\n        // Clipboard events\n        onCopy: props.onCopy,\n        onCut: props.onCut,\n        onPaste: props.onPaste,\n\n        // Composition events\n        onCompositionEnd: props.onCompositionEnd,\n        onCompositionStart: props.onCompositionStart,\n        onCompositionUpdate: props.onCompositionUpdate,\n\n        // Selection events\n        onSelect: props.onSelect,\n\n        // Input events\n        onBeforeInput: props.onBeforeInput,\n        onInput: props.onInput,\n        ...focusableProps,\n        ...fieldProps\n      }\n    ),\n    descriptionProps,\n    errorMessageProps,\n    isInvalid,\n    validationErrors,\n    validationDetails\n  };\n}\n"], "names": [], "version": 3, "file": "useTextField.main.js.map"}