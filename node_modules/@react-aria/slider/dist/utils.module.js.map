{"mappings": "AAQO,MAAM,4CAA+C,IAAI;AAEzD,SAAS,0CAAiB,KAAkB,EAAE,KAAa;IAChE,IAAI,OAAO,0CAAW,GAAG,CAAC;IAC1B,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;IAGlB,OAAO,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO;AAC9B", "sources": ["packages/@react-aria/slider/src/utils.ts"], "sourcesContent": ["import {SliderState} from '@react-stately/slider';\n\ninterface SliderData {\n  id: string,\n  'aria-describedby'?: string,\n  'aria-details'?: string\n}\n\nexport const sliderData: WeakMap<SliderState, SliderData> = new WeakMap<SliderState, SliderData>();\n\nexport function getSliderThumbId(state: SliderState, index: number): string {\n  let data = sliderData.get(state);\n  if (!data) {\n    throw new Error('Unknown slider state');\n  }\n\n  return `${data.id}-${index}`;\n}\n"], "names": [], "version": 3, "file": "utils.module.js.map"}