{"mappings": ";;;;;;;;;;;;;AAyCO,SAAS,yCACd,IAA4B,EAC5B,KAAkB;IAElB,IAAI,SACF,QAAQ,eACR,UAAU,mBACV,eAAe,aACf,SAAS,YACT,QAAQ,YACR,QAAQ,eACR,cAAc,MAAM,WAAW,QAC/B,IAAI,QACJ,IAAI,EACL,GAAG;IAEJ,IAAI,aAAa,KAAK,UAAU,IAAI,MAAM,UAAU;IACpD,IAAI,aAAa,gBAAgB;IAEjC,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAC1B,IAAI,qBAAC,iBAAiB,wBAAE,oBAAoB,EAAC,GAAG,CAAA,GAAA,yBAAiB;IAEjE,IAAI,OAAO,CAAA,GAAA,yCAAS,EAAE,GAAG,CAAC;QAIS;IAHnC,MAAM,cAAC,UAAU,cAAE,UAAU,EAAC,GAAG,CAAA,GAAA,eAAO,EAAE;QACxC,GAAG,IAAI;QACP,IAAI,CAAA,GAAA,yCAAe,EAAE,OAAO;QAC5B,mBAAmB,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,CAAA,uBAAA,IAAI,CAAC,kBAAkB,cAAvB,kCAAA,uBAA2B,IAAI,CAAC,IAAI;IACvE;IAEA,MAAM,QAAQ,MAAM,MAAM,CAAC,MAAM;IAEjC,MAAM,aAAa,CAAA,GAAA,kBAAU,EAAE;QAC7B,IAAI,SAAS,OAAO,EAClB,CAAA,GAAA,4BAAoB,EAAE,SAAS,OAAO;IAE1C,GAAG;QAAC;KAAS;IAEb,MAAM,YAAY,MAAM,YAAY,KAAK;IAEzC,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,WACF;IAEJ,GAAG;QAAC;QAAW;KAAW;IAE1B,IAAI,WAAW,cAAc;IAC7B,IAAI,kBAAkB,CAAA,GAAA,aAAK,EAAU;IAErC,IAAI,iBAAC,aAAa,EAAC,GAAG,CAAA,GAAA,kBAAU,EAAE;QAChC,WAAU,CAAC;YACT,IAAI,oBACF,gBAAgB,oBAChB,gBAAgB,kBAChB,cAAc,kBACd,cAAc,iBACd,aAAa,oBACb,gBAAgB,YAChB,QAAQ,EACT,GAAG;YACJ,6DAA6D;YAC7D,IAAI,CAAC,+BAA+B,IAAI,CAAC,EAAE,GAAG,GAAG;gBAC/C,EAAE,mBAAmB;gBACrB;YACF;YACA,kGAAkG;YAClG,EAAE,cAAc;YAChB,oDAAoD;YACpD,iBAAiB,OAAO;YACxB,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,eAAe,OAAO;oBACtB;gBACF,KAAK;oBACH,eAAe,OAAO;oBACtB;gBACF,KAAK;oBACH,cAAc,OAAO,iBAAiB;oBACtC;gBACF,KAAK;oBACH,cAAc,OAAO,iBAAiB;oBACtC;YACJ;YACA,iBAAiB,OAAO;QAC1B;IACF;IAEA,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,cAAM,EAAE;QACxB;YACE,gBAAgB,OAAO,GAAG;YAC1B,MAAM,gBAAgB,CAAC,OAAO;QAChC;QACA,QAAO,UAAC,MAAM,UAAE,MAAM,eAAE,WAAW,YAAE,QAAQ,EAAC;YAC5C,MAAM,mBACJ,eAAe,mBACf,eAAe,kBACf,cAAc,kBACd,cAAc,QACd,IAAI,YACJ,QAAQ,EACT,GAAG;YACJ,IAAI,CAAC,SAAS,OAAO,EACnB;YAEF,IAAI,SAAC,KAAK,UAAE,MAAM,EAAC,GAAG,SAAS,OAAO,CAAC,qBAAqB;YAC5D,IAAI,OAAO,aAAa,SAAS;YAEjC,IAAI,gBAAgB,OAAO,IAAI,MAC7B,gBAAgB,OAAO,GAAG,gBAAgB,SAAS;YAErD,IAAI,gBAAgB;gBAClB,IAAI,AAAC,SAAS,KAAK,YAAc,SAAS,KAAK,CAAC,YAAa,SAAS,GACpE,eAAe,OAAO,WAAW,WAAW;qBAE5C,eAAe,OAAO,WAAW,WAAW;mBAEzC;gBACL,IAAI,QAAQ,aAAa,SAAS;gBAClC,IAAI,cAAc,UAChB,QAAQ,CAAC;gBAGX,gBAAgB,OAAO,IAAI;gBAC3B,gBAAgB,OAAO,CAAA,GAAA,YAAI,EAAE,gBAAgB,OAAO,GAAG,MAAM,GAAG;YAClE;QACF;QACA;YACE,MAAM,gBAAgB,CAAC,OAAO;QAChC;IACF;IAEA,kDAAkD;IAClD,MAAM,gBAAgB,CAAC,OAAO,CAAC;IAE/B,MAAM,kBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,mBAAW,EAClC,CAAA,GAAA,iBAAS,EAAE,MAAM;QACf,SAAS,IAAM,MAAM,eAAe,CAAC;QACrC,QAAQ,IAAM,MAAM,eAAe,CAAC;IACtC,IACA;IAGF,IAAI,iBAAiB,CAAA,GAAA,aAAK,EAAsB;IAChD,IAAI,SAAS,CAAC;QACZ;QACA,eAAe,OAAO,GAAG;QACzB,MAAM,gBAAgB,CAAC,OAAO;QAE9B,kBAAkB,QAAQ,WAAW,MAAM;QAC3C,kBAAkB,QAAQ,YAAY,MAAM;QAC5C,kBAAkB,QAAQ,aAAa,MAAM;IAE/C;IAEA,IAAI,OAAO,CAAC;YACc;YAAf;QAAT,IAAI,KAAK,CAAA,eAAA,EAAE,SAAS,cAAX,0BAAA,gBAAe,oBAAA,EAAE,cAAc,cAAhB,wCAAA,iBAAkB,CAAC,EAAE,CAAC,UAAU;QACxD,IAAI,OAAO,eAAe,OAAO,EAAE;YACjC;YACA,MAAM,gBAAgB,CAAC,OAAO;YAC9B,qBAAqB,QAAQ,WAAW,MAAM;YAC9C,qBAAqB,QAAQ,YAAY,MAAM;YAC/C,qBAAqB,QAAQ,aAAa,MAAM;QAClD;IACF;IAEA,IAAI,gBAAgB,MAAM,eAAe,CAAC;IAC1C,IAAI,cAAc,cAAc,OAC9B,gBAAgB,IAAI;IAGtB,IAAI,eAAe,CAAC,aAAa,CAAA,GAAA,iBAAS,EACxC,eACA,WACA;QACE,aAAa,CAAC;YACZ,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EACtD;YAEF;QACF;QACA,eAAe,CAAC;YACd,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EACtD;YAEF,OAAO,EAAE,SAAS;QACpB;QACA,cAAc,CAAC;YAAyB,OAAO,EAAE,cAAc,CAAC,EAAE,CAAC,UAAU;QAAE;IACjF,KACE,CAAC;IAEL,CAAA,GAAA,mBAAW,EAAE,UAAU,MAAM,aAAa,CAAC,MAAM,EAAE,CAAC;QAClD,MAAM,aAAa,CAAC,OAAO;IAC7B;IAEA,sEAAsE;IACtE,sEAAsE;IACtE,oEAAoE;IACpE,oEAAoE;IACpE,OAAO;QACL,YAAY,CAAA,GAAA,iBAAS,EAAE,gBAAgB,YAAY;YACjD,MAAM;YACN,UAAU,CAAC,aAAa,IAAI;YAC5B,KAAK,MAAM,gBAAgB,CAAC;YAC5B,KAAK,MAAM,gBAAgB,CAAC;YAC5B,MAAM,MAAM,IAAI;YAChB,OAAO;kBACP;kBACA;YACA,UAAU;YACV,oBAAoB;YACpB,kBAAkB,MAAM,kBAAkB,CAAC;YAC3C,iBAAiB,cAAc;YAC/B,gBAAgB,aAAa,oBAAoB,aAAa;YAC9D,qBAAqB,IAAI,CAAC,oBAAoB;YAC9C,oBAAoB;gBAAC,IAAI,CAAC,mBAAmB;gBAAE,IAAI,CAAC,mBAAmB;aAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;YAC9F,gBAAgB;gBAAC,IAAI,CAAC,eAAe;gBAAE,IAAI,CAAC,eAAe;aAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;YAClF,UAAU,CAAC;gBACT,MAAM,aAAa,CAAC,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;YACtD;QACF;QACA,YAAY;YACV,GAAG,YAAY;YACf,OAAO;gBACL,UAAU;gBACV,CAAC,aAAa,QAAQ,OAAO,EAAE,GAAG,gBAAgB,IAAI,CAAC,CAAC;gBACxD,WAAW;gBACX,aAAa;YACf;QACF;oBACA;QACA,YAAY,MAAM,eAAe,CAAC;oBAClC;mBACA;IACF;AACF", "sources": ["packages/@react-aria/slider/src/useSliderThumb.ts"], "sourcesContent": ["import {AriaSliderThumbProps} from '@react-types/slider';\nimport {clamp, focusWithoutScrolling, mergeProps, useFormReset, useGlobalListeners} from '@react-aria/utils';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {getSliderThumbId, sliderData} from './utils';\nimport React, {ChangeEvent, InputHTMLAttributes, LabelHTMLAttributes, useCallback, useEffect, useRef} from 'react';\nimport {SliderState} from '@react-stately/slider';\nimport {useFocusable, useKeyboard, useMove} from '@react-aria/interactions';\nimport {useLabel} from '@react-aria/label';\nimport {useLocale} from '@react-aria/i18n';\n\nexport interface SliderThumbAria {\n  /** Props for the root thumb element; handles the dragging motion. */\n  thumbProps: DOMAttributes,\n\n  /** Props for the visually hidden range input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n\n  /** Props for the label element for this thumb (optional). */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n\n  /** Whether this thumb is currently being dragged. */\n  isDragging: boolean,\n  /** Whether the thumb is currently focused. */\n  isFocused: boolean,\n  /** Whether the thumb is disabled. */\n  isDisabled: boolean\n}\n\nexport interface AriaSliderThumbOptions extends AriaSliderThumbProps {\n  /** A ref to the track element. */\n  trackRef: RefObject<Element | null>,\n  /** A ref to the thumb input element. */\n  inputRef: RefObject<HTMLInputElement | null>\n}\n\n/**\n * Provides behavior and accessibility for a thumb of a slider component.\n *\n * @param opts Options for this Slider thumb.\n * @param state Slider state, created via `useSliderState`.\n */\nexport function useSliderThumb(\n  opts: AriaSliderThumbOptions,\n  state: SliderState\n): SliderThumbAria {\n  let {\n    index = 0,\n    isRequired,\n    validationState,\n    isInvalid,\n    trackRef,\n    inputRef,\n    orientation = state.orientation,\n    name,\n    form\n  } = opts;\n\n  let isDisabled = opts.isDisabled || state.isDisabled;\n  let isVertical = orientation === 'vertical';\n\n  let {direction} = useLocale();\n  let {addGlobalListener, removeGlobalListener} = useGlobalListeners();\n\n  let data = sliderData.get(state)!;\n  const {labelProps, fieldProps} = useLabel({\n    ...opts,\n    id: getSliderThumbId(state, index),\n    'aria-labelledby': `${data.id} ${opts['aria-labelledby'] ?? ''}`.trim()\n  });\n\n  const value = state.values[index];\n\n  const focusInput = useCallback(() => {\n    if (inputRef.current) {\n      focusWithoutScrolling(inputRef.current);\n    }\n  }, [inputRef]);\n\n  const isFocused = state.focusedThumb === index;\n\n  useEffect(() => {\n    if (isFocused) {\n      focusInput();\n    }\n  }, [isFocused, focusInput]);\n\n  let reverseX = direction === 'rtl';\n  let currentPosition = useRef<number>(null);\n\n  let {keyboardProps} = useKeyboard({\n    onKeyDown(e) {\n      let {\n        getThumbMaxValue,\n        getThumbMinValue,\n        decrementThumb,\n        incrementThumb,\n        setThumbValue,\n        setThumbDragging,\n        pageSize\n      } = state;\n      // these are the cases that useMove or useSlider don't handle\n      if (!/^(PageUp|PageDown|Home|End)$/.test(e.key)) {\n        e.continuePropagation();\n        return;\n      }\n      // same handling as useMove, stopPropagation to prevent useSlider from handling the event as well.\n      e.preventDefault();\n      // remember to set this so that onChangeEnd is fired\n      setThumbDragging(index, true);\n      switch (e.key) {\n        case 'PageUp':\n          incrementThumb(index, pageSize);\n          break;\n        case 'PageDown':\n          decrementThumb(index, pageSize);\n          break;\n        case 'Home':\n          setThumbValue(index, getThumbMinValue(index));\n          break;\n        case 'End':\n          setThumbValue(index, getThumbMaxValue(index));\n          break;\n      }\n      setThumbDragging(index, false);\n    }\n  });\n\n  let {moveProps} = useMove({\n    onMoveStart() {\n      currentPosition.current = null;\n      state.setThumbDragging(index, true);\n    },\n    onMove({deltaX, deltaY, pointerType, shiftKey}) {\n      const {\n        getThumbPercent,\n        setThumbPercent,\n        decrementThumb,\n        incrementThumb,\n        step,\n        pageSize\n      } = state;\n      if (!trackRef.current) {\n        return;\n      }\n      let {width, height} = trackRef.current.getBoundingClientRect();\n      let size = isVertical ? height : width;\n\n      if (currentPosition.current == null) {\n        currentPosition.current = getThumbPercent(index) * size;\n      }\n      if (pointerType === 'keyboard') {\n        if ((deltaX > 0 && reverseX) || (deltaX < 0 && !reverseX) || deltaY > 0) {\n          decrementThumb(index, shiftKey ? pageSize : step);\n        } else {\n          incrementThumb(index, shiftKey ? pageSize : step);\n        }\n      } else {\n        let delta = isVertical ? deltaY : deltaX;\n        if (isVertical || reverseX) {\n          delta = -delta;\n        }\n\n        currentPosition.current += delta;\n        setThumbPercent(index, clamp(currentPosition.current / size, 0, 1));\n      }\n    },\n    onMoveEnd() {\n      state.setThumbDragging(index, false);\n    }\n  });\n\n  // Immediately register editability with the state\n  state.setThumbEditable(index, !isDisabled);\n\n  const {focusableProps} = useFocusable(\n    mergeProps(opts, {\n      onFocus: () => state.setFocusedThumb(index),\n      onBlur: () => state.setFocusedThumb(undefined)\n    }),\n    inputRef\n  );\n\n  let currentPointer = useRef<number | undefined>(undefined);\n  let onDown = (id?: number) => {\n    focusInput();\n    currentPointer.current = id;\n    state.setThumbDragging(index, true);\n\n    addGlobalListener(window, 'mouseup', onUp, false);\n    addGlobalListener(window, 'touchend', onUp, false);\n    addGlobalListener(window, 'pointerup', onUp, false);\n\n  };\n\n  let onUp = (e) => {\n    let id = e.pointerId ?? e.changedTouches?.[0].identifier;\n    if (id === currentPointer.current) {\n      focusInput();\n      state.setThumbDragging(index, false);\n      removeGlobalListener(window, 'mouseup', onUp, false);\n      removeGlobalListener(window, 'touchend', onUp, false);\n      removeGlobalListener(window, 'pointerup', onUp, false);\n    }\n  };\n\n  let thumbPosition = state.getThumbPercent(index);\n  if (isVertical || direction === 'rtl') {\n    thumbPosition = 1 - thumbPosition;\n  }\n\n  let interactions = !isDisabled ? mergeProps(\n    keyboardProps,\n    moveProps,\n    {\n      onMouseDown: (e: React.MouseEvent) => {\n        if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) {\n          return;\n        }\n        onDown();\n      },\n      onPointerDown: (e: React.PointerEvent) => {\n        if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) {\n          return;\n        }\n        onDown(e.pointerId);\n      },\n      onTouchStart: (e: React.TouchEvent) => {onDown(e.changedTouches[0].identifier);}\n    }\n  ) : {};\n\n  useFormReset(inputRef, state.defaultValues[index], (v) => {\n    state.setThumbValue(index, v);\n  });\n\n  // We install mouse handlers for the drag motion on the thumb div, but\n  // not the key handler for moving the thumb with the slider.  Instead,\n  // we focus the range input, and let the browser handle the keyboard\n  // interactions; we then listen to input's onChange to update state.\n  return {\n    inputProps: mergeProps(focusableProps, fieldProps, {\n      type: 'range',\n      tabIndex: !isDisabled ? 0 : undefined,\n      min: state.getThumbMinValue(index),\n      max: state.getThumbMaxValue(index),\n      step: state.step,\n      value: value,\n      name,\n      form,\n      disabled: isDisabled,\n      'aria-orientation': orientation,\n      'aria-valuetext': state.getThumbValueLabel(index),\n      'aria-required': isRequired || undefined,\n      'aria-invalid': isInvalid || validationState === 'invalid' || undefined,\n      'aria-errormessage': opts['aria-errormessage'],\n      'aria-describedby': [data['aria-describedby'], opts['aria-describedby']].filter(Boolean).join(' '),\n      'aria-details': [data['aria-details'], opts['aria-details']].filter(Boolean).join(' '),\n      onChange: (e: ChangeEvent<HTMLInputElement>) => {\n        state.setThumbValue(index, parseFloat(e.target.value));\n      }\n    }),\n    thumbProps: {\n      ...interactions,\n      style: {\n        position: 'absolute',\n        [isVertical ? 'top' : 'left']: `${thumbPosition * 100}%`,\n        transform: 'translate(-50%, -50%)',\n        touchAction: 'none'\n      }\n    },\n    labelProps,\n    isDragging: state.isThumbDragging(index),\n    isDisabled,\n    isFocused\n  };\n}\n"], "names": [], "version": 3, "file": "useSliderThumb.module.js.map"}