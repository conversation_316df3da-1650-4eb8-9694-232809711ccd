import {getSliderThumbId as $aa519ee6cf463259$export$68e648cbec363a18, sliderData as $aa519ee6cf463259$export$d6c8d9636a3dc49c} from "./utils.mjs";
import {useGlobalListeners as $lSlq7$useGlobalListeners, focusWithoutScrolling as $lSlq7$focusWithoutScrolling, clamp as $lSlq7$clamp, mergeProps as $lSlq7$mergeProps, useFormReset as $lSlq7$useFormReset} from "@react-aria/utils";
import {useCallback as $lSlq7$useCallback, useEffect as $lSlq7$useEffect, useRef as $lSlq7$useRef} from "react";
import {useKeyboard as $lSlq7$useKeyboard, useMove as $lSlq7$useMove, useFocusable as $lSlq7$useFocusable} from "@react-aria/interactions";
import {useLabel as $lSlq7$useLabel} from "@react-aria/label";
import {useLocale as $lSlq7$useLocale} from "@react-aria/i18n";







function $47b897dc8cdb026b$export$8d15029008292ae(opts, state) {
    let { index: index = 0, isRequired: isRequired, validationState: validationState, isInvalid: isInvalid, trackRef: trackRef, inputRef: inputRef, orientation: orientation = state.orientation, name: name, form: form } = opts;
    let isDisabled = opts.isDisabled || state.isDisabled;
    let isVertical = orientation === 'vertical';
    let { direction: direction } = (0, $lSlq7$useLocale)();
    let { addGlobalListener: addGlobalListener, removeGlobalListener: removeGlobalListener } = (0, $lSlq7$useGlobalListeners)();
    let data = (0, $aa519ee6cf463259$export$d6c8d9636a3dc49c).get(state);
    var _opts_arialabelledby;
    const { labelProps: labelProps, fieldProps: fieldProps } = (0, $lSlq7$useLabel)({
        ...opts,
        id: (0, $aa519ee6cf463259$export$68e648cbec363a18)(state, index),
        'aria-labelledby': `${data.id} ${(_opts_arialabelledby = opts['aria-labelledby']) !== null && _opts_arialabelledby !== void 0 ? _opts_arialabelledby : ''}`.trim()
    });
    const value = state.values[index];
    const focusInput = (0, $lSlq7$useCallback)(()=>{
        if (inputRef.current) (0, $lSlq7$focusWithoutScrolling)(inputRef.current);
    }, [
        inputRef
    ]);
    const isFocused = state.focusedThumb === index;
    (0, $lSlq7$useEffect)(()=>{
        if (isFocused) focusInput();
    }, [
        isFocused,
        focusInput
    ]);
    let reverseX = direction === 'rtl';
    let currentPosition = (0, $lSlq7$useRef)(null);
    let { keyboardProps: keyboardProps } = (0, $lSlq7$useKeyboard)({
        onKeyDown (e) {
            let { getThumbMaxValue: getThumbMaxValue, getThumbMinValue: getThumbMinValue, decrementThumb: decrementThumb, incrementThumb: incrementThumb, setThumbValue: setThumbValue, setThumbDragging: setThumbDragging, pageSize: pageSize } = state;
            // these are the cases that useMove or useSlider don't handle
            if (!/^(PageUp|PageDown|Home|End)$/.test(e.key)) {
                e.continuePropagation();
                return;
            }
            // same handling as useMove, stopPropagation to prevent useSlider from handling the event as well.
            e.preventDefault();
            // remember to set this so that onChangeEnd is fired
            setThumbDragging(index, true);
            switch(e.key){
                case 'PageUp':
                    incrementThumb(index, pageSize);
                    break;
                case 'PageDown':
                    decrementThumb(index, pageSize);
                    break;
                case 'Home':
                    setThumbValue(index, getThumbMinValue(index));
                    break;
                case 'End':
                    setThumbValue(index, getThumbMaxValue(index));
                    break;
            }
            setThumbDragging(index, false);
        }
    });
    let { moveProps: moveProps } = (0, $lSlq7$useMove)({
        onMoveStart () {
            currentPosition.current = null;
            state.setThumbDragging(index, true);
        },
        onMove ({ deltaX: deltaX, deltaY: deltaY, pointerType: pointerType, shiftKey: shiftKey }) {
            const { getThumbPercent: getThumbPercent, setThumbPercent: setThumbPercent, decrementThumb: decrementThumb, incrementThumb: incrementThumb, step: step, pageSize: pageSize } = state;
            if (!trackRef.current) return;
            let { width: width, height: height } = trackRef.current.getBoundingClientRect();
            let size = isVertical ? height : width;
            if (currentPosition.current == null) currentPosition.current = getThumbPercent(index) * size;
            if (pointerType === 'keyboard') {
                if (deltaX > 0 && reverseX || deltaX < 0 && !reverseX || deltaY > 0) decrementThumb(index, shiftKey ? pageSize : step);
                else incrementThumb(index, shiftKey ? pageSize : step);
            } else {
                let delta = isVertical ? deltaY : deltaX;
                if (isVertical || reverseX) delta = -delta;
                currentPosition.current += delta;
                setThumbPercent(index, (0, $lSlq7$clamp)(currentPosition.current / size, 0, 1));
            }
        },
        onMoveEnd () {
            state.setThumbDragging(index, false);
        }
    });
    // Immediately register editability with the state
    state.setThumbEditable(index, !isDisabled);
    const { focusableProps: focusableProps } = (0, $lSlq7$useFocusable)((0, $lSlq7$mergeProps)(opts, {
        onFocus: ()=>state.setFocusedThumb(index),
        onBlur: ()=>state.setFocusedThumb(undefined)
    }), inputRef);
    let currentPointer = (0, $lSlq7$useRef)(undefined);
    let onDown = (id)=>{
        focusInput();
        currentPointer.current = id;
        state.setThumbDragging(index, true);
        addGlobalListener(window, 'mouseup', onUp, false);
        addGlobalListener(window, 'touchend', onUp, false);
        addGlobalListener(window, 'pointerup', onUp, false);
    };
    let onUp = (e)=>{
        var _e_changedTouches;
        var _e_pointerId;
        let id = (_e_pointerId = e.pointerId) !== null && _e_pointerId !== void 0 ? _e_pointerId : (_e_changedTouches = e.changedTouches) === null || _e_changedTouches === void 0 ? void 0 : _e_changedTouches[0].identifier;
        if (id === currentPointer.current) {
            focusInput();
            state.setThumbDragging(index, false);
            removeGlobalListener(window, 'mouseup', onUp, false);
            removeGlobalListener(window, 'touchend', onUp, false);
            removeGlobalListener(window, 'pointerup', onUp, false);
        }
    };
    let thumbPosition = state.getThumbPercent(index);
    if (isVertical || direction === 'rtl') thumbPosition = 1 - thumbPosition;
    let interactions = !isDisabled ? (0, $lSlq7$mergeProps)(keyboardProps, moveProps, {
        onMouseDown: (e)=>{
            if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) return;
            onDown();
        },
        onPointerDown: (e)=>{
            if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) return;
            onDown(e.pointerId);
        },
        onTouchStart: (e)=>{
            onDown(e.changedTouches[0].identifier);
        }
    }) : {};
    (0, $lSlq7$useFormReset)(inputRef, state.defaultValues[index], (v)=>{
        state.setThumbValue(index, v);
    });
    // We install mouse handlers for the drag motion on the thumb div, but
    // not the key handler for moving the thumb with the slider.  Instead,
    // we focus the range input, and let the browser handle the keyboard
    // interactions; we then listen to input's onChange to update state.
    return {
        inputProps: (0, $lSlq7$mergeProps)(focusableProps, fieldProps, {
            type: 'range',
            tabIndex: !isDisabled ? 0 : undefined,
            min: state.getThumbMinValue(index),
            max: state.getThumbMaxValue(index),
            step: state.step,
            value: value,
            name: name,
            form: form,
            disabled: isDisabled,
            'aria-orientation': orientation,
            'aria-valuetext': state.getThumbValueLabel(index),
            'aria-required': isRequired || undefined,
            'aria-invalid': isInvalid || validationState === 'invalid' || undefined,
            'aria-errormessage': opts['aria-errormessage'],
            'aria-describedby': [
                data['aria-describedby'],
                opts['aria-describedby']
            ].filter(Boolean).join(' '),
            'aria-details': [
                data['aria-details'],
                opts['aria-details']
            ].filter(Boolean).join(' '),
            onChange: (e)=>{
                state.setThumbValue(index, parseFloat(e.target.value));
            }
        }),
        thumbProps: {
            ...interactions,
            style: {
                position: 'absolute',
                [isVertical ? 'top' : 'left']: `${thumbPosition * 100}%`,
                transform: 'translate(-50%, -50%)',
                touchAction: 'none'
            }
        },
        labelProps: labelProps,
        isDragging: state.isThumbDragging(index),
        isDisabled: isDisabled,
        isFocused: isFocused
    };
}


export {$47b897dc8cdb026b$export$8d15029008292ae as useSliderThumb};
//# sourceMappingURL=useSliderThumb.module.js.map
