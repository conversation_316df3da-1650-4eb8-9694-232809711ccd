{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAkBM,SAAS,0CAAW,KAAuB,EAAE,KAA2B;IAC7E,IAAI,WAAW,CAAA,GAAA,oCAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,IAAI,cAAC,UAAU,EAAC,GAAG,CAAA,GAAA,qCAAO,EAAE;QAC1B,cAAc,IAAM,kBAAA,4BAAA,MAAO,IAAI,CAAC;QAChC,YAAY,IAAM,kBAAA,4BAAA,MAAO,KAAK;IAChC;IAEA,OAAO;QACL,cAAc,CAAA,GAAA,gCAAS,EAAE,UAAU,YAAY;YAC7C,MAAM;QACR;IACF;AACF", "sources": ["packages/@react-aria/tooltip/src/useTooltip.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTooltipProps} from '@react-types/tooltip';\nimport {DOMAttributes} from '@react-types/shared';\nimport {filterDOMProps, mergeProps} from '@react-aria/utils';\nimport {TooltipTriggerState} from '@react-stately/tooltip';\nimport {useHover} from '@react-aria/interactions';\n\nexport interface TooltipAria {\n  /**\n   * Props for the tooltip element.\n   */\n  tooltipProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for a Tooltip component.\n */\nexport function useTooltip(props: AriaTooltipProps, state?: TooltipTriggerState): TooltipAria {\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  let {hoverProps} = useHover({\n    onHoverStart: () => state?.open(true),\n    onHoverEnd: () => state?.close()\n  });\n\n  return {\n    tooltipProps: mergeProps(domProps, hoverProps, {\n      role: 'tooltip'\n    })\n  };\n}\n"], "names": [], "version": 3, "file": "useTooltip.main.js.map"}