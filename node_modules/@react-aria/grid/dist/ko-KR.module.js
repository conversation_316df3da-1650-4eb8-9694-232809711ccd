var $1949c3ad17295fd4$exports = {};
$1949c3ad17295fd4$exports = {
    "deselectedItem": (args)=>`${args.item}\u{C774}(\u{AC00}) \u{C120}\u{D0DD}\u{B418}\u{C9C0} \u{C54A}\u{C558}\u{C2B5}\u{B2C8}\u{B2E4}.`,
    "longPressToSelect": `\u{C120}\u{D0DD} \u{BAA8}\u{B4DC}\u{B85C} \u{B4E4}\u{C5B4}\u{AC00}\u{B824}\u{BA74} \u{AE38}\u{AC8C} \u{B204}\u{B974}\u{C2ED}\u{C2DC}\u{C624}.`,
    "select": `\u{C120}\u{D0DD}`,
    "selectedAll": `\u{BAA8}\u{B4E0} \u{D56D}\u{BAA9}\u{C774} \u{C120}\u{D0DD}\u{B418}\u{C5C8}\u{C2B5}\u{B2C8}\u{B2E4}.`,
    "selectedCount": (args, formatter)=>`${formatter.plural(args.count, {
            "=0": `\u{C120}\u{D0DD}\u{B41C} \u{D56D}\u{BAA9}\u{C774} \u{C5C6}\u{C2B5}\u{B2C8}\u{B2E4}`,
            one: ()=>`${formatter.number(args.count)}\u{AC1C} \u{D56D}\u{BAA9}\u{C774} \u{C120}\u{D0DD}\u{B418}\u{C5C8}\u{C2B5}\u{B2C8}\u{B2E4}`,
            other: ()=>`${formatter.number(args.count)}\u{AC1C} \u{D56D}\u{BAA9}\u{C774} \u{C120}\u{D0DD}\u{B418}\u{C5C8}\u{C2B5}\u{B2C8}\u{B2E4}`
        })}.`,
    "selectedItem": (args)=>`${args.item}\u{C774}(\u{AC00}) \u{C120}\u{D0DD}\u{B418}\u{C5C8}\u{C2B5}\u{B2C8}\u{B2E4}.`
};


export {$1949c3ad17295fd4$exports as default};
//# sourceMappingURL=ko-KR.module.js.map
