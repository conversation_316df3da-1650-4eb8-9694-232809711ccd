{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;IACxE,qBAAqB,CAAC,iEAAkD,CAAC;IACzE,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,2BAA2B,CAAC;IAC5C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC7O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;AACpD", "sources": ["packages/@react-aria/grid/intl/ro-RO.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} neselectat.\",\n  \"longPressToSelect\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lung pentru a intra în modul de selectare.\",\n  \"select\": \"Selectare\",\n  \"selectedAll\": \"Toate elementele selectate.\",\n  \"selectedCount\": \"{count, plural, =0 {Niciun element selectat} one {# element selectat} other {# elemente selectate}}.\",\n  \"selectedItem\": \"{item} selectat.\"\n}\n"], "names": [], "version": 3, "file": "ro-RO.module.js.map"}