{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,qBAAkB,CAAC;IAC9E,qBAAqB,CAAC,iDAA2C,CAAC;IAClE,UAAU,CAAC,YAAS,CAAC;IACrB,eAAe,CAAC,4BAAyB,CAAC;IAC1C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4BAAyB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,sBAAmB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,uBAAoB,CAAC;QAAA,GAAG,CAAC,CAAC;IAClP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,eAAY,CAAC;AACtD", "sources": ["packages/@react-aria/grid/intl/de-DE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nicht ausgewählt.\",\n  \"longPressToSelect\": \"Gedr<PERSON><PERSON> halten, um Auswahlmodus zu öffnen.\",\n  \"select\": \"Auswählen\",\n  \"selectedAll\": \"Alle Elemente ausgewählt.\",\n  \"selectedCount\": \"{count, plural, =0 {Keine Elemente ausgewählt} one {# Element ausgewählt} other {# Elemente ausgewählt}}.\",\n  \"selectedItem\": \"{item} ausgewählt.\"\n}\n"], "names": [], "version": 3, "file": "de-DE.module.js.map"}