{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;IACzE,qBAAqB,CAAC,wDAAyC,CAAC;IAChE,UAAU,CAAC,OAAO,CAAC;IACnB,eAAe,CAAC,sBAAsB,CAAC;IACvC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qBAAqB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,gBAAgB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IACxO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;AACpD", "sources": ["packages/@react-aria/grid/intl/sv-SE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} ej markerat.\",\n  \"longPressToSelect\": \"Tryck länge när du vill öppna väljarläge.\",\n  \"select\": \"Markera\",\n  \"selectedAll\": \"Alla markerade objekt.\",\n  \"selectedCount\": \"{count, plural, =0 {Inga markerade objekt} one {# markerat objekt} other {# markerade objekt}}.\",\n  \"selectedItem\": \"{item} markerat.\"\n}\n"], "names": [], "version": 3, "file": "sv-SE.module.js.map"}