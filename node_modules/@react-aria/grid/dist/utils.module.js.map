{"mappings": "AAAA;;;;;;;;;;CAUC,GAkBM,MAAM,4CAA+E,IAAI", "sources": ["packages/@react-aria/grid/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {GridCollection} from '@react-types/grid';\nimport type {GridState} from '@react-stately/grid';\nimport type {Key, KeyboardDelegate} from '@react-types/shared';\n\ninterface GridMapShared {\n  keyboardDelegate: KeyboardDelegate,\n  actions: {\n    onRowAction?: (key: Key) => void,\n    onCellAction?: (key: Key) => void\n  },\n  shouldSelectOnPressUp?: boolean\n}\n\n// Used to share:\n// keyboard delegate between useGrid and useGridCell\n// onRowAction/onCellAction across hooks\nexport const gridMap: WeakMap<GridState<unknown, GridCollection<unknown>>, GridMapShared> = new WeakMap<GridState<unknown, GridCollection<unknown>>, GridMapShared>();\n"], "names": [], "version": 3, "file": "utils.module.js.map"}