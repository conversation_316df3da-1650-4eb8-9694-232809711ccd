{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iBAAiB,CAAC;IAC7E,qBAAqB,CAAC,oDAAiD,CAAC;IACxE,UAAU,CAAC,WAAW,CAAC;IACvB,eAAe,CAAC,kCAAkC,CAAC;IACnD,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,+BAA4B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,sBAAsB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,wBAAwB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC5P,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;AACxD", "sources": ["packages/@react-aria/grid/intl/es-ES.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} no seleccionado.\",\n  \"longPressToSelect\": \"Mantenga pulsado para abrir el modo de selección.\",\n  \"select\": \"Seleccionar\",\n  \"selectedAll\": \"Todos los elementos seleccionados.\",\n  \"selectedCount\": \"{count, plural, =0 {Ningún elemento seleccionado} one {# elemento seleccionado} other {# elementos seleccionados}}.\",\n  \"selectedItem\": \"{item} seleccionado.\"\n}\n"], "names": [], "version": 3, "file": "es-ES.main.js.map"}