{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;IAC5E,qBAAqB,CAAC,oEAAkD,CAAC;IACzE,UAAU,CAAC,OAAO,CAAC;IACnB,eAAe,CAAC,8BAA8B,CAAC;IAC/C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,yCAAgC,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0BAAuB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC5P,gBAAgB,CAAC,OAAS,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AACtD", "sources": ["packages/@react-aria/grid/intl/pl-PL.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"<PERSON>e zaznaczono {item}.\",\n  \"longPressToSelect\": \"Naciśnij i przytrzymaj, aby wejść do trybu wyboru.\",\n  \"select\": \"Zaznacz\",\n  \"selectedAll\": \"Wszystkie zaznaczone elementy.\",\n  \"selectedCount\": \"{count, plural, =0 {Nie zaznaczono żadnych elementów} one {# zaznaczony element} other {# zaznaczonych elementów}}.\",\n  \"selectedItem\": \"Zaznaczono {item}.\"\n}\n"], "names": [], "version": 3, "file": "pl-PL.main.js.map"}