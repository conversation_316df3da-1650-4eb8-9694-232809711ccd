{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,cAAQ,EAAE,KAAK,IAAI,CAAC,oBAAc,CAAC;IAClF,qBAAqB,CAAC,wEAA6C,CAAC;IACpE,UAAU,CAAC,MAAM,CAAC;IAClB,eAAe,CAAC,uCAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4CAAuB,CAAC;YAAE,KAAK,IAAM,CAAC,WAAQ,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAQ,CAAC;YAAE,OAAO,IAAM,CAAC,WAAQ,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAQ,CAAC;QAAA,GAAG,CAAC,CAAC;IACzO,gBAAgB,CAAC,OAAS,CAAC,yBAAgB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAC3D", "sources": ["packages/@react-aria/grid/intl/cs-CZ.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Polo<PERSON><PERSON> {item} není v<PERSON>.\",\n  \"longPressToSelect\": \"Dlouhým stisknutím přejdete do režimu výběru.\",\n  \"select\": \"Vybrat\",\n  \"selectedAll\": \"Vybrány všechny položky.\",\n  \"selectedCount\": \"{count, plural, =0 {Nevybrány žádné <PERSON>} one {Vybrána # položka} other {Vybráno # položek}}.\",\n  \"selectedItem\": \"Vybrána polož<PERSON> {item}.\"\n}\n"], "names": [], "version": 3, "file": "cs-CZ.main.js.map"}