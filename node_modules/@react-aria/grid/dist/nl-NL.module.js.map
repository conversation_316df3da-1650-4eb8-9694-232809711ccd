{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mBAAmB,CAAC;IAC/E,qBAAqB,CAAC,wCAAwC,CAAC;IAC/D,UAAU,CAAC,UAAU,CAAC;IACtB,eAAe,CAAC,wBAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAkB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC9O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;AACxD", "sources": ["packages/@react-aria/grid/intl/nl-NL.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} niet geselecteerd.\",\n  \"longPressToSelect\": \"Druk lang om de selectiemodus te openen.\",\n  \"select\": \"Selecteren\",\n  \"selectedAll\": \"Alle items geselecteerd.\",\n  \"selectedCount\": \"{count, plural, =0 {Geen items geselecteerd} one {# item geselecteerd} other {# items geselecteerd}}.\",\n  \"selectedItem\": \"{item} geselecteerd.\"\n}\n"], "names": [], "version": 3, "file": "nl-NL.module.js.map"}