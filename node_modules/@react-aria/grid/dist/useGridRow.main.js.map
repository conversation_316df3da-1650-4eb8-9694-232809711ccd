{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAoCM,SAAS,0CAAsE,KAAsB,EAAE,KAAQ,EAAE,GAAuC;QAgBlI,aAA8B;IAfzD,IAAI,QACF,IAAI,iBACJ,aAAa,yBACb,qBAAqB,YACrB,QAAQ,EACT,GAAG;IAEJ,IAAI,WAAC,OAAO,EAAE,uBAAuB,yBAAyB,EAAC,GAAG,CAAA,GAAA,iCAAM,EAAE,GAAG,CAAC;IAC9E,IAAI,cAAc,QAAQ,WAAW,GAAG;YAAM;gBAAA,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,0BAAA,SAAsB,KAAK,GAAG;QAAI;IAChF,IAAI,aAAC,SAAS,EAAE,GAAG,QAAO,GAAG,CAAA,GAAA,2CAAgB,EAAE;QAC7C,kBAAkB,MAAM,gBAAgB;QACxC,KAAK,KAAK,GAAG;aACb;uBACA;QACA,uBAAuB,6BAA6B;QACpD,UAAU,gBAAe,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,QAAQ,IAAG,CAAA,GAAA,2BAAI,EAAE,iBAAA,4BAAA,eAAA,KAAM,KAAK,cAAX,mCAAA,aAAa,QAAQ,EAAE,eAAe;QAC7F,YAAY,MAAM,UAAU,CAAC,IAAI,KAAK;IACxC;IAEA,IAAI,aAAa,MAAM,gBAAgB,CAAC,UAAU,CAAC,KAAK,GAAG;IAE3D,IAAI,WAA0B;QAC5B,MAAM;QACN,iBAAiB,MAAM,gBAAgB,CAAC,aAAa,KAAK,SAAS,aAAa;QAChF,iBAAiB,OAAO,UAAU,IAAI;QACtC,GAAG,SAAS;IACd;IAEA,IAAI,eACF,QAAQ,CAAC,gBAAgB,GAAG,KAAK,KAAK,GAAG,GAAG,2BAA2B;IAGzE,OAAO;kBACL;QACA,GAAG,MAAM;IACX;AACF", "sources": ["packages/@react-aria/grid/src/useGridRow.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from '@react-aria/utils';\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {GridCollection, GridNode} from '@react-types/grid';\nimport {gridMap} from './utils';\nimport {GridState} from '@react-stately/grid';\nimport {SelectableItemStates, useSelectableItem} from '@react-aria/selection';\n\nexport interface GridRowProps<T> {\n  /** An object representing the grid row. Contains all the relevant information that makes up the grid row. */\n  node: GridNode<T>,\n  /** Whether the grid row is contained in a virtual scroller. */\n  isVirtualized?: boolean,\n  /** Whether selection should occur on press up instead of press down. */\n  shouldSelectOnPressUp?: boolean,\n  /**\n   * Handler that is called when a user performs an action on the row.\n   * Please use onCellAction at the collection level instead.\n   * @deprecated\n   **/\n  onAction?: () => void\n}\n\nexport interface GridRowAria extends SelectableItemStates {\n  /** Props for the grid row element. */\n  rowProps: DOMAttributes,\n  /** Whether the row is currently in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a row in a grid.\n * @param props - Props for the row.\n * @param state - State of the parent grid, as returned by `useGridState`.\n */\nexport function useGridRow<T, C extends GridCollection<T>, S extends GridState<T, C>>(props: GridRowProps<T>, state: S, ref: RefObject<FocusableElement | null>): GridRowAria {\n  let {\n    node,\n    isVirtualized,\n    shouldSelectOnPressUp,\n    onAction\n  } = props;\n\n  let {actions, shouldSelectOnPressUp: gridShouldSelectOnPressUp} = gridMap.get(state)!;\n  let onRowAction = actions.onRowAction ? () => actions.onRowAction?.(node.key) : onAction;\n  let {itemProps, ...states} = useSelectableItem({\n    selectionManager: state.selectionManager,\n    key: node.key,\n    ref,\n    isVirtualized,\n    shouldSelectOnPressUp: gridShouldSelectOnPressUp || shouldSelectOnPressUp,\n    onAction: onRowAction || node?.props?.onAction ? chain(node?.props?.onAction, onRowAction) : undefined,\n    isDisabled: state.collection.size === 0\n  });\n\n  let isSelected = state.selectionManager.isSelected(node.key);\n\n  let rowProps: DOMAttributes = {\n    role: 'row',\n    'aria-selected': state.selectionManager.selectionMode !== 'none' ? isSelected : undefined,\n    'aria-disabled': states.isDisabled || undefined,\n    ...itemProps\n  };\n\n  if (isVirtualized) {\n    rowProps['aria-rowindex'] = node.index + 1; // aria-rowindex is 1 based\n  }\n\n  return {\n    rowProps,\n    ...states\n  };\n}\n"], "names": [], "version": 3, "file": "useGridRow.main.js.map"}