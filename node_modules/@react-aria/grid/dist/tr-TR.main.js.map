{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAW,CAAC;IACvE,qBAAqB,CAAC,gDAAoC,CAAC;IAC3D,UAAU,CAAC,MAAG,CAAC;IACf,eAAe,CAAC,4BAAmB,CAAC;IACpC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,6BAAoB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAY,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAY,CAAC;QAAA,GAAG,CAAC,CAAC;IAC9N,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAS,CAAC;AACnD", "sources": ["packages/@react-aria/grid/intl/tr-TR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} seçilmedi.\",\n  \"longPressToSelect\": \"Seçim moduna girmek için uzun basın.\",\n  \"select\": \"Seç\",\n  \"selectedAll\": \"Tüm ögeler seçildi.\",\n  \"selectedCount\": \"{count, plural, =0 {Hiçbir öge seçilmedi} one {# öge seçildi} other {# öge seçildi}}.\",\n  \"selectedItem\": \"{item} seçildi.\"\n}\n"], "names": [], "version": 3, "file": "tr-TR.main.js.map"}