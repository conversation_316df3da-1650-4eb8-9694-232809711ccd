var $3187c0e19200cb16$exports = require("./GridKeyboardDelegate.main.js");
var $8ee34951196858d0$exports = require("./utils.main.js");
var $1eb174acfe8a0f16$exports = require("./useGridSelectionAnnouncement.main.js");
var $340f2fcd0ef9ce8d$exports = require("./useHighlightSelectionDescription.main.js");
var $lBaOG$reactariautils = require("@react-aria/utils");
var $lBaOG$react = require("react");
var $lBaOG$reactariai18n = require("@react-aria/i18n");
var $lBaOG$reactariafocus = require("@react-aria/focus");
var $lBaOG$reactariaselection = require("@react-aria/selection");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useGrid", () => $11d770dfabe45077$export$f6b86a04e5d66d90);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 








function $11d770dfabe45077$export$f6b86a04e5d66d90(props, state, ref) {
    let { isVirtualized: isVirtualized, disallowTypeAhead: disallowTypeAhead, keyboardDelegate: keyboardDelegate, focusMode: focusMode, scrollRef: scrollRef, getRowText: getRowText, onRowAction: onRowAction, onCellAction: onCellAction, escapeKeyBehavior: escapeKeyBehavior = 'clearSelection', shouldSelectOnPressUp: shouldSelectOnPressUp } = props;
    let { selectionManager: manager } = state;
    if (!props['aria-label'] && !props['aria-labelledby']) console.warn('An aria-label or aria-labelledby prop is required for accessibility.');
    // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).
    // When virtualized, the layout object will be passed in as a prop and override this.
    let collator = (0, $lBaOG$reactariai18n.useCollator)({
        usage: 'search',
        sensitivity: 'base'
    });
    let { direction: direction } = (0, $lBaOG$reactariai18n.useLocale)();
    let disabledBehavior = state.selectionManager.disabledBehavior;
    let delegate = (0, $lBaOG$react.useMemo)(()=>keyboardDelegate || new (0, $3187c0e19200cb16$exports.GridKeyboardDelegate)({
            collection: state.collection,
            disabledKeys: state.disabledKeys,
            disabledBehavior: disabledBehavior,
            ref: ref,
            direction: direction,
            collator: collator,
            focusMode: focusMode
        }), [
        keyboardDelegate,
        state.collection,
        state.disabledKeys,
        disabledBehavior,
        ref,
        direction,
        collator,
        focusMode
    ]);
    let { collectionProps: collectionProps } = (0, $lBaOG$reactariaselection.useSelectableCollection)({
        ref: ref,
        selectionManager: manager,
        keyboardDelegate: delegate,
        isVirtualized: isVirtualized,
        scrollRef: scrollRef,
        disallowTypeAhead: disallowTypeAhead,
        escapeKeyBehavior: escapeKeyBehavior
    });
    let id = (0, $lBaOG$reactariautils.useId)(props.id);
    (0, $8ee34951196858d0$exports.gridMap).set(state, {
        keyboardDelegate: delegate,
        actions: {
            onRowAction: onRowAction,
            onCellAction: onCellAction
        },
        shouldSelectOnPressUp: shouldSelectOnPressUp
    });
    let descriptionProps = (0, $340f2fcd0ef9ce8d$exports.useHighlightSelectionDescription)({
        selectionManager: manager,
        hasItemActions: !!(onRowAction || onCellAction)
    });
    let domProps = (0, $lBaOG$reactariautils.filterDOMProps)(props, {
        labelable: true
    });
    let onFocus = (0, $lBaOG$react.useCallback)((e)=>{
        if (manager.isFocused) {
            // If a focus event bubbled through a portal, reset focus state.
            if (!e.currentTarget.contains(e.target)) manager.setFocused(false);
            return;
        }
        // Focus events can bubble through portals. Ignore these events.
        if (!e.currentTarget.contains(e.target)) return;
        manager.setFocused(true);
    }, [
        manager
    ]);
    // Continue to track collection focused state even if keyboard navigation is disabled
    let navDisabledHandlers = (0, $lBaOG$react.useMemo)(()=>({
            onBlur: collectionProps.onBlur,
            onFocus: onFocus
        }), [
        onFocus,
        collectionProps.onBlur
    ]);
    let hasTabbableChild = (0, $lBaOG$reactariafocus.useHasTabbableChild)(ref, {
        isDisabled: state.collection.size !== 0
    });
    let gridProps = (0, $lBaOG$reactariautils.mergeProps)(domProps, {
        role: 'grid',
        id: id,
        'aria-multiselectable': manager.selectionMode === 'multiple' ? 'true' : undefined
    }, state.isKeyboardNavigationDisabled ? navDisabledHandlers : collectionProps, // If collection is empty, make sure the grid is tabbable unless there is a child tabbable element.
    state.collection.size === 0 && {
        tabIndex: hasTabbableChild ? -1 : 0
    } || undefined, descriptionProps);
    if (isVirtualized) {
        gridProps['aria-rowcount'] = state.collection.size;
        gridProps['aria-colcount'] = state.collection.columnCount;
    }
    (0, $1eb174acfe8a0f16$exports.useGridSelectionAnnouncement)({
        getRowText: getRowText
    }, state);
    return {
        gridProps: gridProps
    };
}


//# sourceMappingURL=useGrid.main.js.map
