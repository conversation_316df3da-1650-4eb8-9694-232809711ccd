{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,gCAAI,EAAE,KAAK,IAAI,CAAC,gBAAE,CAAC;IAClE,qBAAqB,CAAC,gFAAU,CAAC;IACjC,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,gEAAQ,CAAC;IACzB,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,wDAAO,CAAC;YAAE,KAAK,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;YAAE,OAAO,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;QAAA,GAAG,QAAC,CAAC;IACzM,gBAAgB,CAAC,OAAS,CAAC,gCAAI,EAAE,KAAK,IAAI,CAAC,gBAAE,CAAC;AAChD", "sources": ["packages/@react-aria/grid/intl/zh-TW.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"未選取「{item}」。\",\n  \"longPressToSelect\": \"長按以進入選擇模式。\",\n  \"select\": \"選取\",\n  \"selectedAll\": \"已選取所有項目。\",\n  \"selectedCount\": \"{count, plural, =0 {未選取任何項目} one {已選取 # 個項目} other {已選取 # 個項目}}。\",\n  \"selectedItem\": \"已選取「{item}」。\"\n}\n"], "names": [], "version": 3, "file": "zh-TW.main.js.map"}