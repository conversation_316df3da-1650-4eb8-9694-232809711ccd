{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;IAClF,qBAAqB,CAAC,gDAA0C,CAAC;IACjE,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,uBAAuB,CAAC;IACxC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4BAA4B,CAAC;YAAE,KAAK,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;YAAE,OAAO,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;QAAA,GAAG,CAAC,CAAC;IACpP,gBAAgB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC;AAC9D", "sources": ["packages/@react-aria/grid/intl/hr-HR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Stavka {item} nije odabrana.\",\n  \"longPressToSelect\": \"Dugo pritisnite za ulazak u način odabira.\",\n  \"select\": \"Odaberite\",\n  \"selectedAll\": \"Odabrane su sve stavke.\",\n  \"selectedCount\": \"{count, plural, =0 {Nije odabrana nijedna stavka} one {Odabrana je # stavka} other {Odabrano je # stavki}}.\",\n  \"selectedItem\": \"Stavka {item} je odabrana.\"\n}\n"], "names": [], "version": 3, "file": "hr-HR.module.js.map"}