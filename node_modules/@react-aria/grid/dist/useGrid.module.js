import {GridKeyboardDelegate as $d1c300d9c497e402$export$de9feff04fda126e} from "./GridKeyboardDelegate.module.js";
import {gridMap as $1af922eb41e03c8f$export$e6235c0d09b995d0} from "./utils.module.js";
import {useGridSelectionAnnouncement as $92599c3fd427b763$export$137e594ef3218a10} from "./useGridSelectionAnnouncement.module.js";
import {useHighlightSelectionDescription as $5b9b5b5723db6ae1$export$be42ebdab07ae4c2} from "./useHighlightSelectionDescription.module.js";
import {useId as $eV0xE$useId, filterDOMProps as $eV0xE$filterDOMProps, mergeProps as $eV0xE$mergeProps} from "@react-aria/utils";
import {useMemo as $eV0xE$useMemo, useCallback as $eV0xE$useCallback} from "react";
import {useCollator as $eV0xE$useCollator, useLocale as $eV0xE$useLocale} from "@react-aria/i18n";
import {useHasTabbableChild as $eV0xE$useHasTabbableChild} from "@react-aria/focus";
import {useSelectableCollection as $eV0xE$useSelectableCollection} from "@react-aria/selection";

/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 








function $83c6e2eafa584c67$export$f6b86a04e5d66d90(props, state, ref) {
    let { isVirtualized: isVirtualized, disallowTypeAhead: disallowTypeAhead, keyboardDelegate: keyboardDelegate, focusMode: focusMode, scrollRef: scrollRef, getRowText: getRowText, onRowAction: onRowAction, onCellAction: onCellAction, escapeKeyBehavior: escapeKeyBehavior = 'clearSelection', shouldSelectOnPressUp: shouldSelectOnPressUp } = props;
    let { selectionManager: manager } = state;
    if (!props['aria-label'] && !props['aria-labelledby']) console.warn('An aria-label or aria-labelledby prop is required for accessibility.');
    // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).
    // When virtualized, the layout object will be passed in as a prop and override this.
    let collator = (0, $eV0xE$useCollator)({
        usage: 'search',
        sensitivity: 'base'
    });
    let { direction: direction } = (0, $eV0xE$useLocale)();
    let disabledBehavior = state.selectionManager.disabledBehavior;
    let delegate = (0, $eV0xE$useMemo)(()=>keyboardDelegate || new (0, $d1c300d9c497e402$export$de9feff04fda126e)({
            collection: state.collection,
            disabledKeys: state.disabledKeys,
            disabledBehavior: disabledBehavior,
            ref: ref,
            direction: direction,
            collator: collator,
            focusMode: focusMode
        }), [
        keyboardDelegate,
        state.collection,
        state.disabledKeys,
        disabledBehavior,
        ref,
        direction,
        collator,
        focusMode
    ]);
    let { collectionProps: collectionProps } = (0, $eV0xE$useSelectableCollection)({
        ref: ref,
        selectionManager: manager,
        keyboardDelegate: delegate,
        isVirtualized: isVirtualized,
        scrollRef: scrollRef,
        disallowTypeAhead: disallowTypeAhead,
        escapeKeyBehavior: escapeKeyBehavior
    });
    let id = (0, $eV0xE$useId)(props.id);
    (0, $1af922eb41e03c8f$export$e6235c0d09b995d0).set(state, {
        keyboardDelegate: delegate,
        actions: {
            onRowAction: onRowAction,
            onCellAction: onCellAction
        },
        shouldSelectOnPressUp: shouldSelectOnPressUp
    });
    let descriptionProps = (0, $5b9b5b5723db6ae1$export$be42ebdab07ae4c2)({
        selectionManager: manager,
        hasItemActions: !!(onRowAction || onCellAction)
    });
    let domProps = (0, $eV0xE$filterDOMProps)(props, {
        labelable: true
    });
    let onFocus = (0, $eV0xE$useCallback)((e)=>{
        if (manager.isFocused) {
            // If a focus event bubbled through a portal, reset focus state.
            if (!e.currentTarget.contains(e.target)) manager.setFocused(false);
            return;
        }
        // Focus events can bubble through portals. Ignore these events.
        if (!e.currentTarget.contains(e.target)) return;
        manager.setFocused(true);
    }, [
        manager
    ]);
    // Continue to track collection focused state even if keyboard navigation is disabled
    let navDisabledHandlers = (0, $eV0xE$useMemo)(()=>({
            onBlur: collectionProps.onBlur,
            onFocus: onFocus
        }), [
        onFocus,
        collectionProps.onBlur
    ]);
    let hasTabbableChild = (0, $eV0xE$useHasTabbableChild)(ref, {
        isDisabled: state.collection.size !== 0
    });
    let gridProps = (0, $eV0xE$mergeProps)(domProps, {
        role: 'grid',
        id: id,
        'aria-multiselectable': manager.selectionMode === 'multiple' ? 'true' : undefined
    }, state.isKeyboardNavigationDisabled ? navDisabledHandlers : collectionProps, // If collection is empty, make sure the grid is tabbable unless there is a child tabbable element.
    state.collection.size === 0 && {
        tabIndex: hasTabbableChild ? -1 : 0
    } || undefined, descriptionProps);
    if (isVirtualized) {
        gridProps['aria-rowcount'] = state.collection.size;
        gridProps['aria-colcount'] = state.collection.columnCount;
    }
    (0, $92599c3fd427b763$export$137e594ef3218a10)({
        getRowText: getRowText
    }, state);
    return {
        gridProps: gridProps
    };
}


export {$83c6e2eafa584c67$export$f6b86a04e5d66d90 as useGrid};
//# sourceMappingURL=useGrid.module.js.map
