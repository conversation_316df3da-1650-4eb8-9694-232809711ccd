{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAoBM,MAAM;IAsBD,OAAO,IAAa,EAAW;QACvC,OAAO,KAAK,IAAI,KAAK;IACvB;IAEU,MAAM,IAAa,EAAW;QACtC,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK;IAC9C;IAEQ,WAAW,IAAmB,EAAE;YACK;QAA3C,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAU,CAAA,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,UAAU,KAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,CAAA;IACrG;IAEU,gBAAgB,OAAa,EAAE,IAAiC,EAAc;QACtF,IAAI,MAAM,WAAW,OACjB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,WAC7B,IAAI,CAAC,UAAU,CAAC,UAAU;QAE9B,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;YAET,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAU,CAAA,CAAC,QAAQ,KAAK,KAAI,GAC/C,OAAO;YAGT,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QACrC;QACA,OAAO;IACT;IAEU,YAAY,OAAa,EAAE,IAAiC,EAAc;QAClF,IAAI,MAAM,WAAW,OACjB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAC5B,IAAI,CAAC,UAAU,CAAC,WAAW;QAE/B,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;YAET,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAU,CAAA,CAAC,QAAQ,KAAK,KAAI,GAC/C,OAAO;YAGT,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;YAClC,IAAI,OAAO,MACT,OAAO;QAEX;QACA,OAAO;IACT;IAEU,0BAA0B,GAAQ,EAAE,QAAgB,CAAC,EAAc;QAC3E,IAAI,QAAQ,GACV,OAAO;QAGT,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,IAAI,IAAI;QACR,KAAK,IAAI,SAAS,CAAA,GAAA,4CAAY,EAAE,MAAM,IAAI,CAAC,UAAU,EAA4B;gBAEtE;YADT,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,GAAG,IAAI,OACvC,OAAO,CAAA,aAAA,MAAM,GAAG,cAAT,wBAAA,aAAa;YAGtB,IAAI,MAAM,OAAO,EACf,IAAI,IAAI,MAAM,OAAO,GAAG;gBAIjB;YADT,IAAI,MAAM,OACR,OAAO,CAAA,cAAA,MAAM,GAAG,cAAT,yBAAA,cAAa;YAGtB;QACF;QACA,OAAO;IACT;IAEA,YAAY,OAAY,EAAc;QACpC,IAAI,MAAkB;QACtB,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;YAKD;QAFR,8DAA8D;QAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,YACd,MAAM,CAAA,uBAAA,UAAU,SAAS,cAAnB,kCAAA,uBAAuB;QAE/B,IAAI,OAAO,MACT,OAAO;QAGT,qBAAqB;QACrB,MAAM,IAAI,CAAC,WAAW,CAAC,KAAM,CAAA,OAAQ,KAAK,IAAI,KAAK;QACnD,IAAI,OAAO,MAAM;YACf,8EAA8E;YAC9E,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;gBAC1B,IAAI,aAAa,UAAU,QAAQ,GAAG,UAAU,QAAQ,GAAG,UAAU,KAAK;gBAC1E,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK;YAC7C;YAEA,gCAAgC;YAChC,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO;QAEX;QACA,OAAO;IACT;IAEA,YAAY,OAAY,EAAc;QACpC,IAAI,MAAkB;QACtB,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;YAKD;QAFR,6DAA6D;QAC7D,IAAI,IAAI,CAAC,MAAM,CAAC,YACd,MAAM,CAAA,uBAAA,UAAU,SAAS,cAAnB,kCAAA,uBAAuB;QAE/B,IAAI,OAAO,MACT,OAAO;QAGT,yBAAyB;QACzB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAA,OAAQ,KAAK,IAAI,KAAK;QACtD,IAAI,OAAO,MAAM;YACf,kFAAkF;YAClF,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;gBAC1B,IAAI,aAAa,UAAU,QAAQ,GAAG,UAAU,QAAQ,GAAG,UAAU,KAAK;gBAC1E,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK;YAC7C;YAEA,oCAAoC;YACpC,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO;QAEX;QACA,OAAO;IACT;IAEA,cAAc,GAAQ,EAAc;QAClC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,oDAAoD;QACpD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;gBAGhB,cACA;YAHJ,IAAI,WAAW,CAAA,GAAA,4CAAY,EAAE,MAAM,IAAI,CAAC,UAAU;gBAC1C;YAAR,OAAO,CAAC,OAAA,IAAI,CAAC,SAAS,KAAK,SACvB,eAAA,CAAA,GAAA,0CAAU,EAAE,uBAAZ,mCAAA,aAAuB,GAAG,IAC1B,gBAAA,CAAA,GAAA,2CAAW,EAAE,uBAAb,oCAAA,cAAwB,GAAG,cAFvB,kBAAA,OAE4B;QACtC;QAEA,qDAAqD;QACrD,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM;YAC/C,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;YACnD,IAAI,CAAC,QACH,OAAO;YAET,IAAI,WAAW,CAAA,GAAA,4CAAY,EAAE,QAAQ,IAAI,CAAC,UAAU;gBACxC;YAAZ,IAAI,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAC3B,CAAA,GAAA,yCAAS,EAAE,UAAU,KAAK,KAAK,GAAG,KAClC,CAAA,GAAA,yCAAS,EAAE,UAAU,KAAK,KAAK,GAAG,gBAF1B,mBAAA,QAEiC;gBAGpC;YADT,IAAI,MACF,OAAO,CAAA,YAAA,KAAK,GAAG,cAAR,uBAAA,YAAY;gBAKZ;YAFT,4CAA4C;YAC5C,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO,CAAA,kBAAA,KAAK,SAAS,cAAd,6BAAA,kBAAkB;gBAGnB;YAAR,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAnE,mBAAA,QAA4E;QACtF;QACA,OAAO;IACT;IAEA,aAAa,GAAQ,EAAc;QACjC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,mDAAmD;QACnD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;gBAGhB,eACA;YAHJ,IAAI,WAAW,CAAA,GAAA,4CAAY,EAAE,MAAM,IAAI,CAAC,UAAU;gBAC1C;YAAR,OAAO,CAAC,OAAA,IAAI,CAAC,SAAS,KAAK,SACvB,gBAAA,CAAA,GAAA,2CAAW,EAAE,uBAAb,oCAAA,cAAwB,GAAG,IAC3B,eAAA,CAAA,GAAA,0CAAU,EAAE,uBAAZ,mCAAA,aAAuB,GAAG,cAFtB,kBAAA,OAE2B;QACrC;QAEA,yDAAyD;QACzD,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM;YAC/C,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;YACnD,IAAI,CAAC,QACH,OAAO;YAET,IAAI,WAAW,CAAA,GAAA,4CAAY,EAAE,QAAQ,IAAI,CAAC,UAAU;gBACxC;YAAZ,IAAI,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAC3B,CAAA,GAAA,yCAAS,EAAE,UAAU,KAAK,KAAK,GAAG,KAClC,CAAA,GAAA,yCAAS,EAAE,UAAU,KAAK,KAAK,GAAG,gBAF1B,mBAAA,QAEiC;gBAGpC;YADT,IAAI,MACF,OAAO,CAAA,YAAA,KAAK,GAAG,cAAR,uBAAA,YAAY;gBAKZ;YAFT,4CAA4C;YAC5C,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO,CAAA,kBAAA,KAAK,SAAS,cAAd,6BAAA,kBAAkB;gBAGnB;YAAR,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAnE,mBAAA,QAA4E;QACtF;QACA,OAAO;IACT;IAEA,YAAY,OAAa,EAAE,MAAgB,EAAc;QACvD,IAAI,MAAkB,oBAAA,qBAAA,UAAW;QACjC,IAAI;QACJ,IAAI,OAAO,MAAM;YACf,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,MACH,OAAO;YAGT,8DAA8D;YAC9D,kDAAkD;YAClD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM;oBAKnD;gBAJP,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;gBACnD,IAAI,CAAC,QACH,OAAO;oBAEF;gBAAP,OAAO,CAAA,qBAAA,gBAAA,CAAA,GAAA,2CAAW,EAAE,CAAA,GAAA,4CAAY,EAAE,QAAQ,IAAI,CAAC,UAAU,gBAAlD,oCAAA,cAAsD,GAAG,cAAzD,+BAAA,oBAA6D;YACtE;QACF;QAEA,qBAAqB;QACrB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAA,OAAQ,KAAK,IAAI,KAAK;QAExD,2FAA2F;QAC3F,IAAI,OAAO,QAAS,CAAA,AAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,UAAW,IAAI,CAAC,SAAS,KAAK,MAAK,GAAI;gBAKjF;YAJN,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;gBAEH;YAAN,MAAM,CAAA,sBAAA,iBAAA,CAAA,GAAA,2CAAW,EAAE,CAAA,GAAA,4CAAY,EAAE,MAAM,IAAI,CAAC,UAAU,gBAAhD,qCAAA,eAAoD,GAAG,cAAvD,gCAAA,qBAA2D;QACnE;QAEA,mCAAmC;QACnC,OAAO;IACT;IAEA,WAAW,OAAa,EAAE,MAAgB,EAAc;QACtD,IAAI,MAAkB,oBAAA,qBAAA,UAAW;QACjC,IAAI;QACJ,IAAI,OAAO,MAAM;YACf,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,MACH,OAAO;YAGT,8DAA8D;YAC9D,iDAAiD;YACjD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM;oBAMnD;gBALP,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;gBACnD,IAAI,CAAC,QACH,OAAO;gBAET,IAAI,WAAW,CAAA,GAAA,4CAAY,EAAE,QAAQ,IAAI,CAAC,UAAU;oBAC7C;gBAAP,OAAO,CAAA,oBAAA,eAAA,CAAA,GAAA,0CAAU,EAAE,uBAAZ,mCAAA,aAAuB,GAAG,cAA1B,8BAAA,mBAA8B;YACvC;QACF;QAEA,oBAAoB;QACpB,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAA,OAAQ,KAAK,IAAI,KAAK;QAE5D,yFAAyF;QACzF,IAAI,OAAO,QAAS,CAAA,AAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,UAAW,IAAI,CAAC,SAAS,KAAK,MAAK,GAAI;gBAMjF;YALN,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;YAET,IAAI,WAAW,CAAA,GAAA,4CAAY,EAAE,MAAM,IAAI,CAAC,UAAU;gBAC5C;YAAN,MAAM,CAAA,qBAAA,gBAAA,CAAA,GAAA,0CAAU,EAAE,uBAAZ,oCAAA,cAAuB,GAAG,cAA1B,+BAAA,oBAA8B;QACtC;QAEA,mCAAmC;QACnC,OAAO;IACT;IAEA,gBAAgB,OAAY,EAAc;QACxC,IAAI,MAAkB;QACtB,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;QAElG,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,OAAO,KAAM;gBAC9C;YAAN,MAAM,CAAA,oBAAA,IAAI,CAAC,WAAW,CAAC,kBAAjB,+BAAA,oBAAyB;YAC/B,IAAI,OAAO,MACT;YAEF,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC7C;QAEA,OAAO;IACT;IAEA,gBAAgB,OAAY,EAAc;QACxC,IAAI,MAAkB;QACtB,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAE/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;QAC5D,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,EAAE,SAAS,CAAC,GAAG;QAE/E,MAAO,YAAY,AAAC,SAAS,CAAC,GAAG,SAAS,MAAM,GAAI,MAAO;YACzD,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;YAC/B,8DAA8D;YAC9D,IAAI,WAAW,MACb;YAGF,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YAC3C,MAAM;QACR;QAEA,OAAO;IACT;IAEA,gBAAgB,MAAc,EAAE,OAAa,EAAc;QACzD,IAAI,MAAkB,oBAAA,qBAAA,UAAW;QACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAChB,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,MAAM,oBAAA,qBAAA,UAAW,IAAI,CAAC,WAAW;QACjC,IAAI,OAAO,MACT,OAAO;QAGT,6DAA6D;QAC7D,IAAI,YAAY,WAAW,OAAO,CAAC;QACnC,IAAI,CAAC,WACH,OAAO;YAGD;QADR,IAAI,UAAU,IAAI,KAAK,QACrB,MAAM,CAAA,uBAAA,UAAU,SAAS,cAAnB,kCAAA,uBAAuB;QAG/B,IAAI,aAAa;QACjB,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,WAAW,OAAO,CAAC;YAC9B,IAAI,CAAC,MACH,OAAO;YAGT,iCAAiC;YACjC,IAAI,KAAK,SAAS,EAAE;gBAClB,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;gBACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GAAG;wBAEzC;wBAAA;oBADT,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,SAAS,KAAK,QACzC,OAAO,CAAA,qBAAA,gBAAA,CAAA,GAAA,2CAAW,EAAE,CAAA,GAAA,4CAAY,EAAE,MAAM,IAAI,CAAC,UAAU,gBAAhD,oCAAA,cAAoD,GAAG,cAAvD,+BAAA,oBAA2D;oBAGpE,OAAO,KAAK,GAAG;gBACjB;YACF;YAEA,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAA,OAAQ,KAAK,IAAI,KAAK;YAElD,sDAAsD;YACtD,IAAI,OAAO,QAAQ,CAAC,YAAY;gBAC9B,MAAM,IAAI,CAAC,WAAW;gBACtB,aAAa;YACf;QACF;QAEA,OAAO;IACT;IAxZA,YAAY,OAAuC,CAAE;QACnD,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;QACpC,IAAI,CAAC,YAAY,GAAG,QAAQ,YAAY;QACxC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,gBAAgB,IAAI;QACpD,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;QAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;QAChC,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,GAAG,EACjC,MAAM,IAAI,MAAM;QAElB,IAAI,CAAC,cAAc,GAAG,QAAQ,cAAc,IAAK,CAAA,QAAQ,MAAM,GAAG,IAAI,+CAAyB,QAAQ,MAAM,IAAI,IAAI,CAAA,GAAA,2CAAgB,EAAE,QAAQ,GAAG,CAAC;YAClI;QAAjB,IAAI,CAAC,SAAS,GAAG,CAAA,qBAAA,QAAQ,SAAS,cAAjB,gCAAA,qBAAqB;IACxC;AA8YF;AAiBA,MAAM;IAOJ,iBAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc;IACnC;IAEA,YAAY,GAAQ,EAAe;YAC1B;QAAP,OAAO,EAAA,6BAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,kBAA1B,iDAAA,2BAAgC,IAAI,KAAI;IACjD;IAEA,iBAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW;IAC5C;IAdA,YAAY,MAAwB,CAAE;QACpC,IAAI,CAAC,MAAM,GAAG;IAChB;AAaF", "sources": ["packages/@react-aria/grid/src/GridKeyboardDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Direction, DisabledBehavior, Key, KeyboardDelegate, LayoutDelegate, Node, Rect, RefObject, Size} from '@react-types/shared';\nimport {DOMLayoutDelegate} from '@react-aria/selection';\nimport {getChildNodes, getFirstItem, getLastItem, getNthItem} from '@react-stately/collections';\nimport {GridCollection, GridNode} from '@react-types/grid';\n\nexport interface GridKeyboardDelegateOptions<C> {\n  collection: C,\n  disabledKeys: Set<Key>,\n  disabledBehavior?: DisabledBehavior,\n  ref?: RefObject<HTMLElement | null>,\n  direction: Direction,\n  collator?: Intl.Collator,\n  layoutDelegate?: LayoutDelegate,\n  /** @deprecated - Use layoutDelegate instead. */\n  layout?: DeprecatedLayout,\n  focusMode?: 'row' | 'cell'\n}\n\nexport class GridKeyboardDelegate<T, C extends GridCollection<T>> implements KeyboardDelegate {\n  collection: C;\n  protected disabledKeys: Set<Key>;\n  protected disabledBehavior: DisabledBehavior;\n  protected direction: Direction;\n  protected collator: Intl.Collator | undefined;\n  protected layoutDelegate: LayoutDelegate;\n  protected focusMode: 'row' | 'cell';\n\n  constructor(options: GridKeyboardDelegateOptions<C>) {\n    this.collection = options.collection;\n    this.disabledKeys = options.disabledKeys;\n    this.disabledBehavior = options.disabledBehavior || 'all';\n    this.direction = options.direction;\n    this.collator = options.collator;\n    if (!options.layout && !options.ref) {\n      throw new Error('Either a layout or a ref must be specified.');\n    }\n    this.layoutDelegate = options.layoutDelegate || (options.layout ? new DeprecatedLayoutDelegate(options.layout) : new DOMLayoutDelegate(options.ref!));\n    this.focusMode = options.focusMode ?? 'row';\n  }\n\n  protected isCell(node: Node<T>): boolean {\n    return node.type === 'cell';\n  }\n\n  protected isRow(node: Node<T>): boolean {\n    return node.type === 'row' || node.type === 'item';\n  }\n\n  private isDisabled(item: Node<unknown>) {\n    return this.disabledBehavior === 'all' && (item.props?.isDisabled || this.disabledKeys.has(item.key));\n  }\n\n  protected findPreviousKey(fromKey?: Key, pred?: (item: Node<T>) => boolean): Key | null {\n    let key = fromKey != null\n      ? this.collection.getKeyBefore(fromKey)\n      : this.collection.getLastKey();\n\n    while (key != null) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      if (!this.isDisabled(item) && (!pred || pred(item))) {\n        return key;\n      }\n\n      key = this.collection.getKeyBefore(key);\n    }\n    return null;\n  }\n\n  protected findNextKey(fromKey?: Key, pred?: (item: Node<T>) => boolean): Key | null {\n    let key = fromKey != null\n      ? this.collection.getKeyAfter(fromKey)\n      : this.collection.getFirstKey();\n\n    while (key != null) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      if (!this.isDisabled(item) && (!pred || pred(item))) {\n        return key;\n      }\n\n      key = this.collection.getKeyAfter(key);\n      if (key == null) {\n        return null;\n      }\n    }\n    return null;\n  }\n\n  protected getKeyForItemInRowByIndex(key: Key, index: number = 0): Key | null {\n    if (index < 0) {\n      return null;\n    }\n\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    let i = 0;\n    for (let child of getChildNodes(item, this.collection) as Iterable<GridNode<T>>) {\n      if (child.colSpan && child.colSpan + i > index) {\n        return child.key ?? null;\n      }\n\n      if (child.colSpan) {\n        i = i + child.colSpan - 1;\n      }\n\n      if (i === index) {\n        return child.key ?? null;\n      }\n\n      i++;\n    }\n    return null;\n  }\n\n  getKeyBelow(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus was on a cell, start searching from the parent row\n    if (this.isCell(startItem)) {\n      key = startItem.parentKey ?? null;\n    }\n    if (key == null) {\n      return null;\n    }\n\n    // Find the next item\n    key = this.findNextKey(key, (item => item.type === 'item'));\n    if (key != null) {\n      // If focus was on a cell, focus the cell with the same index in the next row.\n      if (this.isCell(startItem)) {\n        let startIndex = startItem.colIndex ? startItem.colIndex : startItem.index;\n        return this.getKeyForItemInRowByIndex(key, startIndex);\n      }\n\n      // Otherwise, focus the next row\n      if (this.focusMode === 'row') {\n        return key;\n      }\n    }\n    return null;\n  }\n\n  getKeyAbove(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus is on a cell, start searching from the parent row\n    if (this.isCell(startItem)) {\n      key = startItem.parentKey ?? null;\n    }\n    if (key == null) {\n      return null;\n    }\n\n    // Find the previous item\n    key = this.findPreviousKey(key, item => item.type === 'item');\n    if (key != null) {\n      // If focus was on a cell, focus the cell with the same index in the previous row.\n      if (this.isCell(startItem)) {\n        let startIndex = startItem.colIndex ? startItem.colIndex : startItem.index;\n        return this.getKeyForItemInRowByIndex(key, startIndex);\n      }\n\n      // Otherwise, focus the previous row\n      if (this.focusMode === 'row') {\n        return key;\n      }\n    }\n    return null;\n  }\n\n  getKeyRightOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus is on a row, focus the first child cell.\n    if (this.isRow(item)) {\n      let children = getChildNodes(item, this.collection);\n      return (this.direction === 'rtl'\n        ? getLastItem(children)?.key\n        : getFirstItem(children)?.key) ?? null;\n    }\n\n    // If focus is on a cell, focus the next cell if any,\n    // otherwise focus the parent row.\n    if (this.isCell(item) && item.parentKey != null) {\n      let parent = this.collection.getItem(item.parentKey);\n      if (!parent) {\n        return null;\n      }\n      let children = getChildNodes(parent, this.collection);\n      let next = (this.direction === 'rtl'\n        ? getNthItem(children, item.index - 1)\n        : getNthItem(children, item.index + 1)) ?? null;\n\n      if (next) {\n        return next.key ?? null;\n      }\n\n      // focus row only if focusMode is set to row\n      if (this.focusMode === 'row') {\n        return item.parentKey ?? null;\n      }\n\n      return (this.direction === 'rtl' ? this.getFirstKey(key) : this.getLastKey(key)) ?? null;\n    }\n    return null;\n  }\n\n  getKeyLeftOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus is on a row, focus the last child cell.\n    if (this.isRow(item)) {\n      let children = getChildNodes(item, this.collection);\n      return (this.direction === 'rtl'\n        ? getFirstItem(children)?.key\n        : getLastItem(children)?.key) ?? null;\n    }\n\n    // If focus is on a cell, focus the previous cell if any,\n    // otherwise focus the parent row.\n    if (this.isCell(item) && item.parentKey != null) {\n      let parent = this.collection.getItem(item.parentKey);\n      if (!parent) {\n        return null;\n      }\n      let children = getChildNodes(parent, this.collection);\n      let prev = (this.direction === 'rtl'\n        ? getNthItem(children, item.index + 1)\n        : getNthItem(children, item.index - 1)) ?? null;\n\n      if (prev) {\n        return prev.key ?? null;\n      }\n\n      // focus row only if focusMode is set to row\n      if (this.focusMode === 'row') {\n        return item.parentKey ?? null;\n      }\n\n      return (this.direction === 'rtl' ? this.getLastKey(key) : this.getFirstKey(key)) ?? null;\n    }\n    return null;\n  }\n\n  getFirstKey(fromKey?: Key, global?: boolean): Key | null {\n    let key: Key | null = fromKey ?? null;\n    let item: Node<T> | undefined | null;\n    if (key != null) {\n      item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      // If global flag is not set, and a cell is currently focused,\n      // move focus to the first cell in the parent row.\n      if (this.isCell(item) && !global && item.parentKey != null) {\n        let parent = this.collection.getItem(item.parentKey);\n        if (!parent) {\n          return null;\n        }\n        return getFirstItem(getChildNodes(parent, this.collection))?.key ?? null;\n      }\n    }\n\n    // Find the first row\n    key = this.findNextKey(undefined, item => item.type === 'item');\n\n    // If global flag is set (or if focus mode is cell), focus the first cell in the first row.\n    if (key != null && ((item && this.isCell(item) && global) || this.focusMode === 'cell')) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      key = getFirstItem(getChildNodes(item, this.collection))?.key ?? null;\n    }\n\n    // Otherwise, focus the row itself.\n    return key;\n  }\n\n  getLastKey(fromKey?: Key, global?: boolean): Key | null {\n    let key: Key | null = fromKey ?? null;\n    let item: Node<T> | undefined | null;\n    if (key != null) {\n      item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      // If global flag is not set, and a cell is currently focused,\n      // move focus to the last cell in the parent row.\n      if (this.isCell(item) && !global && item.parentKey != null) {\n        let parent = this.collection.getItem(item.parentKey);\n        if (!parent) {\n          return null;\n        }\n        let children = getChildNodes(parent, this.collection);\n        return getLastItem(children)?.key ?? null;\n      }\n    }\n\n    // Find the last row\n    key = this.findPreviousKey(undefined, item => item.type === 'item');\n\n    // If global flag is set (or if focus mode is cell), focus the last cell in the last row.\n    if (key != null && ((item && this.isCell(item) && global) || this.focusMode === 'cell')) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      let children = getChildNodes(item, this.collection);\n      key = getLastItem(children)?.key ?? null;\n    }\n\n    // Otherwise, focus the row itself.\n    return key;\n  }\n\n  getKeyPageAbove(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n    if (!itemRect) {\n      return null;\n    }\n\n    let pageY = Math.max(0, itemRect.y + itemRect.height - this.layoutDelegate.getVisibleRect().height);\n\n    while (itemRect && itemRect.y > pageY && key != null) {\n      key = this.getKeyAbove(key) ?? null;\n      if (key == null) {\n        break;\n      }\n      itemRect = this.layoutDelegate.getItemRect(key);\n    }\n\n    return key;\n  }\n\n  getKeyPageBelow(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n\n    if (!itemRect) {\n      return null;\n    }\n\n    let pageHeight = this.layoutDelegate.getVisibleRect().height;\n    let pageY = Math.min(this.layoutDelegate.getContentSize().height, itemRect.y + pageHeight);\n\n    while (itemRect && (itemRect.y + itemRect.height) < pageY) {\n      let nextKey = this.getKeyBelow(key);\n      // If nextKey is undefined, we've reached the last row already\n      if (nextKey == null) {\n        break;\n      }\n\n      itemRect = this.layoutDelegate.getItemRect(nextKey);\n      key = nextKey;\n    }\n\n    return key;\n  }\n\n  getKeyForSearch(search: string, fromKey?: Key): Key | null {\n    let key: Key | null = fromKey ?? null;\n    if (!this.collator) {\n      return null;\n    }\n\n    let collection = this.collection;\n    key = fromKey ?? this.getFirstKey();\n    if (key == null) {\n      return null;\n    }\n\n    // If the starting key is a cell, search from its parent row.\n    let startItem = collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n    if (startItem.type === 'cell') {\n      key = startItem.parentKey ?? null;\n    }\n\n    let hasWrapped = false;\n    while (key != null) {\n      let item = collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      // check row text value for match\n      if (item.textValue) {\n        let substring = item.textValue.slice(0, search.length);\n        if (this.collator.compare(substring, search) === 0) {\n          if (this.isRow(item) && this.focusMode === 'cell') {\n            return getFirstItem(getChildNodes(item, this.collection))?.key ?? null;\n          }\n\n          return item.key;\n        }\n      }\n\n      key = this.findNextKey(key, item => item.type === 'item');\n\n      // Wrap around when reaching the end of the collection\n      if (key == null && !hasWrapped) {\n        key = this.getFirstKey();\n        hasWrapped = true;\n      }\n    }\n\n    return null;\n  }\n}\n\n/* Backward compatibility for old Virtualizer Layout interface. */\ninterface DeprecatedLayout {\n  getLayoutInfo(key: Key): DeprecatedLayoutInfo,\n  getContentSize(): Size,\n  virtualizer: DeprecatedVirtualizer\n}\n\ninterface DeprecatedLayoutInfo {\n  rect: Rect\n}\n\ninterface DeprecatedVirtualizer {\n  visibleRect: Rect\n}\n\nclass DeprecatedLayoutDelegate implements LayoutDelegate {\n  layout: DeprecatedLayout;\n\n  constructor(layout: DeprecatedLayout) {\n    this.layout = layout;\n  }\n\n  getContentSize(): Size {\n    return this.layout.getContentSize();\n  }\n\n  getItemRect(key: Key): Rect | null {\n    return this.layout.getLayoutInfo(key)?.rect || null;\n  }\n\n  getVisibleRect(): Rect {\n    return this.layout.virtualizer.visibleRect;\n  }\n}\n"], "names": [], "version": 3, "file": "GridKeyboardDelegate.main.js.map"}