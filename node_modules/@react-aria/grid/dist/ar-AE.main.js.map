{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iEAAW,CAAC;IACvE,qBAAqB,CAAC,iNAAmC,CAAC;IAC1D,UAAU,CAAC,mCAAK,CAAC;IACjB,eAAe,CAAC,iIAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4GAAkB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0DAAU,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0DAAU,CAAC;QAAA,GAAG,CAAC,CAAC;IACxN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,2CAAO,CAAC;AACjD", "sources": ["packages/@react-aria/grid/intl/ar-AE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} غير المحدد\",\n  \"longPressToSelect\": \"اضغط مطولًا للدخول إلى وضع التحديد.\",\n  \"select\": \"تحديد\",\n  \"selectedAll\": \"جميع العناصر المحددة.\",\n  \"selectedCount\": \"{count, plural, =0 {لم يتم تحديد عناصر} one {# عنصر محدد} other {# عنصر محدد}}.\",\n  \"selectedItem\": \"{item} المحدد\"\n}\n"], "names": [], "version": 3, "file": "ar-AE.main.js.map"}