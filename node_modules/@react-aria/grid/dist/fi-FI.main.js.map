{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;IACjF,qBAAqB,CAAC,8CAAwC,CAAC;IAC/D,UAAU,CAAC,OAAO,CAAC;IACnB,eAAe,CAAC,uBAAuB,CAAC;IACxC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,gCAA0B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC3O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;AACnD", "sources": ["packages/@react-aria/grid/intl/fi-FI.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"<PERSON><PERSON><PERSON><PERSON> {item} ei valittu.\",\n  \"longPressToSelect\": \"<PERSON><PERSON><PERSON> valintatilaan painamalla pitkään.\",\n  \"select\": \"<PERSON><PERSON><PERSON>\",\n  \"selectedAll\": \"<PERSON><PERSON><PERSON> kohteet valittu.\",\n  \"selectedCount\": \"{count, plural, =0 {Ei yhtään kohdetta valittu} one {# kohde valittu} other {# kohdetta valittu}}.\",\n  \"selectedItem\": \"{item} valittu.\"\n}\n"], "names": [], "version": 3, "file": "fi-FI.main.js.map"}