{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,oBAAc,CAAC;IAClF,qBAAqB,CAAC,qEAAmD,CAAC;IAC1E,UAAU,CAAC,aAAO,CAAC;IACnB,eAAe,CAAC,4BAAsB,CAAC;IACvC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,kCAA4B,CAAC;YAAE,KAAK,IAAM,CAAC,+BAAyB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;YAAE,OAAO,IAAM,CAAC,+BAAyB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;QAAA,GAAG,CAAC,CAAC;IAChQ,gBAAgB,CAAC,OAAS,CAAC,uBAAiB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAC5D", "sources": ["packages/@react-aria/grid/intl/lv-LV.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Vienums {item} nav atlasīts.\",\n  \"longPressToSelect\": \"Ilgi turiet nospiestu. lai ieslēgtu atlases režīmu.\",\n  \"select\": \"Atlasīt\",\n  \"selectedAll\": \"Atlasīti visi vienumi.\",\n  \"selectedCount\": \"{count, plural, =0 {Nav atlasīts neviens vienums} one {Atlasīto vienumu skaits: #} other {Atlasīto vienumu skaits: #}}.\",\n  \"selectedItem\": \"Atlasīts vienums {item}.\"\n}\n"], "names": [], "version": 3, "file": "lv-LV.main.js.map"}