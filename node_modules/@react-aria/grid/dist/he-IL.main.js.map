{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,6CAAS,CAAC;IACrE,qBAAqB,CAAC,6KAA6B,CAAC;IACpD,UAAU,CAAC,qBAAG,CAAC;IACf,eAAe,CAAC,qGAAiB,CAAC;IAClC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,6FAAe,CAAC;YAAE,KAAK,IAAM,CAAC,6BAAK,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6BAAK,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,+EAAa,CAAC;QAAA,GAAG,CAAC,CAAC;IACxN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,8BAAM,CAAC;AAChD", "sources": ["packages/@react-aria/grid/intl/he-IL.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} לא נבחר.\",\n  \"longPressToSelect\": \"הקשה ארוכה לכניסה למצב בחירה.\",\n  \"select\": \"בחר\",\n  \"selectedAll\": \"כל הפריטים נבחרו.\",\n  \"selectedCount\": \"{count, plural, =0 {לא נבחרו פריטים} one {פריט # נבחר} other {# פריטים נבחרו}}.\",\n  \"selectedItem\": \"{item} נבחר.\"\n}\n"], "names": [], "version": 3, "file": "he-IL.main.js.map"}