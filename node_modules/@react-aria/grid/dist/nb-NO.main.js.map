{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,eAAe,CAAC;IAC3E,qBAAqB,CAAC,mDAA6C,CAAC;IACpE,UAAU,CAAC,IAAI,CAAC;IAChB,eAAe,CAAC,wBAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,wBAAwB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC9O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;AACpD", "sources": ["packages/@react-aria/grid/intl/nb-NO.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} er ikke valgt.\",\n  \"longPressToSelect\": \"Bruk et langt trykk for å gå inn i valgmodus.\",\n  \"select\": \"Velg\",\n  \"selectedAll\": \"Alle elementer er valgt.\",\n  \"selectedCount\": \"{count, plural, =0 {Ingen elementer er valgt} one {# element er valgt} other {# elementer er valgt}}.\",\n  \"selectedItem\": \"{item} er valgt.\"\n}\n"], "names": [], "version": 3, "file": "nb-NO.main.js.map"}