{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,uBAAiB,CAAC;IAC7E,qBAAqB,CAAC,uEAA8D,CAAC;IACrF,UAAU,CAAC,eAAY,CAAC;IACxB,eAAe,CAAC,2CAA+B,CAAC;IAChD,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qCAAyB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,gCAAoB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kCAAsB,CAAC;QAAA,GAAG,CAAC,CAAC;IACrP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mBAAa,CAAC;AACvD", "sources": ["packages/@react-aria/grid/intl/fr-FR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} non sélectionné.\",\n  \"longPressToSelect\": \"Appuyez de manière prolongée pour passer en mode de sélection.\",\n  \"select\": \"Sélectionner\",\n  \"selectedAll\": \"Tous les éléments sélectionnés.\",\n  \"selectedCount\": \"{count, plural, =0 {Aucun élément sélectionné} one {# élément sélectionné} other {# éléments sélectionnés}}.\",\n  \"selectedItem\": \"{item} sélectionné.\"\n}\n"], "names": [], "version": 3, "file": "fr-FR.module.js.map"}