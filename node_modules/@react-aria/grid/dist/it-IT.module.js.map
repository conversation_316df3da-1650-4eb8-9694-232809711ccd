{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iBAAiB,CAAC;IAC7E,qBAAqB,CAAC,wDAAqD,CAAC;IAC5E,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,+BAA+B,CAAC;IAChD,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,2BAA2B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,qBAAqB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,qBAAqB,CAAC;QAAA,GAAG,CAAC,CAAC;IACvP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;AACvD", "sources": ["packages/@react-aria/grid/intl/it-IT.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} non selezionato.\",\n  \"longPressToSelect\": \"Premi a lungo per passare alla modalità di selezione.\",\n  \"select\": \"Seleziona\",\n  \"selectedAll\": \"Tutti gli elementi selezionati.\",\n  \"selectedCount\": \"{count, plural, =0 {Nessun elemento selezionato} one {# elemento selezionato} other {# elementi selezionati}}.\",\n  \"selectedItem\": \"{item} selezionato.\"\n}\n"], "names": [], "version": 3, "file": "it-IT.module.js.map"}