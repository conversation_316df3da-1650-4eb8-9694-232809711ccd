{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,kEAAY,CAAC;IACxE,qBAAqB,CAAC,2UAAuD,CAAC;IAC9E,UAAU,CAAC,iDAAO,CAAC;IACnB,eAAe,CAAC,iIAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,2KAA2B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,oGAAgB,CAAC;YAAE,OAAO,IAAM,CAAC,mHAAmB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;QAAA,GAAG,CAAC,CAAC;IAChP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mDAAS,CAAC;AACnD", "sources": ["packages/@react-aria/grid/intl/uk-UA.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} не вибрано.\",\n  \"longPressToSelect\": \"Виконайте довге натиснення, щоб перейти в режим вибору.\",\n  \"select\": \"Вибрати\",\n  \"selectedAll\": \"Усі елементи вибрано.\",\n  \"selectedCount\": \"{count, plural, =0 {Жодних елементів не вибрано} one {# елемент вибрано} other {Вибрано елементів: #}}.\",\n  \"selectedItem\": \"{item} вибрано.\"\n}\n"], "names": [], "version": 3, "file": "uk-UA.module.js.map"}