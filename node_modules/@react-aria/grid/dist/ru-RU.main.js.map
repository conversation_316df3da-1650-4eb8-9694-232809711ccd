{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,kEAAY,CAAC;IACxE,qBAAqB,CAAC,yRAA+C,CAAC;IACtE,UAAU,CAAC,iDAAO,CAAC;IACnB,eAAe,CAAC,iIAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qJAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kHAAkB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC1O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mDAAS,CAAC;AACnD", "sources": ["packages/@react-aria/grid/intl/ru-RU.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} не выбрано.\",\n  \"longPressToSelect\": \"Нажмите и удерживайте для входа в режим выбора.\",\n  \"select\": \"Выбрать\",\n  \"selectedAll\": \"Выбраны все элементы.\",\n  \"selectedCount\": \"{count, plural, =0 {Нет выбранных элементов} one {# элемент выбран} other {# элементов выбрано}}.\",\n  \"selectedItem\": \"{item} выбрано.\"\n}\n"], "names": [], "version": 3, "file": "ru-RU.main.js.map"}