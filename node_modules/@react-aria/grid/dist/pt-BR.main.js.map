{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,oBAAiB,CAAC;IAC7E,qBAAqB,CAAC,0DAAoD,CAAC;IAC3E,UAAU,CAAC,UAAU,CAAC;IACtB,eAAe,CAAC,4BAA4B,CAAC;IAC7C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC7O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;AACvD", "sources": ["packages/@react-aria/grid/intl/pt-BR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} não selecionado.\",\n  \"longPressToSelect\": \"Mantenha pressionado para entrar no modo de seleção.\",\n  \"select\": \"Selecionar\",\n  \"selectedAll\": \"Todos os itens selecionados.\",\n  \"selectedCount\": \"{count, plural, =0 {Nenhum item selecionado} one {# item selecionado} other {# itens selecionados}}.\",\n  \"selectedItem\": \"{item} selecionado.\"\n}\n"], "names": [], "version": 3, "file": "pt-BR.main.js.map"}