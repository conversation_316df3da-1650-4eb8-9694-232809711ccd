{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mEAAa,CAAC;IACzE,qBAAqB,CAAC,2UAAuD,CAAC;IAC9E,UAAU,CAAC,wDAAQ,CAAC;IACpB,eAAe,CAAC,qKAA2B,CAAC;IAC5C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uIAAqB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,2GAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IACvO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,4CAAQ,CAAC;AAClD", "sources": ["packages/@react-aria/grid/intl/bg-BG.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} не е избран.\",\n  \"longPressToSelect\": \"Натиснете и задръжте за да влезете в избирателен режим.\",\n  \"select\": \"Изберете\",\n  \"selectedAll\": \"Всички елементи са избрани.\",\n  \"selectedCount\": \"{count, plural, =0 {Няма избрани елементи} one {# избран елемент} other {# избрани елементи}}.\",\n  \"selectedItem\": \"{item} избран.\"\n}\n"], "names": [], "version": 3, "file": "bg-BG.main.js.map"}