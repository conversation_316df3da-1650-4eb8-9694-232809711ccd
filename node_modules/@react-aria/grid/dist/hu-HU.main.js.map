{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,oBAAiB,CAAC;IAC7E,qBAAqB,CAAC,oCAA8B,CAAC;IACrD,UAAU,CAAC,eAAS,CAAC;IACrB,eAAe,CAAC,+BAAyB,CAAC;IAC1C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,2BAAwB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAe,CAAC;QAAA,GAAG,CAAC,CAAC;IACxO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAW,CAAC;AACrD", "sources": ["packages/@react-aria/grid/intl/hu-HU.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nincs kijelölve.\",\n  \"longPressToSelect\": \"Nyomja hosszan a kijelöléshez.\",\n  \"select\": \"Ki<PERSON>lölés\",\n  \"selectedAll\": \"Az összes elem kijelölve.\",\n  \"selectedCount\": \"{count, plural, =0 {Egy elem sincs kijelölve} one {# elem kijelölve} other {# elem kijelölve}}.\",\n  \"selectedItem\": \"{item} kijelölve.\"\n}\n"], "names": [], "version": 3, "file": "hu-HU.main.js.map"}