{"mappings": ";;;;;;;;;;;;;;;;;AAyBO,SAAS,0CAAyD,KAAqC,EAAE,KAAsB;IACpI,IAAI,OAAC,GAAG,EAAC,GAAG;IAEZ,IAAI,UAAU,MAAM,gBAAgB;IACpC,IAAI,aAAa,CAAA,GAAA,2BAAI;IACrB,IAAI,aAAa,CAAC,MAAM,gBAAgB,CAAC,aAAa,CAAC;IACvD,IAAI,aAAa,MAAM,gBAAgB,CAAC,UAAU,CAAC;IAEnD,4EAA4E;IAC5E,IAAI,WAAW,IAAM,QAAQ,eAAe,CAAC;IAE7C,MAAM,kBAAkB,CAAA,GAAA,gDAA0B,EAAE,CAAA,GAAA,mDAAW,GAAG;IAElE,OAAO;QACL,eAAe;YACb,IAAI;YACJ,cAAc,gBAAgB,MAAM,CAAC;wBACrC;wBACA;sBACA;QACF;IACF;AACF", "sources": ["packages/@react-aria/grid/src/useGridSelectionCheckbox.ts"], "sourcesContent": ["import {AriaCheckboxProps} from '@react-types/checkbox';\nimport {GridCollection} from '@react-types/grid';\nimport {GridState} from '@react-stately/grid';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {Key} from '@react-types/shared';\nimport {useId} from '@react-aria/utils';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaGridSelectionCheckboxProps {\n  /** A unique key for the checkbox. */\n  key: Key\n}\n\nexport interface GridSelectionCheckboxAria {\n  /** Props for the row selection checkbox element. */\n  checkboxProps: AriaCheckboxProps\n}\n\n\n/**\n * Provides the behavior and accessibility implementation for a selection checkbox in a grid.\n * @param props - Props for the selection checkbox.\n * @param state - State of the grid, as returned by `useGridState`.\n */\nexport function useGridSelectionCheckbox<T, C extends GridCollection<T>>(props: AriaGridSelectionCheckboxProps, state: GridState<T, C>): GridSelectionCheckboxAria {\n  let {key} = props;\n\n  let manager = state.selectionManager;\n  let checkboxId = useId();\n  let isDisabled = !state.selectionManager.canSelectItem(key);\n  let isSelected = state.selectionManager.isSelected(key);\n\n  // Checkbox should always toggle selection, regardless of selectionBehavior.\n  let onChange = () => manager.toggleSelection(key);\n\n  const stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/grid');\n\n  return {\n    checkboxProps: {\n      id: checkboxId,\n      'aria-label': stringFormatter.format('select'),\n      isSelected,\n      isDisabled,\n      onChange\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useGridSelectionCheckbox.main.js.map"}