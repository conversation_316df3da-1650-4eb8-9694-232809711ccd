{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,eAAe,CAAC;IAC3E,qBAAqB,CAAC,gDAA0C,CAAC;IACjE,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,uBAAuB,CAAC;IACxC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4BAA4B,CAAC;YAAE,KAAK,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;YAAE,OAAO,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;QAAA,GAAG,CAAC,CAAC;IACpP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;AACvD", "sources": ["packages/@react-aria/grid/intl/sr-SP.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nije izabrano.\",\n  \"longPressToSelect\": \"Dugo pritisnite za ulazak u režim biranja.\",\n  \"select\": \"Izaberite\",\n  \"selectedAll\": \"<PERSON>zabrane su sve stavke.\",\n  \"selectedCount\": \"{count, plural, =0 {Nije izabrana nijedna stavka} one {Izabrana je # stavka} other {Izabrano je # stavki}}.\",\n  \"selectedItem\": \"{item} je izabrano.\"\n}\n"], "names": [], "version": 3, "file": "sr-SP.module.js.map"}