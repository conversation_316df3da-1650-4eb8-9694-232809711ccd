{"mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;AAoBM,SAAS,0CAAiC,KAAyC;IACxF,IAAI,kBAAkB,CAAA,GAAA,gDAA0B,EAAE,CAAA,GAAA,mDAAW,GAAG;IAChE,IAAI,WAAW,CAAA,GAAA,mDAAqB;IACpC,sGAAsG;IACtG,IAAI,kBAAkB,AAAC,CAAA,aAAa,aAAa,aAAa,aAAa,YAAY,IAAG,KACrF,OAAO,WAAW,eAAe,kBAAkB;IAExD,IAAI,yBAAyB,CAAA,GAAA,oBAAM,EAAE;QACnC,IAAI,gBAAgB,MAAM,gBAAgB,CAAC,aAAa;QACxD,IAAI,oBAAoB,MAAM,gBAAgB,CAAC,iBAAiB;QAEhE,IAAI;QACJ,IAAI,iBACF,UAAU,gBAAgB,MAAM,CAAC;QAGnC,OAAO,sBAAsB,aAAa,kBAAkB,UAAU,MAAM,cAAc,GAAG,UAAU;IACzG,GAAG;QAAC,MAAM,gBAAgB,CAAC,aAAa;QAAE,MAAM,gBAAgB,CAAC,iBAAiB;QAAE,MAAM,cAAc;QAAE;QAAiB;KAAgB;IAE3I,IAAI,mBAAmB,CAAA,GAAA,oCAAa,EAAE;IACtC,OAAO;AACT", "sources": ["packages/@react-aria/grid/src/useHighlightSelectionDescription.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps} from '@react-types/shared';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {MultipleSelectionManager} from '@react-stately/selection';\nimport {useDescription} from '@react-aria/utils';\nimport {useInteractionModality} from '@react-aria/interactions';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useMemo} from 'react';\n\nexport interface HighlightSelectionDescriptionProps {\n  selectionManager: MultipleSelectionManager,\n  hasItemActions?: boolean\n}\n\n/**\n * Computes the description for a grid selectable collection.\n * @param props\n */\nexport function useHighlightSelectionDescription(props: HighlightSelectionDescriptionProps): AriaLabelingProps {\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/grid');\n  let modality = useInteractionModality();\n  // null is the default if the user hasn't interacted with the table at all yet or the rest of the page\n  let shouldLongPress = (modality === 'pointer' || modality === 'virtual' || modality == null)\n    && typeof window !== 'undefined' && 'ontouchstart' in window;\n\n  let interactionDescription = useMemo(() => {\n    let selectionMode = props.selectionManager.selectionMode;\n    let selectionBehavior = props.selectionManager.selectionBehavior;\n\n    let message: string | undefined;\n    if (shouldLongPress) {\n      message = stringFormatter.format('longPressToSelect');\n    }\n\n    return selectionBehavior === 'replace' && selectionMode !== 'none' && props.hasItemActions ? message : undefined;\n  }, [props.selectionManager.selectionMode, props.selectionManager.selectionBehavior, props.hasItemActions, stringFormatter, shouldLongPress]);\n\n  let descriptionProps = useDescription(interactionDescription);\n  return descriptionProps;\n}\n"], "names": [], "version": 3, "file": "useHighlightSelectionDescription.main.js.map"}