{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,yFAAY,CAAC;IACxE,qBAAqB,CAAC,gIAAgB,CAAC;IACvC,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,gHAAc,CAAC;IAC/B,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,gGAAY,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yEAAU,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yEAAU,CAAC;QAAA,GAAG,QAAC,CAAC;IAClN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iEAAS,CAAC;AACnD", "sources": ["packages/@react-aria/grid/intl/ja-JP.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} が選択されていません。\",\n  \"longPressToSelect\": \"長押しして選択モードを開きます。\",\n  \"select\": \"選択\",\n  \"selectedAll\": \"すべての項目を選択しました。\",\n  \"selectedCount\": \"{count, plural, =0 {項目が選択されていません} one {# 項目を選択しました} other {# 項目を選択しました}}。\",\n  \"selectedItem\": \"{item} を選択しました。\"\n}\n"], "names": [], "version": 3, "file": "ja-JP.main.js.map"}