{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;;;AAuEM,SAAS,0CAAW,KAAgB,EAAE,KAAsC,EAAE,GAAkC;IACrH,IAAI,iBACF,aAAa,qBACb,iBAAiB,oBACjB,gBAAgB,aAChB,SAAS,aACT,SAAS,cACT,UAAU,eACV,WAAW,gBACX,YAAY,qBACZ,oBAAoB,yCACpB,qBAAqB,EACtB,GAAG;IACJ,IAAI,EAAC,kBAAkB,OAAO,EAAC,GAAG;IAElC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,kBAAkB,EACnD,QAAQ,IAAI,CAAC;IAGf,0HAA0H;IAC1H,qFAAqF;IACrF,IAAI,WAAW,CAAA,GAAA,kBAAU,EAAE;QAAC,OAAO;QAAU,aAAa;IAAM;IAChE,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAC1B,IAAI,mBAAmB,MAAM,gBAAgB,CAAC,gBAAgB;IAC9D,IAAI,WAAW,CAAA,GAAA,cAAM,EAAE,IAAM,oBAAoB,IAAI,CAAA,GAAA,yCAAmB,EAAE;YACxE,YAAY,MAAM,UAAU;YAC5B,cAAc,MAAM,YAAY;8BAChC;iBACA;uBACA;sBACA;uBACA;QACF,IAAI;QAAC;QAAkB,MAAM,UAAU;QAAE,MAAM,YAAY;QAAE;QAAkB;QAAK;QAAW;QAAU;KAAU;IAEnH,IAAI,mBAAC,eAAe,EAAC,GAAG,CAAA,GAAA,8BAAsB,EAAE;aAC9C;QACA,kBAAkB;QAClB,kBAAkB;uBAClB;mBACA;2BACA;2BACA;IACF;IAEA,IAAI,KAAK,CAAA,GAAA,YAAI,EAAE,MAAM,EAAE;IACvB,CAAA,GAAA,yCAAM,EAAE,GAAG,CAAC,OAAO;QAAC,kBAAkB;QAAU,SAAS;yBAAC;0BAAa;QAAY;+BAAG;IAAqB;IAE3G,IAAI,mBAAmB,CAAA,GAAA,yCAA+B,EAAE;QACtD,kBAAkB;QAClB,gBAAgB,CAAC,CAAE,CAAA,eAAe,YAAW;IAC/C;IAEA,IAAI,WAAW,CAAA,GAAA,qBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,IAAI,UAAU,CAAA,GAAA,kBAAU,EAAE,CAAC;QACzB,IAAI,QAAQ,SAAS,EAAE;YACrB,gEAAgE;YAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC,QAAQ,UAAU,CAAC;YAGrB;QACF;QAEA,gEAAgE;QAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,QAAQ,UAAU,CAAC;IACrB,GAAG;QAAC;KAAQ;IAEZ,qFAAqF;IACrF,IAAI,sBAAsB,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;YACvC,QAAQ,gBAAgB,MAAM;qBAC9B;QACF,CAAA,GAAI;QAAC;QAAS,gBAAgB,MAAM;KAAC;IAErC,IAAI,mBAAmB,CAAA,GAAA,0BAAkB,EAAE,KAAK;QAC9C,YAAY,MAAM,UAAU,CAAC,IAAI,KAAK;IACxC;IAEA,IAAI,YAA2B,CAAA,GAAA,iBAAS,EACtC,UACA;QACE,MAAM;YACN;QACA,wBAAwB,QAAQ,aAAa,KAAK,aAAa,SAAS;IAC1E,GACA,MAAM,4BAA4B,GAAG,sBAAsB,iBAE3D,AADA,mGAAmG;IAClG,MAAM,UAAU,CAAC,IAAI,KAAK,KAAK;QAAC,UAAU,mBAAmB,KAAK;IAAC,KAAM,WAC1E;IAGF,IAAI,eAAe;QACjB,SAAS,CAAC,gBAAgB,GAAG,MAAM,UAAU,CAAC,IAAI;QAClD,SAAS,CAAC,gBAAgB,GAAG,MAAM,UAAU,CAAC,WAAW;IAC3D;IAEA,CAAA,GAAA,yCAA2B,EAAE;oBAAC;IAAU,GAAG;IAC3C,OAAO;mBACL;IACF;AACF", "sources": ["packages/@react-aria/grid/src/useGrid.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMAttributes, DOMProps, Key, KeyboardDelegate, RefObject} from '@react-types/shared';\nimport {filterDOMProps, mergeProps, useId} from '@react-aria/utils';\nimport {GridCollection} from '@react-types/grid';\nimport {GridKeyboardDelegate} from './GridKeyboardDelegate';\nimport {gridMap} from './utils';\nimport {GridState} from '@react-stately/grid';\nimport {useCallback, useMemo} from 'react';\nimport {useCollator, useLocale} from '@react-aria/i18n';\nimport {useGridSelectionAnnouncement} from './useGridSelectionAnnouncement';\nimport {useHasTabbableChild} from '@react-aria/focus';\nimport {useHighlightSelectionDescription} from './useHighlightSelectionDescription';\nimport {useSelectableCollection} from '@react-aria/selection';\n\nexport interface GridProps extends DOMProps, AriaLabelingProps {\n  /** Whether the grid uses virtual scrolling. */\n  isVirtualized?: boolean,\n  /**\n   * Whether typeahead navigation is disabled.\n   * @default false\n   */\n  disallowTypeAhead?: boolean,\n  /**\n   * An optional keyboard delegate implementation for type to select,\n   * to override the default.\n   */\n  keyboardDelegate?: KeyboardDelegate,\n  /**\n   * Whether initial grid focus should be placed on the grid row or grid cell.\n   * @default 'row'\n   */\n  focusMode?: 'row' | 'cell',\n  /**\n   * A function that returns the text that should be announced by assistive technology when a row is added or removed from selection.\n   * @default (key) => state.collection.getItem(key)?.textValue\n   */\n  getRowText?: (key: Key) => string,\n  /**\n   * The ref attached to the scrollable body. Used to provided automatic scrolling on item focus for non-virtualized grids.\n   */\n  scrollRef?: RefObject<HTMLElement | null>,\n  /** Handler that is called when a user performs an action on the row. */\n  onRowAction?: (key: Key) => void,\n  /** Handler that is called when a user performs an action on the cell. */\n  onCellAction?: (key: Key) => void,\n  /**\n   * Whether pressing the escape key should clear selection in the grid or not.\n   *\n   * Most experiences should not modify this option as it eliminates a keyboard user's ability to\n   * easily clear selection. Only use if the escape key is being handled externally or should not\n   * trigger selection clearing contextually.\n   * @default 'clearSelection'\n   */\n  escapeKeyBehavior?: 'clearSelection' | 'none',\n  /** Whether selection should occur on press up instead of press down. */\n  shouldSelectOnPressUp?: boolean\n}\n\nexport interface GridAria {\n  /** Props for the grid element. */\n  gridProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a grid component.\n * A grid displays data in one or more rows and columns and enables a user to navigate its contents via directional navigation keys.\n * @param props - Props for the grid.\n * @param state - State for the grid, as returned by `useGridState`.\n * @param ref - The ref attached to the grid element.\n */\nexport function useGrid<T>(props: GridProps, state: GridState<T, GridCollection<T>>, ref: RefObject<HTMLElement | null>): GridAria {\n  let {\n    isVirtualized,\n    disallowTypeAhead,\n    keyboardDelegate,\n    focusMode,\n    scrollRef,\n    getRowText,\n    onRowAction,\n    onCellAction,\n    escapeKeyBehavior = 'clearSelection',\n    shouldSelectOnPressUp\n  } = props;\n  let {selectionManager: manager} = state;\n\n  if (!props['aria-label'] && !props['aria-labelledby']) {\n    console.warn('An aria-label or aria-labelledby prop is required for accessibility.');\n  }\n\n  // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n  // When virtualized, the layout object will be passed in as a prop and override this.\n  let collator = useCollator({usage: 'search', sensitivity: 'base'});\n  let {direction} = useLocale();\n  let disabledBehavior = state.selectionManager.disabledBehavior;\n  let delegate = useMemo(() => keyboardDelegate || new GridKeyboardDelegate({\n    collection: state.collection,\n    disabledKeys: state.disabledKeys,\n    disabledBehavior,\n    ref,\n    direction,\n    collator,\n    focusMode\n  }), [keyboardDelegate, state.collection, state.disabledKeys, disabledBehavior, ref, direction, collator, focusMode]);\n\n  let {collectionProps} = useSelectableCollection({\n    ref,\n    selectionManager: manager,\n    keyboardDelegate: delegate,\n    isVirtualized,\n    scrollRef,\n    disallowTypeAhead,\n    escapeKeyBehavior\n  });\n\n  let id = useId(props.id);\n  gridMap.set(state, {keyboardDelegate: delegate, actions: {onRowAction, onCellAction}, shouldSelectOnPressUp});\n\n  let descriptionProps = useHighlightSelectionDescription({\n    selectionManager: manager,\n    hasItemActions: !!(onRowAction || onCellAction)\n  });\n\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  let onFocus = useCallback((e) => {\n    if (manager.isFocused) {\n      // If a focus event bubbled through a portal, reset focus state.\n      if (!e.currentTarget.contains(e.target)) {\n        manager.setFocused(false);\n      }\n\n      return;\n    }\n\n    // Focus events can bubble through portals. Ignore these events.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    manager.setFocused(true);\n  }, [manager]);\n\n  // Continue to track collection focused state even if keyboard navigation is disabled\n  let navDisabledHandlers = useMemo(() => ({\n    onBlur: collectionProps.onBlur,\n    onFocus\n  }), [onFocus, collectionProps.onBlur]);\n\n  let hasTabbableChild = useHasTabbableChild(ref, {\n    isDisabled: state.collection.size !== 0\n  });\n\n  let gridProps: DOMAttributes = mergeProps(\n    domProps,\n    {\n      role: 'grid',\n      id,\n      'aria-multiselectable': manager.selectionMode === 'multiple' ? 'true' : undefined\n    },\n    state.isKeyboardNavigationDisabled ? navDisabledHandlers : collectionProps,\n    // If collection is empty, make sure the grid is tabbable unless there is a child tabbable element.\n    (state.collection.size === 0 && {tabIndex: hasTabbableChild ? -1 : 0}) || undefined,\n    descriptionProps\n  );\n\n  if (isVirtualized) {\n    gridProps['aria-rowcount'] = state.collection.size;\n    gridProps['aria-colcount'] = state.collection.columnCount;\n  }\n\n  useGridSelectionAnnouncement({getRowText}, state);\n  return {\n    gridProps\n  };\n}\n"], "names": [], "version": 3, "file": "useGrid.module.js.map"}