var $085250522aa37816$exports = require("./intlStrings.main.js");
var $iMOYA$reactarialiveannouncer = require("@react-aria/live-announcer");
var $iMOYA$reactariautils = require("@react-aria/utils");
var $iMOYA$reactariai18n = require("@react-aria/i18n");
var $iMOYA$react = require("react");


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useGridSelectionAnnouncement", () => $1eb174acfe8a0f16$export$137e594ef3218a10);
/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 




function $1eb174acfe8a0f16$export$137e594ef3218a10(props, state) {
    let { getRowText: getRowText = (key)=>{
        var _state_collection_getTextValue, _state_collection, _state_collection_getItem;
        var _state_collection_getTextValue1;
        return (_state_collection_getTextValue1 = (_state_collection_getTextValue = (_state_collection = state.collection).getTextValue) === null || _state_collection_getTextValue === void 0 ? void 0 : _state_collection_getTextValue.call(_state_collection, key)) !== null && _state_collection_getTextValue1 !== void 0 ? _state_collection_getTextValue1 : (_state_collection_getItem = state.collection.getItem(key)) === null || _state_collection_getItem === void 0 ? void 0 : _state_collection_getItem.textValue;
    } } = props;
    let stringFormatter = (0, $iMOYA$reactariai18n.useLocalizedStringFormatter)((0, ($parcel$interopDefault($085250522aa37816$exports))), '@react-aria/grid');
    // Many screen readers do not announce when items in a grid are selected/deselected.
    // We do this using an ARIA live region.
    let selection = state.selectionManager.rawSelection;
    let lastSelection = (0, $iMOYA$react.useRef)(selection);
    let announceSelectionChange = (0, $iMOYA$reactariautils.useEffectEvent)(()=>{
        var _lastSelection_current;
        if (!state.selectionManager.isFocused || selection === lastSelection.current) {
            lastSelection.current = selection;
            return;
        }
        let addedKeys = $1eb174acfe8a0f16$var$diffSelection(selection, lastSelection.current);
        let removedKeys = $1eb174acfe8a0f16$var$diffSelection(lastSelection.current, selection);
        // If adding or removing a single row from the selection, announce the name of that item.
        let isReplace = state.selectionManager.selectionBehavior === 'replace';
        let messages = [];
        if (state.selectionManager.selectedKeys.size === 1 && isReplace) {
            let firstKey = state.selectionManager.selectedKeys.keys().next().value;
            if (firstKey != null && state.collection.getItem(firstKey)) {
                let currentSelectionText = getRowText(firstKey);
                if (currentSelectionText) messages.push(stringFormatter.format('selectedItem', {
                    item: currentSelectionText
                }));
            }
        } else if (addedKeys.size === 1 && removedKeys.size === 0) {
            let firstKey = addedKeys.keys().next().value;
            if (firstKey != null) {
                let addedText = getRowText(firstKey);
                if (addedText) messages.push(stringFormatter.format('selectedItem', {
                    item: addedText
                }));
            }
        } else if (removedKeys.size === 1 && addedKeys.size === 0) {
            let firstKey = removedKeys.keys().next().value;
            if (firstKey != null && state.collection.getItem(firstKey)) {
                let removedText = getRowText(firstKey);
                if (removedText) messages.push(stringFormatter.format('deselectedItem', {
                    item: removedText
                }));
            }
        }
        // Announce how many items are selected, except when selecting the first item.
        if (state.selectionManager.selectionMode === 'multiple') {
            if (messages.length === 0 || selection === 'all' || selection.size > 1 || lastSelection.current === 'all' || ((_lastSelection_current = lastSelection.current) === null || _lastSelection_current === void 0 ? void 0 : _lastSelection_current.size) > 1) messages.push(selection === 'all' ? stringFormatter.format('selectedAll') : stringFormatter.format('selectedCount', {
                count: selection.size
            }));
        }
        if (messages.length > 0) (0, $iMOYA$reactarialiveannouncer.announce)(messages.join(' '));
        lastSelection.current = selection;
    });
    (0, $iMOYA$reactariautils.useUpdateEffect)(()=>{
        if (state.selectionManager.isFocused) announceSelectionChange();
        else {
            // Wait a frame in case the collection is about to become focused (e.g. on mouse down).
            let raf = requestAnimationFrame(announceSelectionChange);
            return ()=>cancelAnimationFrame(raf);
        }
    }, [
        selection,
        state.selectionManager.isFocused
    ]);
}
function $1eb174acfe8a0f16$var$diffSelection(a, b) {
    let res = new Set();
    if (a === 'all' || b === 'all') return res;
    for (let key of a.keys())if (!b.has(key)) res.add(key);
    return res;
}


//# sourceMappingURL=useGridSelectionAnnouncement.main.js.map
