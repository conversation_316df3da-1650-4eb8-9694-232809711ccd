{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,qKAA2B,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;IACxF,qBAAqB,CAAC,sWAA0D,CAAC;IACjF,UAAU,CAAC,iDAAO,CAAC;IACnB,eAAe,CAAC,4KAA4B,CAAC;IAC7C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4JAAwB,CAAC;YAAE,KAAK,IAAM,CAAC,uEAAW,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yDAAS,CAAC;YAAE,OAAO,IAAM,CAAC,8EAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yDAAS,CAAC;QAAA,GAAG,CAAC,CAAC;IACnP,gBAAgB,CAAC,OAAS,CAAC,+IAAuB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAClE", "sources": ["packages/@react-aria/grid/intl/el-GR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Δεν επιλέχθηκε το στοιχείο {item}.\",\n  \"longPressToSelect\": \"Πατήστε παρατεταμένα για να μπείτε σε λειτουργία επιλογής.\",\n  \"select\": \"Επιλογή\",\n  \"selectedAll\": \"Επιλέχθηκαν όλα τα στοιχεία.\",\n  \"selectedCount\": \"{count, plural, =0 {Δεν επιλέχθηκαν στοιχεία} one {Επιλέχθηκε # στοιχείο} other {Επιλέχθηκαν # στοιχεία}}.\",\n  \"selectedItem\": \"Επιλέχθηκε το στοιχείο {item}.\"\n}\n"], "names": [], "version": 3, "file": "el-GR.module.js.map"}