{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,yBAAI,EAAE,KAAK,IAAI,CAAC,QAAC,CAAC;IACjE,qBAAqB,CAAC,gFAAU,CAAC;IACjC,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,gEAAQ,CAAC;IACzB,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,wCAAK,CAAC;YAAE,KAAK,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;YAAE,OAAO,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;QAAA,GAAG,QAAC,CAAC;IACvM,gBAAgB,CAAC,OAAS,CAAC,yBAAI,EAAE,KAAK,IAAI,CAAC,QAAC,CAAC;AAC/C", "sources": ["packages/@react-aria/grid/intl/zh-CN.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"未选择 {item}。\",\n  \"longPressToSelect\": \"长按以进入选择模式。\",\n  \"select\": \"选择\",\n  \"selectedAll\": \"已选择所有项目。\",\n  \"selectedCount\": \"{count, plural, =0 {未选择项目} one {已选择 # 个项目} other {已选择 # 个项目}}。\",\n  \"selectedItem\": \"已选择 {item}。\"\n}\n"], "names": [], "version": 3, "file": "zh-CN.main.js.map"}