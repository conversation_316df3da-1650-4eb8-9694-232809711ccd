{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,6FAAgB,CAAC;IAC5E,qBAAqB,CAAC,6IAAsB,CAAC;IAC7C,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,mGAAe,CAAC;IAChC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,kFAAY,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0FAAa,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0FAAa,CAAC;QAAA,GAAG,CAAC,CAAC;IACxN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,4EAAa,CAAC;AACvD", "sources": ["packages/@react-aria/grid/intl/ko-KR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item}이(가) 선택되지 않았습니다.\",\n  \"longPressToSelect\": \"선택 모드로 들어가려면 길게 누르십시오.\",\n  \"select\": \"선택\",\n  \"selectedAll\": \"모든 항목이 선택되었습니다.\",\n  \"selectedCount\": \"{count, plural, =0 {선택된 항목이 없습니다} one {#개 항목이 선택되었습니다} other {#개 항목이 선택되었습니다}}.\",\n  \"selectedItem\": \"{item}이(가) 선택되었습니다.\"\n}\n"], "names": [], "version": 3, "file": "ko-KR.main.js.map"}