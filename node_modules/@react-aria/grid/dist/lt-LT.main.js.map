{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;IAC1E,qBAAqB,CAAC,wFAAgE,CAAC;IACvF,UAAU,CAAC,UAAU,CAAC;IACtB,eAAe,CAAC,0BAA0B,CAAC;IAC3C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uCAAiC,CAAC;YAAE,KAAK,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,UAAU,CAAC;YAAE,OAAO,IAAM,CAAC,2BAAqB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;QAAA,GAAG,CAAC,CAAC;IAC9P,gBAAgB,CAAC,OAAS,CAAC,YAAY,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AACvD", "sources": ["packages/@react-aria/grid/intl/lt-LT.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nepasirinkta.\",\n  \"longPressToSelect\": \"Norėdami įjungti pasirinkimo re<PERSON>, paspauskite ir palaikykite.\",\n  \"select\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"selectedAll\": \"Pasirinkti visi elementai.\",\n  \"selectedCount\": \"{count, plural, =0 {Nepasirinktas nė vienas elementas} one {Pasirinktas # elementas} other {Pasirinkta elementų: #}}.\",\n  \"selectedItem\": \"Pasirinkta: {item}.\"\n}\n"], "names": [], "version": 3, "file": "lt-LT.main.js.map"}