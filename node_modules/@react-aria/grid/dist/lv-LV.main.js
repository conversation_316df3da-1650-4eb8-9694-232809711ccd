module.exports = {
    "deselectedItem": (args)=>`Vienums ${args.item} nav atlas\u{12B}ts.`,
    "longPressToSelect": `Ilgi turiet nospiestu. lai iesl\u{113}gtu atlases re\u{17E}\u{12B}mu.`,
    "select": `Atlas\u{12B}t`,
    "selectedAll": `Atlas\u{12B}ti visi vienumi.`,
    "selectedCount": (args, formatter)=>`${formatter.plural(args.count, {
            "=0": `Nav atlas\u{12B}ts neviens vienums`,
            one: ()=>`Atlas\u{12B}to vienumu skaits: ${formatter.number(args.count)}`,
            other: ()=>`Atlas\u{12B}to vienumu skaits: ${formatter.number(args.count)}`
        })}.`,
    "selectedItem": (args)=>`Atlas\u{12B}ts vienums ${args.item}.`
};


//# sourceMappingURL=lv-LV.main.js.map
