{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;IACxE,qBAAqB,CAAC,+CAA+C,CAAC;IACtE,UAAU,CAAC,OAAI,CAAC;IAChB,eAAe,CAAC,qBAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qBAAqB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,gBAAgB,CAAC;QAAA,GAAG,CAAC,CAAC;IACrO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC;AACjD", "sources": ["packages/@react-aria/grid/intl/da-DK.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} ikke valgt.\",\n  \"longPressToSelect\": \"Lav et langt tryk for at aktivere valgtilstand.\",\n  \"select\": \"Vælg\",\n  \"selectedAll\": \"Alle elementer valgt.\",\n  \"selectedCount\": \"{count, plural, =0 {Ingen elementer valgt} one {# element valgt} other {# elementer valgt}}.\",\n  \"selectedItem\": \"{item} valgt.\"\n}\n"], "names": [], "version": 3, "file": "da-DK.module.js.map"}