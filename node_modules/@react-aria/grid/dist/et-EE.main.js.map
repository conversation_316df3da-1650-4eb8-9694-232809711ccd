{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;IAC1E,qBAAqB,CAAC,iDAA2C,CAAC;IAClE,UAAU,CAAC,IAAI,CAAC;IAChB,eAAe,CAAC,2BAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,sBAAmB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAe,CAAC;QAAA,GAAG,CAAC,CAAC;IAClO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;AACnD", "sources": ["packages/@react-aria/grid/intl/et-EE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} pole valitud.\",\n  \"longPressToSelect\": \"Valikurežiimi sisenemiseks vajutage pikalt.\",\n  \"select\": \"Vali\",\n  \"selectedAll\": \"Kõik üksused valitud.\",\n  \"selectedCount\": \"{count, plural, =0 {Üksusi pole valitud} one {# üksus valitud} other {# üksust valitud}}.\",\n  \"selectedItem\": \"{item} valitud.\"\n}\n"], "names": [], "version": 3, "file": "et-EE.main.js.map"}