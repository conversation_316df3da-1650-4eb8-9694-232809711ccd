{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;IAC1E,UAAU,CAAC,MAAM,CAAC;IAClB,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,iBAAiB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,eAAe,CAAC;QAAA,GAAG,CAAC,CAAC;IAChO,eAAe,CAAC,mBAAmB,CAAC;IACpC,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;IAClD,qBAAqB,CAAC,mCAAmC,CAAC;AAC5D", "sources": ["packages/@react-aria/grid/intl/en-US.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} not selected.\",\n  \"select\": \"Select\",\n  \"selectedCount\": \"{count, plural, =0 {No items selected} one {# item selected} other {# items selected}}.\",\n  \"selectedAll\": \"All items selected.\",\n  \"selectedItem\": \"{item} selected.\",\n  \"longPressToSelect\": \"Long press to enter selection mode.\"\n}\n"], "names": [], "version": 3, "file": "en-US.main.js.map"}