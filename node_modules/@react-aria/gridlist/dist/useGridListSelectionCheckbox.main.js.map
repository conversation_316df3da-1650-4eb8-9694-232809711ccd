{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAWM,SAAS,0CAAgC,KAAqC,EAAE,KAAmB;IACxG,IAAI,OAAC,GAAG,EAAC,GAAG;IACZ,MAAM,iBAAC,aAAa,EAAC,GAAG,CAAA,GAAA,6CAAuB,EAAE,OAAO;IAExD,OAAO;QACL,eAAe;YACb,GAAG,aAAa;YAChB,mBAAmB,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE,CAAA,GAAA,kCAAO,EAAE,OAAO,MAAM;QAClE;IACF;AACF", "sources": ["packages/@react-aria/gridlist/src/useGridListSelectionCheckbox.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaGridSelectionCheckboxProps, GridSelectionCheckboxAria, useGridSelectionCheckbox} from '@react-aria/grid';\nimport {getRowId} from './utils';\nimport type {ListState} from '@react-stately/list';\n\n/**\n * Provides the behavior and accessibility implementation for a selection checkbox in a grid list.\n * @param props - Props for the selection checkbox.\n * @param state - State of the list, as returned by `useListState`.\n */\nexport function useGridListSelectionCheckbox<T>(props: AriaGridSelectionCheckboxProps, state: ListState<T>): GridSelectionCheckboxAria {\n  let {key} = props;\n  const {checkboxProps} = useGridSelectionCheckbox(props, state as any);\n\n  return {\n    checkboxProps: {\n      ...checkboxProps,\n      'aria-labelledby': `${checkboxProps.id} ${getRowId(state, key)}`\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useGridListSelectionCheckbox.main.js.map"}