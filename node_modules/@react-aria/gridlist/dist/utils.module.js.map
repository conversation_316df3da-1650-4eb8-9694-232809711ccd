{"mappings": "AAAA;;;;;;;;;;CAUC,GAeM,MAAM,4CAAsD,IAAI;AAEhE,SAAS,0CAAY,KAAmB,EAAE,GAAQ;QAC5C;IAAX,IAAI,MAAC,EAAE,EAAC,GAAG,CAAA,eAAA,0CAAQ,GAAG,CAAC,oBAAZ,0BAAA,eAAsB,CAAC;IAClC,IAAI,CAAC,IACH,MAAM,IAAI,MAAM;IAGlB,OAAO,GAAG,GAAG,CAAC,EAAE,0CAAa,MAAM;AACrC;AAEO,SAAS,0CAAa,GAAQ;IACnC,IAAI,OAAO,QAAQ,UACjB,OAAO,IAAI,OAAO,CAAC,QAAQ;IAG7B,OAAO,KAAK;AACd", "sources": ["packages/@react-aria/gridlist/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport type {ListState} from '@react-stately/list';\n\ninterface ListMapShared {\n  id: string,\n  onAction?: (key: Key) => void,\n  linkBehavior?: 'action' | 'selection' | 'override',\n  keyboardNavigationBehavior: 'arrow' | 'tab',\n  shouldSelectOnPressUp?: boolean\n}\n\n// Used to share:\n// id of the list and onAction between useList, useListItem, and useListSelectionCheckbox\nexport const listMap: WeakMap<ListState<unknown>, ListMapShared> = new WeakMap<ListState<unknown>, ListMapShared>();\n\nexport function getRowId<T>(state: ListState<T>, key: Key): string {\n  let {id} = listMap.get(state) ?? {};\n  if (!id) {\n    throw new Error('Unknown list');\n  }\n\n  return `${id}-${normalizeKey(key)}`;\n}\n\nexport function normalizeKey(key: Key): string {\n  if (typeof key === 'string') {\n    return key.replace(/\\s*/g, '');\n  }\n\n  return '' + key;\n}\n"], "names": [], "version": 3, "file": "utils.module.js.map"}