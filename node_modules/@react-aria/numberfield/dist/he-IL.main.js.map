{"mappings": "AAAA,iBAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,6BAAK,EAAE,KAAK,UAAU,EAAE;IACjE,YAAY,CAAC,OAAS,CAAC,6BAAK,EAAE,KAAK,UAAU,EAAE;IAC/C,eAAe,CAAC,kDAAQ,CAAC;AAC3B", "sources": ["packages/@react-aria/numberfield/intl/he-IL.json"], "sourcesContent": ["{\n  \"decrease\": \"הקט<PERSON> {fieldLabel}\",\n  \"increase\": \"הגדל {fieldLabel}\",\n  \"numberField\": \"שדה מספר\"\n}\n"], "names": [], "version": 3, "file": "he-IL.main.js.map"}