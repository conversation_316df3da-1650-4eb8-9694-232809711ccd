{"mappings": ";AAAA,4BAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,cAAQ,EAAE,KAAK,UAAU,EAAE;IACpE,YAAY,CAAC,OAAS,CAAC,YAAM,EAAE,KAAK,UAAU,EAAE;IAChD,eAAe,CAAC,eAAY,CAAC;AAC/B", "sources": ["packages/@react-aria/numberfield/intl/fi-FI.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON>nä {fieldLabel}\",\n  \"increase\": \"<PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"numberField\": \"Numerokenttä\"\n}\n"], "names": [], "version": 3, "file": "fi-FI.module.js.map"}