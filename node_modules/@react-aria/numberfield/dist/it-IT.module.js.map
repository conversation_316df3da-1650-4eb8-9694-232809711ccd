{"mappings": ";AAAA,4BAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,UAAU,EAAE;IACnE,YAAY,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,UAAU,EAAE;IAClD,eAAe,CAAC,YAAY,CAAC;AAC/B", "sources": ["packages/@react-aria/numberfield/intl/it-IT.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"Aumenta {fieldLabel}\",\n  \"numberField\": \"Campo numero\"\n}\n"], "names": [], "version": 3, "file": "it-IT.module.js.map"}