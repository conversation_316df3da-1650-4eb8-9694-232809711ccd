{"mappings": "AAAA,iBAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,UAAU,EAAE;IACrE,YAAY,CAAC,OAAS,CAAC,UAAU,EAAE,KAAK,UAAU,EAAE;IACpD,eAAe,CAAC,eAAe,CAAC;AAClC", "sources": ["packages/@react-aria/numberfield/intl/fr-FR.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"Augmenter {fieldLabel}\",\n  \"numberField\": \"Champ de nombre\"\n}\n"], "names": [], "version": 3, "file": "fr-FR.main.js.map"}