{"mappings": "AAAA,iBAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,UAAU,EAAE;IACrE,YAAY,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,UAAU,EAAE;IACnD,eAAe,CAAC,iBAAc,CAAC;AACjC", "sources": ["packages/@react-aria/numberfield/intl/pt-PT.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"Aumentar {fieldLabel}\",\n  \"numberField\": \"Campo numérico\"\n}\n"], "names": [], "version": 3, "file": "pt-PT.main.js.map"}