{"mappings": "AAAA,iBAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,UAAU,EAAE;IACrE,YAAY,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,UAAU,EAAE;IACnD,eAAe,CAAC,kBAAe,CAAC;AAClC", "sources": ["packages/@react-aria/numberfield/intl/pt-BR.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"Aumentar {fieldLabel}\",\n  \"numberField\": \"Campo de número\"\n}\n"], "names": [], "version": 3, "file": "pt-BR.main.js.map"}