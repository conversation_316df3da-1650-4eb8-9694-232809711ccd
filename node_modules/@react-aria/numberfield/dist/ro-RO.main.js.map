{"mappings": "AAAA,iBAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,cAAQ,EAAE,KAAK,UAAU,EAAE;IACpE,YAAY,CAAC,OAAS,CAAC,eAAS,EAAE,KAAK,UAAU,EAAE;IACnD,eAAe,CAAC,eAAY,CAAC;AAC/B", "sources": ["packages/@react-aria/numberfield/intl/ro-RO.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"<PERSON><PERSON>ș<PERSON><PERSON> {fieldLabel}\",\n  \"numberField\": \"Câmp numeric\"\n}\n"], "names": [], "version": 3, "file": "ro-RO.main.js.map"}