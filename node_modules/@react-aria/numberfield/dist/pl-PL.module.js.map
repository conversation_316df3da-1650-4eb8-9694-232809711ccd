{"mappings": ";AAAA,4BAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,UAAU,EAAE;IACrE,YAAY,CAAC,OAAS,CAAC,cAAQ,EAAE,KAAK,UAAU,EAAE;IAClD,eAAe,CAAC,WAAW,CAAC;AAC9B", "sources": ["packages/@react-aria/numberfield/intl/pl-PL.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"numberField\": \"Pole numeru\"\n}\n"], "names": [], "version": 3, "file": "pl-PL.module.js.map"}