{"mappings": "AAAA,iBAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,UAAU,EAAE;IACnE,YAAY,CAAC,OAAS,CAAC,OAAI,EAAE,KAAK,UAAU,EAAE;IAC9C,eAAe,CAAC,aAAU,CAAC;AAC7B", "sources": ["packages/@react-aria/numberfield/intl/sv-SE.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"<PERSON><PERSON> {fieldLabel}\",\n  \"numberField\": \"Nummerfält\"\n}\n"], "names": [], "version": 3, "file": "sv-SE.main.js.map"}