import $aC4gS$arAEmodulejs from "./ar-AE.mjs";
import $aC4gS$bgBGmodulejs from "./bg-BG.mjs";
import $aC4gS$csCZmodulejs from "./cs-CZ.mjs";
import $aC4gS$daDKmodulejs from "./da-DK.mjs";
import $aC4gS$deDEmodulejs from "./de-DE.mjs";
import $aC4gS$elGRmodulejs from "./el-GR.mjs";
import $aC4gS$enUSmodulejs from "./en-US.mjs";
import $aC4gS$esESmodulejs from "./es-ES.mjs";
import $aC4gS$etEEmodulejs from "./et-EE.mjs";
import $aC4gS$fiFImodulejs from "./fi-FI.mjs";
import $aC4gS$frFRmodulejs from "./fr-FR.mjs";
import $aC4gS$heILmodulejs from "./he-IL.mjs";
import $aC4gS$hrHRmodulejs from "./hr-HR.mjs";
import $aC4gS$huHUmodulejs from "./hu-HU.mjs";
import $aC4gS$itITmodulejs from "./it-IT.mjs";
import $aC4gS$jaJPmodulejs from "./ja-JP.mjs";
import $aC4gS$koKRmodulejs from "./ko-KR.mjs";
import $aC4gS$ltLTmodulejs from "./lt-LT.mjs";
import $aC4gS$lvLVmodulejs from "./lv-LV.mjs";
import $aC4gS$nbNOmodulejs from "./nb-NO.mjs";
import $aC4gS$nlNLmodulejs from "./nl-NL.mjs";
import $aC4gS$plPLmodulejs from "./pl-PL.mjs";
import $aC4gS$ptBRmodulejs from "./pt-BR.mjs";
import $aC4gS$ptPTmodulejs from "./pt-PT.mjs";
import $aC4gS$roROmodulejs from "./ro-RO.mjs";
import $aC4gS$ruRUmodulejs from "./ru-RU.mjs";
import $aC4gS$skSKmodulejs from "./sk-SK.mjs";
import $aC4gS$slSImodulejs from "./sl-SI.mjs";
import $aC4gS$srSPmodulejs from "./sr-SP.mjs";
import $aC4gS$svSEmodulejs from "./sv-SE.mjs";
import $aC4gS$trTRmodulejs from "./tr-TR.mjs";
import $aC4gS$ukUAmodulejs from "./uk-UA.mjs";
import $aC4gS$zhCNmodulejs from "./zh-CN.mjs";
import $aC4gS$zhTWmodulejs from "./zh-TW.mjs";

var $280a227d7cb94b92$exports = {};


































$280a227d7cb94b92$exports = {
    "ar-AE": $aC4gS$arAEmodulejs,
    "bg-BG": $aC4gS$bgBGmodulejs,
    "cs-CZ": $aC4gS$csCZmodulejs,
    "da-DK": $aC4gS$daDKmodulejs,
    "de-DE": $aC4gS$deDEmodulejs,
    "el-GR": $aC4gS$elGRmodulejs,
    "en-US": $aC4gS$enUSmodulejs,
    "es-ES": $aC4gS$esESmodulejs,
    "et-EE": $aC4gS$etEEmodulejs,
    "fi-FI": $aC4gS$fiFImodulejs,
    "fr-FR": $aC4gS$frFRmodulejs,
    "he-IL": $aC4gS$heILmodulejs,
    "hr-HR": $aC4gS$hrHRmodulejs,
    "hu-HU": $aC4gS$huHUmodulejs,
    "it-IT": $aC4gS$itITmodulejs,
    "ja-JP": $aC4gS$jaJPmodulejs,
    "ko-KR": $aC4gS$koKRmodulejs,
    "lt-LT": $aC4gS$ltLTmodulejs,
    "lv-LV": $aC4gS$lvLVmodulejs,
    "nb-NO": $aC4gS$nbNOmodulejs,
    "nl-NL": $aC4gS$nlNLmodulejs,
    "pl-PL": $aC4gS$plPLmodulejs,
    "pt-BR": $aC4gS$ptBRmodulejs,
    "pt-PT": $aC4gS$ptPTmodulejs,
    "ro-RO": $aC4gS$roROmodulejs,
    "ru-RU": $aC4gS$ruRUmodulejs,
    "sk-SK": $aC4gS$skSKmodulejs,
    "sl-SI": $aC4gS$slSImodulejs,
    "sr-SP": $aC4gS$srSPmodulejs,
    "sv-SE": $aC4gS$svSEmodulejs,
    "tr-TR": $aC4gS$trTRmodulejs,
    "uk-UA": $aC4gS$ukUAmodulejs,
    "zh-CN": $aC4gS$zhCNmodulejs,
    "zh-TW": $aC4gS$zhTWmodulejs
};


export {$280a227d7cb94b92$exports as default};
//# sourceMappingURL=intlStrings.module.js.map
