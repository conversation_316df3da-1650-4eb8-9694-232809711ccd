{"mappings": "AAAA,iBAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,gBAAU,EAAE,KAAK,UAAU,EAAE;IACtE,YAAY,CAAC,OAAS,CAAC,UAAU,EAAE,KAAK,UAAU,EAAE;IACpD,eAAe,CAAC,cAAc,CAAC;AACjC", "sources": ["packages/@react-aria/numberfield/intl/lt-LT.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"<PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"numberField\": \"Numerio laukas\"\n}\n"], "names": [], "version": 3, "file": "lt-LT.main.js.map"}