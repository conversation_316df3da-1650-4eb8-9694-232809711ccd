{"mappings": ";AAAA,4BAAiB;IAAG,YAAY,CAAC,OAAS,CAAC,yBAAa,EAAE,KAAK,UAAU,EAAE;IACzE,YAAY,CAAC,OAAS,CAAC,0BAAc,EAAE,KAAK,UAAU,EAAE;IACxD,eAAe,CAAC,mBAAa,CAAC;AAChC", "sources": ["packages/@react-aria/numberfield/intl/lv-LV.json"], "sourcesContent": ["{\n  \"decrease\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"increase\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {fieldLabel}\",\n  \"numberField\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> lauks\"\n}\n"], "names": [], "version": 3, "file": "lv-LV.module.js.map"}