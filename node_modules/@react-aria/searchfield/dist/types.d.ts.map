{"mappings": ";;;;;AAuBA,gCAAiC,SAAQ,gBAAgB;IACvD,iEAAiE;IACjE,UAAU,EAAE,oBAAoB,gBAAgB,CAAC,CAAC;IAClD,mCAAmC;IACnC,UAAU,EAAE,oBAAoB,gBAAgB,CAAC,CAAC;IAClD,kCAAkC;IAClC,gBAAgB,EAAE,eAAe,CAAC;IAClC,+DAA+D;IAC/D,gBAAgB,EAAE,aAAa,CAAC;IAChC,iEAAiE;IACjE,iBAAiB,EAAE,aAAa,CAAA;CACjC;AAED;;;;;GAKG;AACH,+BACE,KAAK,EAAE,oBAAoB,EAC3B,KAAK,EAAE,gBAAgB,EACvB,QAAQ,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAC3C,eAAe,CAoFjB;ACtHD,YAAY,EAAC,oBAAoB,EAAC,MAAM,0BAA0B,CAAC", "sources": ["packages/@react-aria/searchfield/src/packages/@react-aria/searchfield/src/useSearchField.ts", "packages/@react-aria/searchfield/src/packages/@react-aria/searchfield/src/index.ts", "packages/@react-aria/searchfield/src/index.ts"], "sourcesContent": [null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {useSearchField} from './useSearchField';\nexport type {AriaSearchFieldProps} from '@react-types/searchfield';\nexport type {SearchFieldAria} from './useSearchField';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}