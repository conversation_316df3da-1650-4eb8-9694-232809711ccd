{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAUM,MAAM,4CAA2C,IAAI;AASrD,SAAS,0CAAiB,IAA0B;IACzD,IAAI,cAAC,UAAU,UAAE,MAAM,WAAE,OAAO,EAAC,GAAG;IAEpC,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,CAAC,UAAU,YAAY,MACzB;QAGF,IAAI,WAAW,CAAC;YACd,uEAAuE;YACvE,IAAI,SAAS,EAAE,MAAM;YACrB,gFAAgF;YAChF,IAAI,CAAC,WAAW,OAAO,IAAK,AAAC,kBAAkB,QAAS,CAAC,OAAO,QAAQ,CAAC,WAAW,OAAO,GACzF;YAGF,8FAA8F;YAC9F,4FAA4F;YAC5F,2GAA2G;YAC3G,IAAI,EAAE,MAAM,YAAY,oBAAoB,EAAE,MAAM,YAAY,qBAC9D;YAGF,IAAI,iBAAiB,WAAW,0CAAW,GAAG,CAAC,WAAW,OAAO;YACjE,IAAI,gBACF;QAEJ;QAEA,OAAO,gBAAgB,CAAC,UAAU,UAAU;QAC5C,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU,UAAU;QACjD;IACF,GAAG;QAAC;QAAQ;QAAS;KAAW;AAClC", "sources": ["packages/@react-aria/overlays/src/useCloseOnScroll.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\n\n// This behavior moved from useOverlayTrigger to useOverlayPosition.\n// For backward compatibility, where useOverlayTrigger handled hiding the popover on close,\n// it sets a close function here mapped from the trigger element. This way we can avoid\n// forcing users to pass an onClose function to useOverlayPosition which could be considered\n// a breaking change.\nexport const onCloseMap: WeakMap<Element, () => void> = new WeakMap();\n\ninterface CloseOnScrollOptions {\n  triggerRef: RefObject<Element | null>,\n  isOpen?: boolean,\n  onClose?: (() => void) | null\n}\n\n/** @private */\nexport function useCloseOnScroll(opts: CloseOnScrollOptions): void {\n  let {triggerRef, isOpen, onClose} = opts;\n\n  useEffect(() => {\n    if (!isOpen || onClose === null) {\n      return;\n    }\n\n    let onScroll = (e: Event) => {\n      // Ignore if scrolling an scrollable region outside the trigger's tree.\n      let target = e.target;\n      // window is not a Node and doesn't have contain, but window contains everything\n      if (!triggerRef.current || ((target instanceof Node) && !target.contains(triggerRef.current))) {\n        return;\n      }\n\n      // Ignore scroll events on any input or textarea as the cursor position can cause it to scroll\n      // such as in a combobox. Clicking the dropdown button places focus on the input, and if the\n      // text inside the input extends beyond the 'end', then it will scroll so the cursor is visible at the end.\n      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\n        return;\n      }\n\n      let onCloseHandler = onClose || onCloseMap.get(triggerRef.current);\n      if (onCloseHandler) {\n        onCloseHandler();\n      }\n    };\n\n    window.addEventListener('scroll', onScroll, true);\n    return () => {\n      window.removeEventListener('scroll', onScroll, true);\n    };\n  }, [isOpen, onClose, triggerRef]);\n}\n"], "names": [], "version": 3, "file": "useCloseOnScroll.module.js.map"}