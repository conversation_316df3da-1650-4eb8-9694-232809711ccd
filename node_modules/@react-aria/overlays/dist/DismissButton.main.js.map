{"mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;AAoBM,SAAS,0CAAc,KAAyB;IACrD,IAAI,aAAC,SAAS,EAAE,GAAG,YAAW,GAAG;IACjC,IAAI,kBAAkB,CAAA,GAAA,gDAA0B,EAAE,CAAA,GAAA,mDAAW,GAAG;IAEhE,IAAI,SAAS,CAAA,GAAA,+BAAQ,EAAE,YAAY,gBAAgB,MAAM,CAAC;IAE1D,IAAI,UAAU;QACZ,IAAI,WACF;IAEJ;IAEA,qBACE,0DAAC,CAAA,GAAA,6CAAa,uBACZ,0DAAC;QACE,GAAG,MAAM;QACV,UAAU;QACV,SAAS;QACT,OAAO;YAAC,OAAO;YAAG,QAAQ;QAAC;;AAGnC", "sources": ["packages/@react-aria/overlays/src/DismissButton.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport React, {JSX} from 'react';\nimport {useLabels} from '@react-aria/utils';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {VisuallyHidden} from '@react-aria/visually-hidden';\n\nexport interface DismissButtonProps extends AriaLabelingProps, DOMProps {\n  /** Called when the dismiss button is activated. */\n  onDismiss?: () => void\n}\n\n/**\n * A visually hidden button that can be used to allow screen reader\n * users to dismiss a modal or popup when there is no visual\n * affordance to do so.\n */\nexport function DismissButton(props: DismissButtonProps): JSX.Element {\n  let {onDismiss, ...otherProps} = props;\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/overlays');\n\n  let labels = useLabels(otherProps, stringFormatter.format('dismiss'));\n\n  let onClick = () => {\n    if (onDismiss) {\n      onDismiss();\n    }\n  };\n\n  return (\n    <VisuallyHidden>\n      <button\n        {...labels}\n        tabIndex={-1}\n        onClick={onClick}\n        style={{width: 1, height: 1}} />\n    </VisuallyHidden>\n  );\n}\n"], "names": [], "version": 3, "file": "DismissButton.main.js.map"}