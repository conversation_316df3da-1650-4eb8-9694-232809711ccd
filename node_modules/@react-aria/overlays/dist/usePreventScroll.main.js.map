{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AASD,MAAM,uCAAiB,OAAO,aAAa,eAAe,OAAO,cAAc;AAE/E,sEAAsE;AACtE,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,mIAAmI;AACnI,IAAI,2CAAqB;AACzB,IAAI;AAOG,SAAS,0CAAiB,UAAgC,CAAC,CAAC;IACjE,IAAI,cAAC,UAAU,EAAC,GAAG;IAEnB,CAAA,GAAA,qCAAc,EAAE;QACd,IAAI,YACF;QAGF;QACA,IAAI,6CAAuB;YACzB,IAAI,CAAA,GAAA,2BAAI,KACN,gCAAU;iBAEV,gCAAU;;QAId,OAAO;YACL;YACA,IAAI,6CAAuB,GACzB;QAEJ;IACF,GAAG;QAAC;KAAW;AACjB;AAEA,0FAA0F;AAC1F,mFAAmF;AACnF,SAAS;IACP,IAAI,iBAAiB,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW;IAC7E,OAAO,CAAA,GAAA,2BAAI,EACT,iBAAiB,KACf,2FAA2F;IAC1F,CAAA,qBAAqB,SAAS,eAAe,CAAC,KAAK,GAChD,+BAAS,SAAS,eAAe,EAAE,mBAAmB,YACtD,+BAAS,SAAS,eAAe,EAAE,gBAAgB,GAAG,eAAe,EAAE,CAAC,CAAA,GAC9E,+BAAS,SAAS,eAAe,EAAE,YAAY;AAEnD;AAEA,wEAAwE;AACxE,gDAAgD;AAChD,EAAE;AACF,8FAA8F;AAC9F,sGAAsG;AACtG,mCAAmC;AACnC,6GAA6G;AAC7G,2EAA2E;AAC3E,4GAA4G;AAC5G,sGAAsG;AACtG,EAAE;AACF,oGAAoG;AACpG,EAAE;AACF,+GAA+G;AAC/G,oBAAoB;AACpB,4GAA4G;AAC5G,+GAA+G;AAC/G,mDAAmD;AACnD,uGAAuG;AACvG,qGAAqG;AACrG,4GAA4G;AAC5G,4DAA4D;AAC5D,kHAAkH;AAClH,0GAA0G;AAC1G,oFAAoF;AACpF,gHAAgH;AAChH,oFAAoF;AACpF,SAAS;IACP,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe,CAAC;QAClB,sFAAsF;QACtF,aAAa,CAAA,GAAA,qCAAc,EAAE,EAAE,MAAM,EAAa;QAClD,IAAI,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,EACzE;QAGF,6EAA6E;QAC7E,4EAA4E;QAC5E,sBAAsB;QACtB,IAAI,sBAAsB,eAAe,OAAO,gBAAgB,CAAC,YAAY,kBAAkB,KAAK,QAClG,0BAA0B,+BAAS,YAAY,sBAAsB;IAEzE;IAEA,IAAI,cAAc,CAAC;QACjB,gCAAgC;QAChC,IAAI,CAAC,cAAc,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,EAAE;YAC1F,EAAE,cAAc;YAChB;QACF;QAEA,6EAA6E;QAC7E,2FAA2F;QAC3F,iFAAiF;QACjF,gFAAgF;QAChF,oFAAoF;QACpF,sDAAsD;QACtD,IAAI,WAAW,YAAY,KAAK,WAAW,YAAY,IAAI,WAAW,WAAW,KAAK,WAAW,WAAW,EAC1G,EAAE,cAAc;IAEpB;IAEA,IAAI,aAAa;QACf,IAAI,yBACF;IAEJ;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,uCAAiB,SAAS;YAC5B;YAEA,sFAAsF;YACtF,4CAA4C;YAC5C,OAAO,KAAK,CAAC,SAAS,GAAG;YACzB,sBAAsB;gBACpB,OAAO,KAAK,CAAC,SAAS,GAAG;gBAEzB,qFAAqF;gBACrF,wFAAwF;gBACxF,IAAI;oBACF,IAAI,qCAAe,MAAM,GAAG,OAAO,WAAW,EAC5C,yEAAyE;oBACzE,2CAA2C;oBAC3C,sBAAsB;wBACpB,qCAAe;oBACjB;yBAEA,+EAA+E;oBAC/E,6CAA6C;oBAC7C,qCAAe,gBAAgB,CAAC,UAAU,IAAM,qCAAe,SAAS;wBAAC,MAAM;oBAAI;;YAGzF;QACF;IACF;IAEA,IAAI,gBAAqC;IACzC,IAAI,cAAc;QAChB,IAAI,eACF;QAGF,IAAI,iBAAiB;YACnB,kEAAkE;YAClE,2FAA2F;YAC3F,OAAO,QAAQ,CAAC,GAAG;QACrB;QAEA,4DAA4D;QAC5D,0FAA0F;QAC1F,6FAA6F;QAC7F,IAAI,UAAU,OAAO,WAAW;QAChC,IAAI,UAAU,OAAO,WAAW;QAEhC,gBAAgB,CAAA,GAAA,2BAAI,EAClB,+BAAS,QAAQ,UAAU,iBAC3B,+BAAS,SAAS,eAAe,EAAE,gBAAgB,GAAG,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC,GAClH,+BAAS,SAAS,eAAe,EAAE,YAAY,WAC/C,+BAAS,SAAS,IAAI,EAAE,aAAa,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,GACpD;YACE,OAAO,QAAQ,CAAC,SAAS;QAC3B;QAGF,qFAAqF;QACrF,OAAO,QAAQ,CAAC,GAAG;IACrB;IAEA,IAAI,eAAe,CAAA,GAAA,2BAAI,EACrB,+BAAS,UAAU,cAAc,cAAc;QAAC,SAAS;QAAO,SAAS;IAAI,IAC7E,+BAAS,UAAU,aAAa,aAAa;QAAC,SAAS;QAAO,SAAS;IAAI,IAC3E,+BAAS,UAAU,YAAY,YAAY;QAAC,SAAS;QAAO,SAAS;IAAI,IACzE,+BAAS,UAAU,SAAS,SAAS;IAGvC,OAAO;QACL,2DAA2D;QAC3D,oCAAA,8CAAA;QACA,0BAAA,oCAAA;QACA;IACF;AACF;AAEA,gGAAgG;AAChG,SAAS,+BAAS,OAAoB,EAAE,KAAa,EAAE,KAAa;IAClE,IAAI,MAAM,QAAQ,KAAK,CAAC,MAAM;IAC9B,QAAQ,KAAK,CAAC,MAAM,GAAG;IAEvB,OAAO;QACL,QAAQ,KAAK,CAAC,MAAM,GAAG;IACzB;AACF;AAEA,6EAA6E;AAC7E,SAAS,+BACP,MAAyB,EACzB,KAAQ,EACR,OAA6E,EAC7E,OAA2C;IAE3C,0EAA0E;IAC1E,aAAa;IACb,OAAO,gBAAgB,CAAC,OAAO,SAAS;IACxC,OAAO;QACL,aAAa;QACb,OAAO,mBAAmB,CAAC,OAAO,SAAS;IAC7C;AACF;AAEA,SAAS,qCAAe,MAAe;IACrC,IAAI,OAAO,SAAS,gBAAgB,IAAI,SAAS,eAAe;IAChE,IAAI,aAA6B;IACjC,MAAO,cAAc,eAAe,KAAM;QACxC,0GAA0G;QAC1G,IAAI,aAAa,CAAA,GAAA,qCAAc,EAAE;QACjC,IAAI,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,IAAI,eAAe,YAAY;YACxG,IAAI,gBAAgB,WAAW,qBAAqB,GAAG,GAAG;YAC1D,IAAI,YAAY,WAAW,qBAAqB,GAAG,GAAG;YACtD,IAAI,YAAY,gBAAgB,WAAW,YAAY,EACrD,WAAW,SAAS,IAAI,YAAY;QAExC;QAEA,aAAa,WAAW,aAAa;IACvC;AACF;AAEA,SAAS,uCAAiB,MAAe;IACvC,OACE,AAAC,kBAAkB,oBAAoB,CAAC,wCAAkB,GAAG,CAAC,OAAO,IAAI,KACzE,kBAAkB,uBACjB,kBAAkB,eAAe,OAAO,iBAAiB;AAE9D", "sources": ["packages/@react-aria/overlays/src/usePreventScroll.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain, getScrollParent, isIOS, useLayoutEffect} from '@react-aria/utils';\n\ninterface PreventScrollOptions {\n  /** Whether the scroll lock is disabled. */\n  isDisabled?: boolean\n}\n\nconst visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */\nexport function usePreventScroll(options: PreventScrollOptions = {}): void {\n  let {isDisabled} = options;\n\n  useLayoutEffect(() => {\n    if (isDisabled) {\n      return;\n    }\n\n    preventScrollCount++;\n    if (preventScrollCount === 1) {\n      if (isIOS()) {\n        restore = preventScrollMobileSafari();\n      } else {\n        restore = preventScrollStandard();\n      }\n    }\n\n    return () => {\n      preventScrollCount--;\n      if (preventScrollCount === 0) {\n        restore();\n      }\n    };\n  }, [isDisabled]);\n}\n\n// For most browsers, all we need to do is set `overflow: hidden` on the root element, and\n// add some padding to prevent the page from shifting when the scrollbar is hidden.\nfunction preventScrollStandard() {\n  let scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  return chain(\n    scrollbarWidth > 0 &&\n      // Use scrollbar-gutter when supported because it also works for fixed positioned elements.\n      ('scrollbarGutter' in document.documentElement.style\n        ? setStyle(document.documentElement, 'scrollbarGutter', 'stable')\n        : setStyle(document.documentElement, 'paddingRight', `${scrollbarWidth}px`)),\n    setStyle(document.documentElement, 'overflow', 'hidden')\n  );\n}\n\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Set `overscroll-behavior: contain` on nested scrollable regions so they do not scroll the page when at\n//    the top or bottom. Work around a bug where this does not work when the element does not actually overflow\n//    by preventing default in a `touchmove` event.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n  let scrollable: Element;\n  let restoreScrollableStyles;\n  let onTouchStart = (e: TouchEvent) => {\n    // Store the nearest scrollable parent element from the element that the user touched.\n    scrollable = getScrollParent(e.target as Element, true);\n    if (scrollable === document.documentElement && scrollable === document.body) {\n      return;\n    }\n\n    // Prevent scrolling up when at the top and scrolling down when at the bottom\n    // of a nested scrollable area, otherwise mobile Safari will start scrolling\n    // the window instead.\n    if (scrollable instanceof HTMLElement && window.getComputedStyle(scrollable).overscrollBehavior === 'auto') {\n      restoreScrollableStyles = setStyle(scrollable, 'overscrollBehavior', 'contain');\n    }\n  };\n\n  let onTouchMove = (e: TouchEvent) => {\n    // Prevent scrolling the window.\n    if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n      e.preventDefault();\n      return;\n    }\n\n    // overscroll-behavior should prevent scroll chaining, but currently does not\n    // if the element doesn't actually overflow. https://bugs.webkit.org/show_bug.cgi?id=243452\n    // This checks that both the width and height do not overflow, otherwise we might\n    // block horizontal scrolling too. In that case, adding `touch-action: pan-x` to\n    // the element will prevent vertical page scrolling. We can't add that automatically\n    // because it must be set before the touchstart event.\n    if (scrollable.scrollHeight === scrollable.clientHeight && scrollable.scrollWidth === scrollable.clientWidth) {\n      e.preventDefault();\n    }\n  };\n\n  let onTouchEnd = () => {\n    if (restoreScrollableStyles) {\n      restoreScrollableStyles();\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    let target = e.target as HTMLElement;\n    if (willOpenKeyboard(target)) {\n      setupStyles();\n\n      // Apply a transform to trick Safari into thinking the input is at the top of the page\n      // so it doesn't try to scroll it into view.\n      target.style.transform = 'translateY(-2000px)';\n      requestAnimationFrame(() => {\n        target.style.transform = '';\n\n        // This will have prevented the browser from scrolling the focused element into view,\n        // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n        if (visualViewport) {\n          if (visualViewport.height < window.innerHeight) {\n            // If the keyboard is already visible, do this after one additional frame\n            // to wait for the transform to be removed.\n            requestAnimationFrame(() => {\n              scrollIntoView(target);\n            });\n          } else {\n            // Otherwise, wait for the visual viewport to resize before scrolling so we can\n            // measure the correct position to scroll to.\n            visualViewport.addEventListener('resize', () => scrollIntoView(target), {once: true});\n          }\n        }\n      });\n    }\n  };\n\n  let restoreStyles: null | (() => void) = null;\n  let setupStyles = () => {\n    if (restoreStyles) {\n      return;\n    }\n\n    let onWindowScroll = () => {\n      // Last resort. If the window scrolled, scroll it back to the top.\n      // It should always be at the top because the body will have a negative margin (see below).\n      window.scrollTo(0, 0);\n    };\n\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n\n    restoreStyles = chain(\n      addEvent(window, 'scroll', onWindowScroll),\n      setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`),\n      setStyle(document.documentElement, 'overflow', 'hidden'),\n      setStyle(document.body, 'marginTop', `-${scrollY}px`),\n      () => {\n        window.scrollTo(scrollX, scrollY);\n      }\n    );\n\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n  };\n\n  let removeEvents = chain(\n    addEvent(document, 'touchstart', onTouchStart, {passive: false, capture: true}),\n    addEvent(document, 'touchmove', onTouchMove, {passive: false, capture: true}),\n    addEvent(document, 'touchend', onTouchEnd, {passive: false, capture: true}),\n    addEvent(document, 'focus', onFocus, true)\n  );\n\n  return () => {\n    // Restore styles and scroll the page back to where it was.\n    restoreScrollableStyles?.();\n    restoreStyles?.();\n    removeEvents();\n  };\n}\n\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element: HTMLElement, style: string, value: string) {\n  let cur = element.style[style];\n  element.style[style] = value;\n\n  return () => {\n    element.style[style] = cur;\n  };\n}\n\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent<K extends keyof GlobalEventHandlersEventMap>(\n  target: Document | Window,\n  event: K,\n  handler: (this: Document | Window, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n) {\n  // internal function, so it's ok to ignore the difficult to fix type error\n  // @ts-ignore\n  target.addEventListener(event, handler, options);\n  return () => {\n    // @ts-ignore\n    target.removeEventListener(event, handler, options);\n  };\n}\n\nfunction scrollIntoView(target: Element) {\n  let root = document.scrollingElement || document.documentElement;\n  let nextTarget: Element | null = target;\n  while (nextTarget && nextTarget !== root) {\n    // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n    let scrollable = getScrollParent(nextTarget);\n    if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== nextTarget) {\n      let scrollableTop = scrollable.getBoundingClientRect().top;\n      let targetTop = nextTarget.getBoundingClientRect().top;\n      if (targetTop > scrollableTop + nextTarget.clientHeight) {\n        scrollable.scrollTop += targetTop - scrollableTop;\n      }\n    }\n\n    nextTarget = scrollable.parentElement;\n  }\n}\n\nfunction willOpenKeyboard(target: Element) {\n  return (\n    (target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type)) ||\n    target instanceof HTMLTextAreaElement ||\n    (target instanceof HTMLElement && target.isContentEditable)\n  );\n}\n"], "names": [], "version": 3, "file": "usePreventScroll.main.js.map"}