{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AAmCM,SAAS,0CAAgB,KAA4B,EAAE,KAA0B,EAAE,GAAkC;IAC1H,IAAI,gBAAC,YAAY,iBAAE,aAAa,EAAC,GAAG,CAAA,GAAA,yCAAS,EAAE;QAC7C,GAAG,KAAK;QACR,QAAQ,MAAM,MAAM;QACpB,SAAS,MAAM,KAAK;IACtB,GAAG;IAEH,CAAA,GAAA,yCAAe,EAAE;QACf,YAAY,CAAC,MAAM,MAAM;IAC3B;IAEA,CAAA,GAAA,yCAAqB;IAErB,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,MAAM,MAAM,IAAI,IAAI,OAAO,EAC7B,OAAO,CAAA,GAAA,yCAAc,EAAE;YAAC,IAAI,OAAO;SAAC,EAAE;YAAC,gBAAgB;QAAI;IAE/D,GAAG;QAAC,MAAM,MAAM;QAAE;KAAI;IAEtB,OAAO;QACL,YAAY,CAAA,GAAA,iBAAS,EAAE;uBACvB;IACF;AACF", "sources": ["packages/@react-aria/overlays/src/useModalOverlay.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ariaHideOutside} from './ariaHideOutside';\nimport {AriaOverlayProps, useOverlay} from './useOverlay';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {mergeProps} from '@react-aria/utils';\nimport {OverlayTriggerState} from '@react-stately/overlays';\nimport {useEffect} from 'react';\nimport {useOverlayFocusContain} from './Overlay';\nimport {usePreventScroll} from './usePreventScroll';\n\nexport interface AriaModalOverlayProps extends Pick<AriaOverlayProps, 'shouldCloseOnInteractOutside'> {\n  /**\n   * Whether to close the modal when the user interacts outside it.\n   * @default false\n   */\n  isDismissable?: boolean,\n  /**\n   * Whether pressing the escape key to close the modal should be disabled.\n   * @default false\n   */\n  isKeyboardDismissDisabled?: boolean\n}\n\nexport interface ModalOverlayAria {\n  /** Props for the modal element. */\n  modalProps: DOMAttributes,\n  /** Props for the underlay element. */\n  underlayProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a modal component.\n * A modal is an overlay element which blocks interaction with elements outside it.\n */\nexport function useModalOverlay(props: AriaModalOverlayProps, state: OverlayTriggerState, ref: RefObject<HTMLElement | null>): ModalOverlayAria {\n  let {overlayProps, underlayProps} = useOverlay({\n    ...props,\n    isOpen: state.isOpen,\n    onClose: state.close\n  }, ref);\n\n  usePreventScroll({\n    isDisabled: !state.isOpen\n  });\n\n  useOverlayFocusContain();\n\n  useEffect(() => {\n    if (state.isOpen && ref.current) {\n      return ariaHideOutside([ref.current], {shouldUseInert: true});\n    }\n  }, [state.isOpen, ref]);\n\n  return {\n    modalProps: mergeProps(overlayProps),\n    underlayProps\n  };\n}\n"], "names": [], "version": 3, "file": "useModalOverlay.module.js.map"}