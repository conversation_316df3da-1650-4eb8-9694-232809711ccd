{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AA0BM,SAAS,0CAAkB,KAA0B,EAAE,KAA0B,EAAE,GAA+B;IACvH,IAAI,QAAC,IAAI,EAAC,GAAG;IACb,IAAI,UAAC,MAAM,EAAC,GAAG;IAEf,uGAAuG;IACvG,yCAAyC;IACzC,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,OAAO,IAAI,OAAO,EACpB,CAAA,GAAA,oCAAS,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,MAAM,KAAK;IAE3C;IAEA,6EAA6E;IAC7E,oDAAoD;IACpD,yEAAyE;IACzE,+CAA+C;IAC/C,IAAI,eAAgD;IACpD,IAAI,SAAS,QACX,eAAe;SACV,IAAI,SAAS,WAClB,eAAe;IAGjB,IAAI,YAAY,CAAA,GAAA,2BAAI;IACpB,OAAO;QACL,cAAc;YACZ,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB,SAAS,YAAY;YACtC,SAAS,MAAM,MAAM;QACvB;QACA,cAAc;YACZ,IAAI;QACN;IACF;AACF", "sources": ["packages/@react-aria/overlays/src/useOverlayTrigger.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaButtonProps} from '@react-types/button';\nimport {DOMProps, RefObject} from '@react-types/shared';\nimport {onCloseMap} from './useCloseOnScroll';\nimport {OverlayTriggerState} from '@react-stately/overlays';\nimport {useEffect} from 'react';\nimport {useId} from '@react-aria/utils';\n\nexport interface OverlayTriggerProps {\n  /** Type of overlay that is opened by the trigger. */\n  type: 'dialog' | 'menu' | 'listbox' | 'tree' | 'grid'\n}\n\nexport interface OverlayTriggerAria {\n  /** Props for the trigger element. */\n  triggerProps: AriaButtonProps,\n\n  /** Props for the overlay container element. */\n  overlayProps: DOMProps\n}\n\n/**\n * Handles the behavior and accessibility for an overlay trigger, e.g. a button\n * that opens a popover, menu, or other overlay that is positioned relative to the trigger.\n */\nexport function useOverlayTrigger(props: OverlayTriggerProps, state: OverlayTriggerState, ref?: RefObject<Element | null>): OverlayTriggerAria {\n  let {type} = props;\n  let {isOpen} = state;\n\n  // Backward compatibility. Share state close function with useOverlayPosition so it can close on scroll\n  // without forcing users to pass onClose.\n  useEffect(() => {\n    if (ref && ref.current) {\n      onCloseMap.set(ref.current, state.close);\n    }\n  });\n\n  // Aria 1.1 supports multiple values for aria-haspopup other than just menus.\n  // https://www.w3.org/TR/wai-aria-1.1/#aria-haspopup\n  // However, we only add it for menus for now because screen readers often\n  // announce it as a menu even for other values.\n  let ariaHasPopup: undefined | boolean | 'listbox' = undefined;\n  if (type === 'menu') {\n    ariaHasPopup = true;\n  } else if (type === 'listbox') {\n    ariaHasPopup = 'listbox';\n  }\n\n  let overlayId = useId();\n  return {\n    triggerProps: {\n      'aria-haspopup': ariaHasPopup,\n      'aria-expanded': isOpen,\n      'aria-controls': isOpen ? overlayId : undefined,\n      onPress: state.toggle\n    },\n    overlayProps: {\n      id: overlayId\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useOverlayTrigger.main.js.map"}