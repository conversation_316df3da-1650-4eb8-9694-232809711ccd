import {useUNSAFE_PortalContext as $96b38030c423d352$export$9fc1347d4195ccb3} from "./PortalProvider.module.js";
import {ClearPressResponder as $1CM7W$ClearPressResponder} from "@react-aria/interactions";
import {FocusScope as $1CM7W$FocusScope} from "@react-aria/focus";
import $1CM7W$react, {useState as $1CM7W$useState, useMemo as $1CM7W$useMemo, useContext as $1CM7W$useContext} from "react";
import $1CM7W$reactdom from "react-dom";
import {useIsSSR as $1CM7W$useIsSSR} from "@react-aria/ssr";
import {useLayoutEffect as $1CM7W$useLayoutEffect} from "@react-aria/utils";

/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 






const $337b884510726a0d$export$a2200b96afd16271 = /*#__PURE__*/ (0, $1CM7W$react).createContext(null);
function $337b884510726a0d$export$c6fdb837b070b4ff(props) {
    let isSSR = (0, $1CM7W$useIsSSR)();
    let { portalContainer: portalContainer = isSSR ? null : document.body, isExiting: isExiting } = props;
    let [contain, setContain] = (0, $1CM7W$useState)(false);
    let contextValue = (0, $1CM7W$useMemo)(()=>({
            contain: contain,
            setContain: setContain
        }), [
        contain,
        setContain
    ]);
    let { getContainer: getContainer } = (0, $96b38030c423d352$export$9fc1347d4195ccb3)();
    if (!props.portalContainer && getContainer) portalContainer = getContainer();
    if (!portalContainer) return null;
    let contents = props.children;
    if (!props.disableFocusManagement) contents = /*#__PURE__*/ (0, $1CM7W$react).createElement((0, $1CM7W$FocusScope), {
        restoreFocus: true,
        contain: (props.shouldContainFocus || contain) && !isExiting
    }, contents);
    contents = /*#__PURE__*/ (0, $1CM7W$react).createElement($337b884510726a0d$export$a2200b96afd16271.Provider, {
        value: contextValue
    }, /*#__PURE__*/ (0, $1CM7W$react).createElement((0, $1CM7W$ClearPressResponder), null, contents));
    return /*#__PURE__*/ (0, $1CM7W$reactdom).createPortal(contents, portalContainer);
}
function $337b884510726a0d$export$14c98a7594375490() {
    let ctx = (0, $1CM7W$useContext)($337b884510726a0d$export$a2200b96afd16271);
    let setContain = ctx === null || ctx === void 0 ? void 0 : ctx.setContain;
    (0, $1CM7W$useLayoutEffect)(()=>{
        setContain === null || setContain === void 0 ? void 0 : setContain(true);
    }, [
        setContain
    ]);
}


export {$337b884510726a0d$export$a2200b96afd16271 as OverlayContext, $337b884510726a0d$export$c6fdb837b070b4ff as Overlay, $337b884510726a0d$export$14c98a7594375490 as useOverlayFocusContain};
//# sourceMappingURL=Overlay.module.js.map
