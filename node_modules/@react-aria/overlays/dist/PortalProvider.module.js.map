{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAaM,MAAM,0DAA2D,CAAA,GAAA,oBAAY,EAA8B,CAAC;AAK5G,SAAS,0CAAsB,KAA0B;IAC9D,IAAI,gBAAC,YAAY,EAAC,GAAG;IACrB,IAAI,EAAC,cAAc,eAAe,EAAC,GAAG;IACtC,qBACE,gCAAC,0CAAc,QAAQ;QAAC,OAAO;YAAC,cAAc,iBAAiB,OAAO,YAAY,yBAAA,0BAAA,eAAgB;QAAe;OAC9G,MAAM,QAAQ;AAGrB;AAEO,SAAS;QACP;IAAP,OAAO,CAAA,cAAA,CAAA,GAAA,iBAAS,EAAE,wDAAX,yBAAA,cAA6B,CAAC;AACvC", "sources": ["packages/@react-aria/overlays/src/PortalProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React, {createContext, JSX, ReactNode, useContext} from 'react';\n\nexport interface PortalProviderProps {\n  /** Should return the element where we should portal to. Can clear the context by passing null. */\n  getContainer?: (() => HTMLElement | null) | null,\n  /** The content of the PortalProvider. Should contain all children that want to portal their overlays to the element returned by the provided `getContainer()`. */\n  children: ReactNode\n}\n\nexport interface PortalProviderContextValue extends Omit<PortalProviderProps, 'children'>{};\n\nexport const PortalContext: React.Context<PortalProviderContextValue> = createContext<PortalProviderContextValue>({});\n\n/**\n * Sets the portal container for all overlay elements rendered by its children.\n */\nexport function UNSAFE_PortalProvider(props: PortalProviderProps): JSX.Element {\n  let {getContainer} = props;\n  let {getContainer: ctxGetContainer} = useUNSAFE_PortalContext();\n  return (\n    <PortalContext.Provider value={{getContainer: getContainer === null ? undefined : getContainer ?? ctxGetContainer}}>\n      {props.children}\n    </PortalContext.Provider>\n  );\n}\n\nexport function useUNSAFE_PortalContext(): PortalProviderContextValue {\n  return useContext(PortalContext) ?? {};\n}\n"], "names": [], "version": 3, "file": "PortalProvider.module.js.map"}