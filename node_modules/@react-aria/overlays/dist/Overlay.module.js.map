{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;AAmCM,MAAM,0DACX,CAAA,GAAA,YAAI,EAAE,aAAa,CAAuF;AAMrG,SAAS,0CAAQ,KAAmB;IACzC,IAAI,QAAQ,CAAA,GAAA,eAAO;IACnB,IAAI,mBAAC,kBAAkB,QAAQ,OAAO,SAAS,IAAI,aAAE,SAAS,EAAC,GAAG;IAClE,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,eAAO,EAAE;IACrC,IAAI,eAAe,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;qBAAC;wBAAS;QAAU,CAAA,GAAI;QAAC;QAAS;KAAW;IAE/E,IAAI,gBAAC,YAAY,EAAC,GAAG,CAAA,GAAA,yCAAsB;IAC3C,IAAI,CAAC,MAAM,eAAe,IAAI,cAC5B,kBAAkB;IAGpB,IAAI,CAAC,iBACH,OAAO;IAGT,IAAI,WAAW,MAAM,QAAQ;IAC7B,IAAI,CAAC,MAAM,sBAAsB,EAC/B,yBACE,gCAAC,CAAA,GAAA,iBAAS;QAAE,cAAA;QAAa,SAAS,AAAC,CAAA,MAAM,kBAAkB,IAAI,OAAM,KAAM,CAAC;OACzE;IAKP,yBACE,gCAAC,0CAAe,QAAQ;QAAC,OAAO;qBAC9B,gCAAC,CAAA,GAAA,0BAAkB,SAChB;IAKP,qBAAO,CAAA,GAAA,eAAO,EAAE,YAAY,CAAC,UAAU;AACzC;AAGO,SAAS;IACd,IAAI,MAAM,CAAA,GAAA,iBAAS,EAAE;IACrB,IAAI,aAAa,gBAAA,0BAAA,IAAK,UAAU;IAChC,CAAA,GAAA,sBAAc,EAAE;QACd,uBAAA,iCAAA,WAAa;IACf,GAAG;QAAC;KAAW;AACjB", "sources": ["packages/@react-aria/overlays/src/Overlay.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ClearPressResponder} from '@react-aria/interactions';\nimport {FocusScope} from '@react-aria/focus';\nimport React, {ReactNode, useContext, useMemo, useState} from 'react';\nimport ReactDOM from 'react-dom';\nimport {useIsSSR} from '@react-aria/ssr';\nimport {useLayoutEffect} from '@react-aria/utils';\nimport {useUNSAFE_PortalContext} from './PortalProvider';\n\nexport interface OverlayProps {\n  /**\n   * The container element in which the overlay portal will be placed.\n   * @default document.body\n   */\n  portalContainer?: Element,\n  /** The overlay to render in the portal. */\n  children: ReactNode,\n  /**\n   * Disables default focus management for the overlay, including containment and restoration.\n   * This option should be used very carefully. When focus management is disabled, you must\n   * implement focus containment and restoration to ensure the overlay is keyboard accessible.\n   */\n  disableFocusManagement?: boolean,\n  /**\n   * Whether to contain focus within the overlay.\n   */\n  shouldContainFocus?: boolean,\n  /**\n   * Whether the overlay is currently performing an exit animation. When true,\n   * focus is allowed to move outside.\n   */\n  isExiting?: boolean\n}\n\nexport const OverlayContext: React.Context<{contain: boolean, setContain: React.Dispatch<React.SetStateAction<boolean>>} | null> =\n  React.createContext<{contain: boolean, setContain: React.Dispatch<React.SetStateAction<boolean>>} | null>(null);\n\n/**\n * A container which renders an overlay such as a popover or modal in a portal,\n * and provides a focus scope for the child elements.\n */\nexport function Overlay(props: OverlayProps): React.ReactPortal | null {\n  let isSSR = useIsSSR();\n  let {portalContainer = isSSR ? null : document.body, isExiting} = props;\n  let [contain, setContain] = useState(false);\n  let contextValue = useMemo(() => ({contain, setContain}), [contain, setContain]);\n\n  let {getContainer} = useUNSAFE_PortalContext();\n  if (!props.portalContainer && getContainer) {\n    portalContainer = getContainer();\n  }\n\n  if (!portalContainer) {\n    return null;\n  }\n\n  let contents = props.children;\n  if (!props.disableFocusManagement) {\n    contents = (\n      <FocusScope restoreFocus contain={(props.shouldContainFocus || contain) && !isExiting}>\n        {contents}\n      </FocusScope>\n    );\n  }\n\n  contents = (\n    <OverlayContext.Provider value={contextValue}>\n      <ClearPressResponder>\n        {contents}\n      </ClearPressResponder>\n    </OverlayContext.Provider>\n  );\n\n  return ReactDOM.createPortal(contents, portalContainer);\n}\n\n/** @private */\nexport function useOverlayFocusContain(): void {\n  let ctx = useContext(OverlayContext);\n  let setContain = ctx?.setContain;\n  useLayoutEffect(() => {\n    setContain?.(true);\n  }, [setContain]);\n}\n"], "names": [], "version": 3, "file": "Overlay.module.js.map"}