import $k51So$arAEmodulejs from "./ar-AE.module.js";
import $k51So$bgBGmodulejs from "./bg-BG.module.js";
import $k51So$csCZmodulejs from "./cs-CZ.module.js";
import $k51So$daDKmodulejs from "./da-DK.module.js";
import $k51So$deDEmodulejs from "./de-DE.module.js";
import $k51So$elGRmodulejs from "./el-GR.module.js";
import $k51So$enUSmodulejs from "./en-US.module.js";
import $k51So$esESmodulejs from "./es-ES.module.js";
import $k51So$etEEmodulejs from "./et-EE.module.js";
import $k51So$fiFImodulejs from "./fi-FI.module.js";
import $k51So$frFRmodulejs from "./fr-FR.module.js";
import $k51So$heILmodulejs from "./he-IL.module.js";
import $k51So$hrHRmodulejs from "./hr-HR.module.js";
import $k51So$huHUmodulejs from "./hu-HU.module.js";
import $k51So$itITmodulejs from "./it-IT.module.js";
import $k51So$jaJPmodulejs from "./ja-JP.module.js";
import $k51So$koKRmodulejs from "./ko-KR.module.js";
import $k51So$ltLTmodulejs from "./lt-LT.module.js";
import $k51So$lvLVmodulejs from "./lv-LV.module.js";
import $k51So$nbNOmodulejs from "./nb-NO.module.js";
import $k51So$nlNLmodulejs from "./nl-NL.module.js";
import $k51So$plPLmodulejs from "./pl-PL.module.js";
import $k51So$ptBRmodulejs from "./pt-BR.module.js";
import $k51So$ptPTmodulejs from "./pt-PT.module.js";
import $k51So$roROmodulejs from "./ro-RO.module.js";
import $k51So$ruRUmodulejs from "./ru-RU.module.js";
import $k51So$skSKmodulejs from "./sk-SK.module.js";
import $k51So$slSImodulejs from "./sl-SI.module.js";
import $k51So$srSPmodulejs from "./sr-SP.module.js";
import $k51So$svSEmodulejs from "./sv-SE.module.js";
import $k51So$trTRmodulejs from "./tr-TR.module.js";
import $k51So$ukUAmodulejs from "./uk-UA.module.js";
import $k51So$zhCNmodulejs from "./zh-CN.module.js";
import $k51So$zhTWmodulejs from "./zh-TW.module.js";

var $a2f21f5f14f60553$exports = {};


































$a2f21f5f14f60553$exports = {
    "ar-AE": $k51So$arAEmodulejs,
    "bg-BG": $k51So$bgBGmodulejs,
    "cs-CZ": $k51So$csCZmodulejs,
    "da-DK": $k51So$daDKmodulejs,
    "de-DE": $k51So$deDEmodulejs,
    "el-GR": $k51So$elGRmodulejs,
    "en-US": $k51So$enUSmodulejs,
    "es-ES": $k51So$esESmodulejs,
    "et-EE": $k51So$etEEmodulejs,
    "fi-FI": $k51So$fiFImodulejs,
    "fr-FR": $k51So$frFRmodulejs,
    "he-IL": $k51So$heILmodulejs,
    "hr-HR": $k51So$hrHRmodulejs,
    "hu-HU": $k51So$huHUmodulejs,
    "it-IT": $k51So$itITmodulejs,
    "ja-JP": $k51So$jaJPmodulejs,
    "ko-KR": $k51So$koKRmodulejs,
    "lt-LT": $k51So$ltLTmodulejs,
    "lv-LV": $k51So$lvLVmodulejs,
    "nb-NO": $k51So$nbNOmodulejs,
    "nl-NL": $k51So$nlNLmodulejs,
    "pl-PL": $k51So$plPLmodulejs,
    "pt-BR": $k51So$ptBRmodulejs,
    "pt-PT": $k51So$ptPTmodulejs,
    "ro-RO": $k51So$roROmodulejs,
    "ru-RU": $k51So$ruRUmodulejs,
    "sk-SK": $k51So$skSKmodulejs,
    "sl-SI": $k51So$slSImodulejs,
    "sr-SP": $k51So$srSPmodulejs,
    "sv-SE": $k51So$svSEmodulejs,
    "tr-TR": $k51So$trTRmodulejs,
    "uk-UA": $k51So$ukUAmodulejs,
    "zh-CN": $k51So$zhCNmodulejs,
    "zh-TW": $k51So$zhTWmodulejs
};


export {$a2f21f5f14f60553$exports as default};
//# sourceMappingURL=intlStrings.module.js.map
