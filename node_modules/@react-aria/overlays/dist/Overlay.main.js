var $d7347c25ab757295$exports = require("./PortalProvider.main.js");
var $kxJqT$reactariainteractions = require("@react-aria/interactions");
var $kxJqT$reactariafocus = require("@react-aria/focus");
var $kxJqT$react = require("react");
var $kxJqT$reactdom = require("react-dom");
var $kxJqT$reactariassr = require("@react-aria/ssr");
var $kxJqT$reactariautils = require("@react-aria/utils");


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "Overlay", () => $745edbb83ab4296f$export$c6fdb837b070b4ff);
$parcel$export(module.exports, "useOverlayFocusContain", () => $745edbb83ab4296f$export$14c98a7594375490);
/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 






const $745edbb83ab4296f$export$a2200b96afd16271 = /*#__PURE__*/ (0, ($parcel$interopDefault($kxJqT$react))).createContext(null);
function $745edbb83ab4296f$export$c6fdb837b070b4ff(props) {
    let isSSR = (0, $kxJqT$reactariassr.useIsSSR)();
    let { portalContainer: portalContainer = isSSR ? null : document.body, isExiting: isExiting } = props;
    let [contain, setContain] = (0, $kxJqT$react.useState)(false);
    let contextValue = (0, $kxJqT$react.useMemo)(()=>({
            contain: contain,
            setContain: setContain
        }), [
        contain,
        setContain
    ]);
    let { getContainer: getContainer } = (0, $d7347c25ab757295$exports.useUNSAFE_PortalContext)();
    if (!props.portalContainer && getContainer) portalContainer = getContainer();
    if (!portalContainer) return null;
    let contents = props.children;
    if (!props.disableFocusManagement) contents = /*#__PURE__*/ (0, ($parcel$interopDefault($kxJqT$react))).createElement((0, $kxJqT$reactariafocus.FocusScope), {
        restoreFocus: true,
        contain: (props.shouldContainFocus || contain) && !isExiting
    }, contents);
    contents = /*#__PURE__*/ (0, ($parcel$interopDefault($kxJqT$react))).createElement($745edbb83ab4296f$export$a2200b96afd16271.Provider, {
        value: contextValue
    }, /*#__PURE__*/ (0, ($parcel$interopDefault($kxJqT$react))).createElement((0, $kxJqT$reactariainteractions.ClearPressResponder), null, contents));
    return /*#__PURE__*/ (0, ($parcel$interopDefault($kxJqT$reactdom))).createPortal(contents, portalContainer);
}
function $745edbb83ab4296f$export$14c98a7594375490() {
    let ctx = (0, $kxJqT$react.useContext)($745edbb83ab4296f$export$a2200b96afd16271);
    let setContain = ctx === null || ctx === void 0 ? void 0 : ctx.setContain;
    (0, $kxJqT$reactariautils.useLayoutEffect)(()=>{
        setContain === null || setContain === void 0 ? void 0 : setContain(true);
    }, [
        setContain
    ]);
}


//# sourceMappingURL=Overlay.main.js.map
