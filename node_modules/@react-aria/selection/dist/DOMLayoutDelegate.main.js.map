{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AAKM,MAAM;IAOX,YAAY,GAAQ,EAAe;QACjC,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,OAAO;QAChC,IAAI,CAAC,WACH,OAAO;QAET,IAAI,OAAO,OAAO,OAAO,CAAA,GAAA,wCAAa,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO;QACzD,IAAI,CAAC,MACH,OAAO;QAGT,IAAI,gBAAgB,UAAU,qBAAqB;QACnD,IAAI,WAAW,KAAK,qBAAqB;QAEzC,OAAO;YACL,GAAG,SAAS,IAAI,GAAG,cAAc,IAAI,GAAG,UAAU,UAAU;YAC5D,GAAG,SAAS,GAAG,GAAG,cAAc,GAAG,GAAG,UAAU,SAAS;YACzD,OAAO,SAAS,KAAK;YACrB,QAAQ,SAAS,MAAM;QACzB;IACF;IAEA,iBAAuB;QACrB,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,OAAO;YAEvB,wBACC;QAFV,OAAO;YACL,OAAO,CAAA,yBAAA,sBAAA,gCAAA,UAAW,WAAW,cAAtB,oCAAA,yBAA0B;YACjC,QAAQ,CAAA,0BAAA,sBAAA,gCAAA,UAAW,YAAY,cAAvB,qCAAA,0BAA2B;QACrC;IACF;IAEA,iBAAuB;QACrB,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,OAAO;YAE3B,uBACA,sBACI,wBACC;QAJV,OAAO;YACL,GAAG,CAAA,wBAAA,sBAAA,gCAAA,UAAW,UAAU,cAArB,mCAAA,wBAAyB;YAC5B,GAAG,CAAA,uBAAA,sBAAA,gCAAA,UAAW,SAAS,cAApB,kCAAA,uBAAwB;YAC3B,OAAO,CAAA,yBAAA,sBAAA,gCAAA,UAAW,WAAW,cAAtB,oCAAA,yBAA0B;YACjC,QAAQ,CAAA,0BAAA,sBAAA,gCAAA,UAAW,YAAY,cAAvB,qCAAA,0BAA2B;QACrC;IACF;IAzCA,YAAY,GAAkC,CAAE;QAC9C,IAAI,CAAC,GAAG,GAAG;IACb;AAwCF", "sources": ["packages/@react-aria/selection/src/DOMLayoutDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getItemElement} from './utils';\nimport {Key, LayoutDelegate, Rect, RefObject, Size} from '@react-types/shared';\n\nexport class DOMLayoutDelegate implements LayoutDelegate {\n  private ref: RefObject<HTMLElement | null>;\n\n  constructor(ref: RefObject<HTMLElement | null>) {\n    this.ref = ref;\n  }\n\n  getItemRect(key: Key): Rect | null {\n    let container = this.ref.current;\n    if (!container) {\n      return null;\n    }\n    let item = key != null ? getItemElement(this.ref, key) : null;\n    if (!item) {\n      return null;\n    }\n\n    let containerRect = container.getBoundingClientRect();\n    let itemRect = item.getBoundingClientRect();\n\n    return {\n      x: itemRect.left - containerRect.left + container.scrollLeft,\n      y: itemRect.top - containerRect.top + container.scrollTop,\n      width: itemRect.width,\n      height: itemRect.height\n    };\n  }\n\n  getContentSize(): Size {\n    let container = this.ref.current;\n    return {\n      width: container?.scrollWidth ?? 0,\n      height: container?.scrollHeight ?? 0\n    };\n  }\n\n  getVisibleRect(): Rect {\n    let container = this.ref.current;\n    return {\n      x: container?.scrollLeft ?? 0,\n      y: container?.scrollTop ?? 0,\n      width: container?.offsetWidth ?? 0,\n      height: container?.offsetHeight ?? 0\n    };\n  }\n}\n"], "names": [], "version": 3, "file": "DOMLayoutDelegate.main.js.map"}