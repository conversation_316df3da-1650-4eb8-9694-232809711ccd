{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAkBM,MAAM;IA4CH,WAAW,IAAmB,EAAE;YACK;QAA3C,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAU,CAAA,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,UAAU,KAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,CAAA;IACrG;IAEQ,oBAAoB,GAAe,EAAE,OAAiC,EAAc;QAC1F,IAAI,UAAU;QACd,MAAO,WAAW,KAAM;YACtB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,OAC5C,OAAO;YAGT,UAAU,QAAQ;QACpB;QAEA,OAAO;IACT;IAEA,WAAW,GAAQ,EAAc;QAC/B,IAAI,UAAsB;QAC1B,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;QACtC,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;IAC9E;IAEA,eAAe,GAAQ,EAAc;QACnC,IAAI,UAAsB;QAC1B,UAAU,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QACvC,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IAC/E;IAEQ,QACN,GAAQ,EACR,OAAiC,EACjC,UAAuD,EACvD;QACA,IAAI,UAAsB;QAC1B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,YAAY,WAAW,MAC1B,OAAO;QAGT,mDAAmD;QACnD,IAAI,WAAW;QACf,GAAG;YACD,UAAU,QAAQ;YAClB,IAAI,WAAW,MACb;YAEF,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC7C,QAAS,YAAY,WAAW,UAAU,aAAa,WAAW,MAAM;QAExE,OAAO;IACT;IAEQ,UAAU,QAAc,EAAE,QAAc,EAAE;QAChD,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC,IAAI,SAAS,CAAC,KAAK,SAAS,CAAC;IAC/D;IAEQ,aAAa,QAAc,EAAE,QAAc,EAAE;QACnD,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC,IAAI,SAAS,CAAC,KAAK,SAAS,CAAC;IAC/D;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,WAAW,KAAK,YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,SAAS;aAEtE,OAAO,IAAI,CAAC,UAAU,CAAC;IAE3B;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,WAAW,KAAK,YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,SAAS;aAE1E,OAAO,IAAI,CAAC,cAAc,CAAC;IAE/B;IAEQ,cAAc,GAAQ,EAAE,KAAc,EAAE;QAC9C,OAAO,QAAQ,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC;IAC5D;IAEA,cAAe,GAAQ,EAAc;QACnC,uFAAuF;QACvF,gFAAgF;QAChF,IAAI,uBAAuB,IAAI,CAAC,SAAS,KAAK,QAAQ,kBAAkB;QACxE,IAAI,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;YAChD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;QACxF;QAEA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,YACvB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;iBAElD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,YAAY;QAE1G,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,cAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;QAGpD,OAAO;IACT;IAEA,aAAc,GAAQ,EAAc;QAClC,IAAI,uBAAuB,IAAI,CAAC,SAAS,KAAK,QAAQ,iBAAiB;QACvE,IAAI,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;YAChD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;QACxF;QAEA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,YACvB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;iBAElD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,YAAY;QAE1G,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,cAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;QAGpD,OAAO;IACT;IAEA,cAA0B;QACxB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;QACrC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;IAC1E;IAEA,aAAyB;QACvB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU;QACpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IAC3E;IAEA,gBAAgB,GAAQ,EAAc;QACpC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO;QAC3B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,QAAQ,CAAC,CAAA,GAAA,kCAAW,EAAE,OACxB,OAAO,IAAI,CAAC,WAAW;QAGzB,IAAI,UAAsB;QAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc;YACrC,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,KAAK;YAEhG,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF,OAAO;YACL,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;YAElG,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF;QAEA,OAAO,oBAAA,qBAAA,UAAW,IAAI,CAAC,WAAW;IACpC;IAEA,gBAAgB,GAAQ,EAAc;QACpC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO;QAC3B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,QAAQ,CAAC,CAAA,GAAA,kCAAW,EAAE,OACxB,OAAO,IAAI,CAAC,UAAU;QAGxB,IAAI,UAAsB;QAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc;YACrC,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,KAAK,EAAE,SAAS,CAAC,GAAG,SAAS,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,KAAK;YAEzI,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF,OAAO;YACL,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,EAAE,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;YAE5I,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF;QAEA,OAAO,oBAAA,qBAAA,UAAW,IAAI,CAAC,UAAU;IACnC;IAEA,gBAAgB,MAAc,EAAE,OAAa,EAAc;QACzD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAChB,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,MAAM,WAAW,IAAI,CAAC,WAAW;QACrC,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,WAAW,OAAO,CAAC;YAC9B,IAAI,CAAC,MACH,OAAO;YAET,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;YACrD,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GACjE,OAAO;YAGT,MAAM,IAAI,CAAC,UAAU,CAAC;QACxB;QAEA,OAAO;IACT;IAxPA,YAAY,GAAG,IAAW,CAAE;QAC1B,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;YACjC,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;YACnB,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;YAC7B,IAAI,CAAC,YAAY,GAAG,KAAK,YAAY,IAAI,IAAI;YAC7C,IAAI,CAAC,gBAAgB,GAAG,KAAK,gBAAgB,IAAI;YACjD,IAAI,CAAC,WAAW,GAAG,KAAK,WAAW,IAAI;YACvC,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;YAC/B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;YAC7B,IAAI,CAAC,cAAc,GAAG,KAAK,cAAc,IAAI,IAAI,CAAA,GAAA,2CAAgB,EAAE,KAAK,GAAG;QAC7E,OAAO;YACL,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA,GAAA,2CAAgB,EAAE,IAAI,CAAC,GAAG;QACtD;QAEA,wEAAwE;QACxE,mDAAmD;QACnD,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,WAAW,KAAK,YAAY;YAC9D,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;AA4NF", "sources": ["packages/@react-aria/selection/src/ListKeyboardDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, Direction, DisabledBehavior, Key, KeyboardDelegate, LayoutDelegate, Node, Orientation, Rect, RefObject} from '@react-types/shared';\nimport {DOMLayoutDelegate} from './DOMLayoutDelegate';\nimport {isScrollable} from '@react-aria/utils';\n\ninterface ListKeyboardDelegateOptions<T> {\n  collection: Collection<Node<T>>,\n  ref: RefObject<HTMLElement | null>,\n  collator?: Intl.Collator,\n  layout?: 'stack' | 'grid',\n  orientation?: Orientation,\n  direction?: Direction,\n  disabledKeys?: Set<Key>,\n  disabledBehavior?: DisabledBehavior,\n  layoutDelegate?: LayoutDelegate\n}\n\nexport class ListKeyboardDelegate<T> implements KeyboardDelegate {\n  private collection: Collection<Node<T>>;\n  private disabledKeys: Set<Key>;\n  private disabledBehavior: DisabledBehavior;\n  private ref: RefObject<HTMLElement | null>;\n  private collator: Intl.Collator | undefined;\n  private layout: 'stack' | 'grid';\n  private orientation?: Orientation;\n  private direction?: Direction;\n  private layoutDelegate: LayoutDelegate;\n\n  constructor(collection: Collection<Node<T>>, disabledKeys: Set<Key>, ref: RefObject<HTMLElement | null>, collator?: Intl.Collator);\n  constructor(options: ListKeyboardDelegateOptions<T>);\n  constructor(...args: any[]) {\n    if (args.length === 1) {\n      let opts = args[0] as ListKeyboardDelegateOptions<T>;\n      this.collection = opts.collection;\n      this.ref = opts.ref;\n      this.collator = opts.collator;\n      this.disabledKeys = opts.disabledKeys || new Set();\n      this.disabledBehavior = opts.disabledBehavior || 'all';\n      this.orientation = opts.orientation || 'vertical';\n      this.direction = opts.direction;\n      this.layout = opts.layout || 'stack';\n      this.layoutDelegate = opts.layoutDelegate || new DOMLayoutDelegate(opts.ref);\n    } else {\n      this.collection = args[0];\n      this.disabledKeys = args[1];\n      this.ref = args[2];\n      this.collator = args[3];\n      this.layout = 'stack';\n      this.orientation = 'vertical';\n      this.disabledBehavior = 'all';\n      this.layoutDelegate = new DOMLayoutDelegate(this.ref);\n    }\n\n    // If this is a vertical stack, remove the left/right methods completely\n    // so they aren't called by useDroppableCollection.\n    if (this.layout === 'stack' && this.orientation === 'vertical') {\n      this.getKeyLeftOf = undefined;\n      this.getKeyRightOf = undefined;\n    }\n  }\n\n  private isDisabled(item: Node<unknown>) {\n    return this.disabledBehavior === 'all' && (item.props?.isDisabled || this.disabledKeys.has(item.key));\n  }\n\n  private findNextNonDisabled(key: Key | null, getNext: (key: Key) => Key | null): Key | null {\n    let nextKey = key;\n    while (nextKey != null) {\n      let item = this.collection.getItem(nextKey);\n      if (item?.type === 'item' && !this.isDisabled(item)) {\n        return nextKey;\n      }\n\n      nextKey = getNext(nextKey);\n    }\n\n    return null;\n  }\n\n  getNextKey(key: Key): Key | null {\n    let nextKey: Key | null = key;\n    nextKey = this.collection.getKeyAfter(nextKey);\n    return this.findNextNonDisabled(nextKey, key => this.collection.getKeyAfter(key));\n  }\n\n  getPreviousKey(key: Key): Key | null {\n    let nextKey: Key | null = key;\n    nextKey = this.collection.getKeyBefore(nextKey);\n    return this.findNextNonDisabled(nextKey, key => this.collection.getKeyBefore(key));\n  }\n\n  private findKey(\n    key: Key,\n    nextKey: (key: Key) => Key | null,\n    shouldSkip: (prevRect: Rect, itemRect: Rect) => boolean\n  ) {\n    let tempKey: Key | null = key;\n    let itemRect = this.layoutDelegate.getItemRect(tempKey);\n    if (!itemRect || tempKey == null) {\n      return null;\n    }\n\n    // Find the item above or below in the same column.\n    let prevRect = itemRect;\n    do {\n      tempKey = nextKey(tempKey);\n      if (tempKey == null) {\n        break;\n      }\n      itemRect = this.layoutDelegate.getItemRect(tempKey);\n    } while (itemRect && shouldSkip(prevRect, itemRect) && tempKey != null);\n\n    return tempKey;\n  }\n\n  private isSameRow(prevRect: Rect, itemRect: Rect) {\n    return prevRect.y === itemRect.y || prevRect.x !== itemRect.x;\n  }\n\n  private isSameColumn(prevRect: Rect, itemRect: Rect) {\n    return prevRect.x === itemRect.x || prevRect.y !== itemRect.y;\n  }\n\n  getKeyBelow(key: Key): Key | null {\n    if (this.layout === 'grid' && this.orientation === 'vertical') {\n      return this.findKey(key, (key) => this.getNextKey(key), this.isSameRow);\n    } else {\n      return this.getNextKey(key);\n    }\n  }\n\n  getKeyAbove(key: Key): Key | null {\n    if (this.layout === 'grid' && this.orientation === 'vertical') {\n      return this.findKey(key, (key) => this.getPreviousKey(key), this.isSameRow);\n    } else {\n      return this.getPreviousKey(key);\n    }\n  }\n\n  private getNextColumn(key: Key, right: boolean) {\n    return right ? this.getPreviousKey(key) : this.getNextKey(key);\n  }\n\n  getKeyRightOf?(key: Key): Key | null {\n    // This is a temporary solution for CardView until we refactor useSelectableCollection.\n    // https://github.com/orgs/adobe/projects/19/views/32?pane=issue&itemId=77825042\n    let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyRightOf' : 'getKeyLeftOf';\n    if (this.layoutDelegate[layoutDelegateMethod]) {\n      key = this.layoutDelegate[layoutDelegateMethod](key);\n      return this.findNextNonDisabled(key, key => this.layoutDelegate[layoutDelegateMethod](key));\n    }\n\n    if (this.layout === 'grid') {\n      if (this.orientation === 'vertical') {\n        return this.getNextColumn(key, this.direction === 'rtl');\n      } else {\n        return this.findKey(key, (key) => this.getNextColumn(key, this.direction === 'rtl'), this.isSameColumn);\n      }\n    } else if (this.orientation === 'horizontal') {\n      return this.getNextColumn(key, this.direction === 'rtl');\n    }\n\n    return null;\n  }\n\n  getKeyLeftOf?(key: Key): Key | null {\n    let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyLeftOf' : 'getKeyRightOf';\n    if (this.layoutDelegate[layoutDelegateMethod]) {\n      key = this.layoutDelegate[layoutDelegateMethod](key);\n      return this.findNextNonDisabled(key, key => this.layoutDelegate[layoutDelegateMethod](key));\n    }\n\n    if (this.layout === 'grid') {\n      if (this.orientation === 'vertical') {\n        return this.getNextColumn(key, this.direction === 'ltr');\n      } else {\n        return this.findKey(key, (key) => this.getNextColumn(key, this.direction === 'ltr'), this.isSameColumn);\n      }\n    } else if (this.orientation === 'horizontal') {\n      return this.getNextColumn(key, this.direction === 'ltr');\n    }\n\n    return null;\n  }\n\n  getFirstKey(): Key | null {\n    let key = this.collection.getFirstKey();\n    return this.findNextNonDisabled(key, key => this.collection.getKeyAfter(key));\n  }\n\n  getLastKey(): Key | null {\n    let key = this.collection.getLastKey();\n    return this.findNextNonDisabled(key, key => this.collection.getKeyBefore(key));\n  }\n\n  getKeyPageAbove(key: Key): Key | null {\n    let menu = this.ref.current;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n    if (!itemRect) {\n      return null;\n    }\n\n    if (menu && !isScrollable(menu)) {\n      return this.getFirstKey();\n    }\n\n    let nextKey: Key | null = key;\n    if (this.orientation === 'horizontal') {\n      let pageX = Math.max(0, itemRect.x + itemRect.width - this.layoutDelegate.getVisibleRect().width);\n\n      while (itemRect && itemRect.x > pageX && nextKey != null) {\n        nextKey = this.getKeyAbove(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    } else {\n      let pageY = Math.max(0, itemRect.y + itemRect.height - this.layoutDelegate.getVisibleRect().height);\n\n      while (itemRect && itemRect.y > pageY && nextKey != null) {\n        nextKey = this.getKeyAbove(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    }\n\n    return nextKey ?? this.getFirstKey();\n  }\n\n  getKeyPageBelow(key: Key): Key | null {\n    let menu = this.ref.current;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n    if (!itemRect) {\n      return null;\n    }\n\n    if (menu && !isScrollable(menu)) {\n      return this.getLastKey();\n    }\n\n    let nextKey: Key | null = key;\n    if (this.orientation === 'horizontal') {\n      let pageX = Math.min(this.layoutDelegate.getContentSize().width, itemRect.y - itemRect.width + this.layoutDelegate.getVisibleRect().width);\n\n      while (itemRect && itemRect.x < pageX && nextKey != null) {\n        nextKey = this.getKeyBelow(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    } else {\n      let pageY = Math.min(this.layoutDelegate.getContentSize().height, itemRect.y - itemRect.height + this.layoutDelegate.getVisibleRect().height);\n\n      while (itemRect && itemRect.y < pageY && nextKey != null) {\n        nextKey = this.getKeyBelow(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    }\n\n    return nextKey ?? this.getLastKey();\n  }\n\n  getKeyForSearch(search: string, fromKey?: Key): Key | null {\n    if (!this.collator) {\n      return null;\n    }\n\n    let collection = this.collection;\n    let key = fromKey || this.getFirstKey();\n    while (key != null) {\n      let item = collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      let substring = item.textValue.slice(0, search.length);\n      if (item.textValue && this.collator.compare(substring, search) === 0) {\n        return key;\n      }\n\n      key = this.getNextKey(key);\n    }\n\n    return null;\n  }\n}\n"], "names": [], "version": 3, "file": "ListKeyboardDelegate.main.js.map"}