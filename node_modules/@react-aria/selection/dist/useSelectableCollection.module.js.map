{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;;AAgGM,SAAS,0CAAwB,OAAwC;IAC9E,IAAI,EACF,kBAAkB,OAAO,EACzB,kBAAkB,QAAQ,OAC1B,GAAG,aACH,YAAY,wBACZ,kBAAkB,+BAClB,yBAAyB,0BACzB,oBAAoB,0BACpB,oBAAoB,iCACpB,gBAAgB,QAAQ,iBAAiB,KAAK,8BAC9C,oBAAoB,8BACpB,qBAAqB,uBACrB,sBAAsB,sBACtB,aAAa,aACb,kFAAkF;IAClF,YAAY,mBACZ,eAAe,UAChB,GAAG;IACJ,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAC1B,IAAI,SAAS,CAAA,GAAA,gBAAQ;IAErB,IAAI,YAAY,CAAC;YAQV;QAPL,6GAA6G;QAC7G,IAAI,EAAE,MAAM,IAAI,EAAE,GAAG,KAAK,OACxB,EAAE,cAAc;QAGlB,uEAAuE;QACvE,oDAAoD;QACpD,IAAI,GAAC,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,QAAQ,CAAC,EAAE,MAAM,IACjC;QAGF,MAAM,gBAAgB,CAAC,KAAsB;YAC3C,IAAI,OAAO,MAAM;gBACf,IAAI,QAAQ,MAAM,CAAC,QAAQ,iBAAiB,eAAe,iBAAiB,CAAC,CAAA,GAAA,yCAA+B,EAAE,IAAI;oBAChH,iFAAiF;oBACjF,CAAA,GAAA,gBAAQ,EAAE;wBACR,QAAQ,aAAa,CAAC,KAAK;oBAC7B;oBAEA,IAAI,OAAO,CAAA,GAAA,yCAAa,EAAE,KAAK;oBAC/B,IAAI,YAAY,QAAQ,YAAY,CAAC;oBACrC,IAAI,MACF,OAAO,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,EAAE,UAAU,aAAa;oBAG9D;gBACF;gBAEA,QAAQ,aAAa,CAAC,KAAK;gBAE3B,IAAI,QAAQ,MAAM,CAAC,QAAQ,iBAAiB,YAC1C;gBAGF,IAAI,EAAE,QAAQ,IAAI,QAAQ,aAAa,KAAK,YAC1C,QAAQ,eAAe,CAAC;qBACnB,IAAI,iBAAiB,CAAC,CAAA,GAAA,yCAA+B,EAAE,IAC5D,QAAQ,gBAAgB,CAAC;YAE7B;QACF;QAEA,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,IAAI,SAAS,WAAW,EAAE;wBAElB,uBACA,uBAEM;oBAJZ,IAAI,UAAU,QAAQ,UAAU,IAAI,QAC9B,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA,UAAuB,QAAQ,UAAU,KACzC,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA;oBACN,IAAI,WAAW,QAAQ,iBACrB,WAAU,yBAAA,SAAS,WAAW,cAApB,6CAAA,4BAAA,UAAuB,QAAQ,UAAU;oBAErD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,WAAW,EAAE;wBAElB,uBACA,sBAEM;oBAJZ,IAAI,UAAU,QAAQ,UAAU,IAAI,QAC9B,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA,UAAuB,QAAQ,UAAU,KACzC,uBAAA,SAAS,UAAU,cAAnB,2CAAA,0BAAA;oBACN,IAAI,WAAW,QAAQ,iBACrB,WAAU,wBAAA,SAAS,UAAU,cAAnB,4CAAA,2BAAA,UAAsB,QAAQ,UAAU;oBAEpD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,YAAY,EAAE;wBAC0C,wBAEjC,wBAA6C;oBAF/E,IAAI,UAAkC,QAAQ,UAAU,IAAI,QAAO,yBAAA,SAAS,YAAY,cAArB,6CAAA,4BAAA,UAAwB,QAAQ,UAAU,IAAI;oBACjH,IAAI,WAAW,QAAQ,iBACrB,UAAU,cAAc,SAAQ,yBAAA,SAAS,WAAW,cAApB,6CAAA,4BAAA,UAAuB,QAAQ,UAAU,KAAI,wBAAA,SAAS,UAAU,cAAnB,4CAAA,2BAAA,UAAsB,QAAQ,UAAU;oBAEvH,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc,SAAS,cAAc,QAAQ,UAAU;oBACzD;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,aAAa,EAAE;wBACyC,yBAEjC,uBAA4C;oBAF9E,IAAI,UAAkC,QAAQ,UAAU,IAAI,QAAO,0BAAA,SAAS,aAAa,cAAtB,8CAAA,6BAAA,UAAyB,QAAQ,UAAU,IAAI;oBAClH,IAAI,WAAW,QAAQ,iBACrB,UAAU,cAAc,SAAQ,wBAAA,SAAS,UAAU,cAAnB,4CAAA,2BAAA,UAAsB,QAAQ,UAAU,KAAI,yBAAA,SAAS,WAAW,cAApB,6CAAA,4BAAA,UAAuB,QAAQ,UAAU;oBAEvH,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc,SAAS,cAAc,QAAQ,SAAS;oBACxD;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,WAAW,EAAE;oBACxB,IAAI,QAAQ,UAAU,KAAK,QAAQ,EAAE,QAAQ,EAC3C;oBAEF,EAAE,cAAc;oBAChB,IAAI,WAAuB,SAAS,WAAW,CAAC,QAAQ,UAAU,EAAE,CAAA,GAAA,uBAAe,EAAE;oBACrF,QAAQ,aAAa,CAAC;oBACtB,IAAI,YAAY,MAAM;wBACpB,IAAI,CAAA,GAAA,uBAAe,EAAE,MAAM,EAAE,QAAQ,IAAI,QAAQ,aAAa,KAAK,YACjE,QAAQ,eAAe,CAAC;6BACnB,IAAI,eACT,QAAQ,gBAAgB,CAAC;oBAE7B;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,SAAS,UAAU,EAAE;oBACvB,IAAI,QAAQ,UAAU,KAAK,QAAQ,EAAE,QAAQ,EAC3C;oBAEF,EAAE,cAAc;oBAChB,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,UAAU,EAAE,CAAA,GAAA,uBAAe,EAAE;oBACvE,QAAQ,aAAa,CAAC;oBACtB,IAAI,WAAW,MAAM;wBACnB,IAAI,CAAA,GAAA,uBAAe,EAAE,MAAM,EAAE,QAAQ,IAAI,QAAQ,aAAa,KAAK,YACjE,QAAQ,eAAe,CAAC;6BACnB,IAAI,eACT,QAAQ,gBAAgB,CAAC;oBAE7B;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,SAAS,eAAe,IAAI,QAAQ,UAAU,IAAI,MAAM;oBAC1D,IAAI,UAAU,SAAS,eAAe,CAAC,QAAQ,UAAU;oBACzD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,SAAS,eAAe,IAAI,QAAQ,UAAU,IAAI,MAAM;oBAC1D,IAAI,UAAU,SAAS,eAAe,CAAC,QAAQ,UAAU;oBACzD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,CAAA,GAAA,uBAAe,EAAE,MAAM,QAAQ,aAAa,KAAK,cAAc,sBAAsB,MAAM;oBAC7F,EAAE,cAAc;oBAChB,QAAQ,SAAS;gBACnB;gBACA;YACF,KAAK;gBACH,IAAI,sBAAsB,oBAAoB,CAAC,0BAA0B,QAAQ,YAAY,CAAC,IAAI,KAAK,GAAG;oBACxG,EAAE,eAAe;oBACjB,EAAE,cAAc;oBAChB,QAAQ,cAAc;gBACxB;gBACA;YACF,KAAK;gBACH,IAAI,CAAC,qBAAqB;oBACxB,uFAAuF;oBACvF,qGAAqG;oBACrG,iGAAiG;oBACjG,6FAA6F;oBAC7F,gGAAgG;oBAChG,yCAAyC;oBACzC,IAAI,EAAE,QAAQ,EACZ,IAAI,OAAO,CAAC,KAAK;yBACZ;wBACL,IAAI,SAAS,CAAA,GAAA,6BAAqB,EAAE,IAAI,OAAO,EAAE;4BAAC,UAAU;wBAAI;wBAChE,IAAI,OAAqC;wBACzC,IAAI;wBACJ,GAAG;4BACD,OAAO,OAAO,SAAS;4BACvB,IAAI,MACF,OAAO;wBAEX,QAAS,MAAM;wBAEf,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,SAAS,aAAa,GAC/C,CAAA,GAAA,4BAAoB,EAAE;oBAE1B;oBACA;gBACF;QAEJ;IACF;IAEA,wDAAwD;IACxD,2CAA2C;IAC3C,IAAI,YAAY,CAAA,GAAA,aAAK,EAAE;QAAC,KAAK;QAAG,MAAM;IAAC;IACvC,CAAA,GAAA,eAAO,EAAE,WAAW,UAAU,gBAAgB,YAAY;YAEjD,oBACC;YADD,8BACC;QAFR,UAAU,OAAO,GAAG;YAClB,KAAK,CAAA,gCAAA,qBAAA,UAAU,OAAO,cAAjB,yCAAA,mBAAmB,SAAS,cAA5B,0CAAA,+BAAgC;YACrC,MAAM,CAAA,iCAAA,sBAAA,UAAU,OAAO,cAAjB,0CAAA,oBAAmB,UAAU,cAA7B,2CAAA,gCAAiC;QACzC;IACF;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,QAAQ,SAAS,EAAE;YACrB,gEAAgE;YAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC,QAAQ,UAAU,CAAC;YAGrB;QACF;QAEA,gEAAgE;QAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,QAAQ,UAAU,CAAC;QACnB,IAAI,QAAQ,UAAU,IAAI,MAAM;gBAca,sBAEC;YAf5C,IAAI,gBAAgB,CAAC;gBACnB,IAAI,OAAO,MAAM;oBACf,QAAQ,aAAa,CAAC;oBACtB,IAAI,iBAAiB,CAAC,QAAQ,UAAU,CAAC,MACvC,QAAQ,gBAAgB,CAAC;gBAE7B;YACF;YACA,0FAA0F;YAC1F,wFAAwF;YACxF,uDAAuD;YACvD,IAAI,gBAAgB,EAAE,aAAa;gBAEnB,0BAEA;YAHhB,IAAI,iBAAkB,EAAE,aAAa,CAAC,uBAAuB,CAAC,iBAAiB,KAAK,2BAA2B,EAC7G,cAAc,CAAA,2BAAA,QAAQ,eAAe,cAAvB,sCAAA,4BAA2B,uBAAA,SAAS,UAAU,cAAnB,2CAAA,0BAAA;iBAEzC,cAAc,CAAA,4BAAA,QAAQ,gBAAgB,cAAxB,uCAAA,6BAA4B,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA;QAE9C,OAAO,IAAI,CAAC,iBAAiB,UAAU,OAAO,EAAE;YAC9C,qDAAqD;YACrD,UAAU,OAAO,CAAC,SAAS,GAAG,UAAU,OAAO,CAAC,GAAG;YACnD,UAAU,OAAO,CAAC,UAAU,GAAG,UAAU,OAAO,CAAC,IAAI;QACvD;QAEA,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,OAAO,EAAE;YACnD,2FAA2F;YAC3F,IAAI,UAAU,CAAA,GAAA,yCAAa,EAAE,KAAK,QAAQ,UAAU;YACpD,IAAI,mBAAmB,aAAa;gBAClC,wGAAwG;gBACxG,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS,aAAa,KAAK,CAAC,uBAChD,CAAA,GAAA,4BAAoB,EAAE;gBAGxB,IAAI,WAAW,CAAA,GAAA,6BAAqB;gBACpC,IAAI,aAAa,YACf,CAAA,GAAA,yBAAiB,EAAE,SAAS;oBAAC,mBAAmB,IAAI,OAAO;gBAAA;YAE/D;QACF;IACF;IAEA,IAAI,SAAS,CAAC;QACZ,kFAAkF;QAClF,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,aAAa,GAC3C,QAAQ,UAAU,CAAC;IAEvB;IAEA,4IAA4I;IAC5I,uDAAuD;IACvD,6CAA6C;IAC7C,IAAI,0BAA0B,CAAA,GAAA,aAAK,EAAE;IACrC,8HAA8H;IAC9H,4BAA4B;IAC5B,uBAAuB;IACvB,CAAA,GAAA,eAAO,EAAE,KAAK,CAAA,GAAA,kBAAU,GAAG,CAAC,wBAAwB,YAAY,CAAC;QAC/D,IAAI,UAAC,MAAM,EAAC,GAAG;QACf,EAAE,eAAe;QACjB,QAAQ,UAAU,CAAC;QACnB,0EAA0E;QAC1E,IAAI,CAAA,mBAAA,6BAAA,OAAQ,aAAa,MAAK,SAC5B,wBAAwB,OAAO,GAAG;IAEtC;IAEA,IAAI,yBAAyB,CAAA,GAAA,qBAAa,EAAE;YACzB;YAAA;QAAjB,IAAI,aAAa,CAAA,0BAAA,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA,uBAAA,oCAAA,yBAA4B;QAE7C,+HAA+H;QAC/H,4DAA4D;QAC5D,IAAI,cAAc,MAAM;YACtB,IAAI,wBAAwB,CAAA,GAAA,uBAAe;YAC3C,CAAA,GAAA,uBAAe,EAAE,IAAI,OAAO;YAC5B,CAAA,GAAA,2BAAmB,EAAE,uBAAwB;YAE7C,mJAAmJ;YACnJ,qHAAqH;YACrH,IAAI,QAAQ,UAAU,CAAC,IAAI,GAAG,GAC5B,wBAAwB,OAAO,GAAG;QAEtC,OAAO;YACL,QAAQ,aAAa,CAAC;YACtB,uGAAuG;YACvG,sHAAsH;YACtH,0CAA0C;YAC1C,wBAAwB,OAAO,GAAG;QACpC;IACF;IAEA,CAAA,GAAA,4BAAoB,EAAE;QACpB,IAAI,wBAAwB,OAAO,EACjC;IAGJ,GAAG;QAAC,QAAQ,UAAU;QAAE;KAAuB;IAE/C,IAAI,sBAAsB,CAAA,GAAA,qBAAa,EAAE;QACvC,uGAAuG;QACvG,+GAA+G;QAC/G,+DAA+D;QAC/D,IAAI,QAAQ,UAAU,CAAC,IAAI,GAAG,GAC5B,wBAAwB,OAAO,GAAG;IAEtC;IAEA,CAAA,GAAA,4BAAoB,EAAE;QACpB;IACF,GAAG;QAAC,QAAQ,UAAU;QAAE;KAAoB;IAE5C,CAAA,GAAA,eAAO,EAAE,KAAK,CAAA,GAAA,wBAAgB,GAAG,CAAC,wBAAwB,YAAY,CAAC;YAGjE;QAFJ,EAAE,eAAe;QACjB,QAAQ,UAAU,CAAC;QACnB,KAAI,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,aAAa,EACzB,QAAQ,aAAa,CAAC;IAE1B;IAEA,MAAM,eAAe,CAAA,GAAA,aAAK,EAAE;IAC5B,MAAM,kBAAkB,CAAA,GAAA,aAAK,EAAE;IAC/B,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,aAAa,OAAO,EAAE;gBAKT,uBAEA;YANf,IAAI,aAAyB;gBAId;YAFf,wDAAwD;YACxD,IAAI,cAAc,SAChB,aAAa,CAAA,0BAAA,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA,uBAAA,oCAAA,yBAA4B;gBAE5B;YADb,IAAI,cAAc,QAClB,aAAa,CAAA,yBAAA,uBAAA,SAAS,UAAU,cAAnB,2CAAA,0BAAA,uBAAA,mCAAA,wBAA2B;YAG1C,0EAA0E;YAC1E,IAAI,eAAe,QAAQ,YAAY;YACvC,IAAI,aAAa,IAAI,EAAE;gBACrB,KAAK,IAAI,OAAO,aACd,IAAI,QAAQ,aAAa,CAAC,MAAM;oBAC9B,aAAa;oBACb;gBACF;YAEJ;YAEA,QAAQ,UAAU,CAAC;YACnB,QAAQ,aAAa,CAAC;YAEtB,oEAAoE;YACpE,IAAI,cAAc,QAAQ,CAAC,yBAAyB,IAAI,OAAO,EAC7D,CAAA,GAAA,kBAAU,EAAE,IAAI,OAAO;YAGzB,oDAAoD;YACpD,IAAI,QAAQ,UAAU,CAAC,IAAI,GAAG,GAAG;gBAC/B,aAAa,OAAO,GAAG;gBACvB,gBAAgB,OAAO,GAAG;YAC5B;QACF;IACF;IAEA,oEAAoE;IACpE,IAAI,iBAAiB,CAAA,GAAA,aAAK,EAAE,QAAQ,UAAU;IAC9C,IAAI,MAAM,CAAA,GAAA,aAAK,EAAiB;IAChC,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAS,CAAA,QAAQ,UAAU,KAAK,eAAe,OAAO,IAAI,gBAAgB,OAAO,AAAD,KAAM,UAAU,OAAO,IAAI,IAAI,OAAO,EAAE;YACrK,IAAI,WAAW,CAAA,GAAA,6BAAqB;YACpC,IAAI,UAAU,CAAA,GAAA,yCAAa,EAAE,KAAK,QAAQ,UAAU;YACpD,IAAI,CAAE,CAAA,mBAAmB,WAAU,GACjC,6FAA6F;YAC7F,8FAA8F;YAC9F;YAGF,IAAI,aAAa,cAAc,gBAAgB,OAAO,EAAE;gBAEtD,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;gBAGlC,IAAI,OAAO,GAAG,sBAAsB;oBAClC,IAAI,UAAU,OAAO,EAAE;wBACrB,CAAA,GAAA,qBAAa,EAAE,UAAU,OAAO,EAAE;wBAClC,iFAAiF;wBACjF,IAAI,aAAa,WACf,CAAA,GAAA,yBAAiB,EAAE,SAAS;4BAAC,mBAAmB,IAAI,OAAO;wBAAA;oBAE/D;gBACF;YACF;QACF;QAEA,+FAA+F;QAC/F,IAAI,CAAC,yBAAyB,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAQ,eAAe,OAAO,IAAI,QAAQ,IAAI,OAAO,EAC5H,CAAA,GAAA,kBAAU,EAAE,IAAI,OAAO;QAGzB,eAAe,OAAO,GAAG,QAAQ,UAAU;QAC3C,gBAAgB,OAAO,GAAG;IAC5B;IAEA,CAAA,GAAA,gBAAQ,EAAE;QACR,OAAO;YACL,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;QAEpC;IACF,GAAG,EAAE;IAEL,sFAAsF;IACtF,CAAA,GAAA,eAAO,EAAE,KAAK,kCAAkC,CAAA;QAC9C,EAAE,cAAc;QAChB,QAAQ,UAAU,CAAC;IACrB;IAEA,IAAI,WAAW;mBACb;iBACA;gBACA;QACA,aAAY,CAAC;YACX,8CAA8C;YAC9C,IAAI,UAAU,OAAO,KAAK,EAAE,MAAM,EAChC,wEAAwE;YACxE,EAAE,cAAc;QAEpB;IACF;IAEA,IAAI,mBAAC,eAAe,EAAC,GAAG,CAAA,GAAA,yCAAY,EAAE;QACpC,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,IAAI,CAAC,mBACH,WAAW,CAAA,GAAA,iBAAS,EAAE,iBAAiB;IAGzC,oFAAoF;IACpF,+FAA+F;IAC/F,IAAI,WAA+B;IACnC,IAAI,CAAC,uBACH,WAAW,QAAQ,UAAU,IAAI,OAAO,IAAI;IAG9C,IAAI,eAAe,CAAA,GAAA,yCAAc,EAAE,QAAQ,UAAU;IACrD,OAAO;QACL,iBAAiB,CAAA,GAAA,iBAAS,EAAE,UAAU;sBACpC;YACA,mBAAmB;QACrB;IACF;AACF", "sources": ["packages/@react-aria/selection/src/useSelectableCollection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CLEAR_FOCUS_EVENT, FOCUS_EVENT, focusWithoutScrolling, getActiveElement, isCtrlKeyPressed, mergeProps, scrollIntoView, scrollIntoViewport, useEffectEvent, useEvent, useRouter, useUpdateLayoutEffect} from '@react-aria/utils';\nimport {dispatchVirtualFocus, getFocusableTreeWalker, moveVirtualFocus} from '@react-aria/focus';\nimport {DOMAttributes, FocusableElement, FocusStrategy, Key, KeyboardDelegate, RefObject} from '@react-types/shared';\nimport {flushSync} from 'react-dom';\nimport {FocusEvent, KeyboardEvent, useEffect, useRef} from 'react';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport {getItemElement, isNonContiguousSelectionModifier, useCollectionId} from './utils';\nimport {MultipleSelectionManager} from '@react-stately/selection';\nimport {useLocale} from '@react-aria/i18n';\nimport {useTypeSelect} from './useTypeSelect';\n\nexport interface AriaSelectableCollectionOptions {\n  /**\n   * An interface for reading and updating multiple selection state.\n   */\n  selectionManager: MultipleSelectionManager,\n  /**\n   * A delegate object that implements behavior for keyboard focus movement.\n   */\n  keyboardDelegate: KeyboardDelegate,\n  /**\n   * The ref attached to the element representing the collection.\n   */\n  ref: RefObject<HTMLElement | null>,\n  /**\n   * Whether the collection or one of its items should be automatically focused upon render.\n   * @default false\n   */\n  autoFocus?: boolean | FocusStrategy,\n  /**\n   * Whether focus should wrap around when the end/start is reached.\n   * @default false\n   */\n  shouldFocusWrap?: boolean,\n  /**\n   * Whether the collection allows empty selection.\n   * @default false\n   */\n  disallowEmptySelection?: boolean,\n  /**\n   * Whether the collection allows the user to select all items via keyboard shortcut.\n   * @default false\n   */\n  disallowSelectAll?: boolean,\n  /**\n   * Whether pressing the Escape should clear selection in the collection or not.\n   * @default 'clearSelection'\n   */\n  escapeKeyBehavior?: 'clearSelection' | 'none',\n  /**\n   * Whether selection should occur automatically on focus.\n   * @default false\n   */\n  selectOnFocus?: boolean,\n  /**\n   * Whether typeahead is disabled.\n   * @default false\n   */\n  disallowTypeAhead?: boolean,\n  /**\n   * Whether the collection items should use virtual focus instead of being focused directly.\n   */\n  shouldUseVirtualFocus?: boolean,\n  /**\n   * Whether navigation through tab key is enabled.\n   */\n  allowsTabNavigation?: boolean,\n  /**\n   * Whether the collection items are contained in a virtual scroller.\n   */\n  isVirtualized?: boolean,\n  /**\n   * The ref attached to the scrollable body. Used to provide automatic scrolling on item focus for non-virtualized collections.\n   * If not provided, defaults to the collection ref.\n   */\n  scrollRef?: RefObject<HTMLElement | null>,\n  /**\n   * The behavior of links in the collection.\n   * - 'action': link behaves like onAction.\n   * - 'selection': link follows selection interactions (e.g. if URL drives selection).\n   * - 'override': links override all other interactions (link items are not selectable).\n   * @default 'action'\n   */\n  linkBehavior?: 'action' | 'selection' | 'override'\n}\n\nexport interface SelectableCollectionAria {\n  /** Props for the collection element. */\n  collectionProps: DOMAttributes\n}\n\n/**\n * Handles interactions with selectable collections.\n */\nexport function useSelectableCollection(options: AriaSelectableCollectionOptions): SelectableCollectionAria {\n  let {\n    selectionManager: manager,\n    keyboardDelegate: delegate,\n    ref,\n    autoFocus = false,\n    shouldFocusWrap = false,\n    disallowEmptySelection = false,\n    disallowSelectAll = false,\n    escapeKeyBehavior = 'clearSelection',\n    selectOnFocus = manager.selectionBehavior === 'replace',\n    disallowTypeAhead = false,\n    shouldUseVirtualFocus,\n    allowsTabNavigation = false,\n    isVirtualized,\n    // If no scrollRef is provided, assume the collection ref is the scrollable region\n    scrollRef = ref,\n    linkBehavior = 'action'\n  } = options;\n  let {direction} = useLocale();\n  let router = useRouter();\n\n  let onKeyDown = (e: KeyboardEvent) => {\n    // Prevent option + tab from doing anything since it doesn't move focus to the cells, only buttons/checkboxes\n    if (e.altKey && e.key === 'Tab') {\n      e.preventDefault();\n    }\n\n    // Keyboard events bubble through portals. Don't handle keyboard events\n    // for elements outside the collection (e.g. menus).\n    if (!ref.current?.contains(e.target as Element)) {\n      return;\n    }\n\n    const navigateToKey = (key: Key | undefined, childFocus?: FocusStrategy) => {\n      if (key != null) {\n        if (manager.isLink(key) && linkBehavior === 'selection' && selectOnFocus && !isNonContiguousSelectionModifier(e)) {\n          // Set focused key and re-render synchronously to bring item into view if needed.\n          flushSync(() => {\n            manager.setFocusedKey(key, childFocus);\n          });\n\n          let item = getItemElement(ref, key);\n          let itemProps = manager.getItemProps(key);\n          if (item) {\n            router.open(item, e, itemProps.href, itemProps.routerOptions);\n          }\n\n          return;\n        }\n\n        manager.setFocusedKey(key, childFocus);\n\n        if (manager.isLink(key) && linkBehavior === 'override') {\n          return;\n        }\n\n        if (e.shiftKey && manager.selectionMode === 'multiple') {\n          manager.extendSelection(key);\n        } else if (selectOnFocus && !isNonContiguousSelectionModifier(e)) {\n          manager.replaceSelection(key);\n        }\n      }\n    };\n\n    switch (e.key) {\n      case 'ArrowDown': {\n        if (delegate.getKeyBelow) {\n          let nextKey = manager.focusedKey != null\n              ? delegate.getKeyBelow?.(manager.focusedKey)\n              : delegate.getFirstKey?.();\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = delegate.getFirstKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      }\n      case 'ArrowUp': {\n        if (delegate.getKeyAbove) {\n          let nextKey = manager.focusedKey != null\n              ? delegate.getKeyAbove?.(manager.focusedKey)\n              : delegate.getLastKey?.();\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = delegate.getLastKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      }\n      case 'ArrowLeft': {\n        if (delegate.getKeyLeftOf) {\n          let nextKey: Key | undefined | null = manager.focusedKey != null ? delegate.getKeyLeftOf?.(manager.focusedKey) : null;\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = direction === 'rtl' ? delegate.getFirstKey?.(manager.focusedKey) : delegate.getLastKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey, direction === 'rtl' ? 'first' : 'last');\n          }\n        }\n        break;\n      }\n      case 'ArrowRight': {\n        if (delegate.getKeyRightOf) {\n          let nextKey: Key | undefined | null = manager.focusedKey != null ? delegate.getKeyRightOf?.(manager.focusedKey) : null;\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = direction === 'rtl' ? delegate.getLastKey?.(manager.focusedKey) : delegate.getFirstKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey, direction === 'rtl' ? 'last' : 'first');\n          }\n        }\n        break;\n      }\n      case 'Home':\n        if (delegate.getFirstKey) {\n          if (manager.focusedKey === null && e.shiftKey) {\n            return;\n          }\n          e.preventDefault();\n          let firstKey: Key | null = delegate.getFirstKey(manager.focusedKey, isCtrlKeyPressed(e));\n          manager.setFocusedKey(firstKey);\n          if (firstKey != null) {\n            if (isCtrlKeyPressed(e) && e.shiftKey && manager.selectionMode === 'multiple') {\n              manager.extendSelection(firstKey);\n            } else if (selectOnFocus) {\n              manager.replaceSelection(firstKey);\n            }\n          }\n        }\n        break;\n      case 'End':\n        if (delegate.getLastKey) {\n          if (manager.focusedKey === null && e.shiftKey) {\n            return;\n          }\n          e.preventDefault();\n          let lastKey = delegate.getLastKey(manager.focusedKey, isCtrlKeyPressed(e));\n          manager.setFocusedKey(lastKey);\n          if (lastKey != null) {\n            if (isCtrlKeyPressed(e) && e.shiftKey && manager.selectionMode === 'multiple') {\n              manager.extendSelection(lastKey);\n            } else if (selectOnFocus) {\n              manager.replaceSelection(lastKey);\n            }\n          }\n        }\n        break;\n      case 'PageDown':\n        if (delegate.getKeyPageBelow && manager.focusedKey != null) {\n          let nextKey = delegate.getKeyPageBelow(manager.focusedKey);\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      case 'PageUp':\n        if (delegate.getKeyPageAbove && manager.focusedKey != null) {\n          let nextKey = delegate.getKeyPageAbove(manager.focusedKey);\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      case 'a':\n        if (isCtrlKeyPressed(e) && manager.selectionMode === 'multiple' && disallowSelectAll !== true) {\n          e.preventDefault();\n          manager.selectAll();\n        }\n        break;\n      case 'Escape':\n        if (escapeKeyBehavior === 'clearSelection' && !disallowEmptySelection && manager.selectedKeys.size !== 0) {\n          e.stopPropagation();\n          e.preventDefault();\n          manager.clearSelection();\n        }\n        break;\n      case 'Tab': {\n        if (!allowsTabNavigation) {\n          // There may be elements that are \"tabbable\" inside a collection (e.g. in a grid cell).\n          // However, collections should be treated as a single tab stop, with arrow key navigation internally.\n          // We don't control the rendering of these, so we can't override the tabIndex to prevent tabbing.\n          // Instead, we handle the Tab key, and move focus manually to the first/last tabbable element\n          // in the collection, so that the browser default behavior will apply starting from that element\n          // rather than the currently focused one.\n          if (e.shiftKey) {\n            ref.current.focus();\n          } else {\n            let walker = getFocusableTreeWalker(ref.current, {tabbable: true});\n            let next: FocusableElement | undefined = undefined;\n            let last: FocusableElement;\n            do {\n              last = walker.lastChild() as FocusableElement;\n              if (last) {\n                next = last;\n              }\n            } while (last);\n\n            if (next && !next.contains(document.activeElement)) {\n              focusWithoutScrolling(next);\n            }\n          }\n          break;\n        }\n      }\n    }\n  };\n\n  // Store the scroll position so we can restore it later.\n  /// TODO: should this happen all the time??\n  let scrollPos = useRef({top: 0, left: 0});\n  useEvent(scrollRef, 'scroll', isVirtualized ? undefined : () => {\n    scrollPos.current = {\n      top: scrollRef.current?.scrollTop ?? 0,\n      left: scrollRef.current?.scrollLeft ?? 0\n    };\n  });\n\n  let onFocus = (e: FocusEvent) => {\n    if (manager.isFocused) {\n      // If a focus event bubbled through a portal, reset focus state.\n      if (!e.currentTarget.contains(e.target)) {\n        manager.setFocused(false);\n      }\n\n      return;\n    }\n\n    // Focus events can bubble through portals. Ignore these events.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    manager.setFocused(true);\n    if (manager.focusedKey == null) {\n      let navigateToKey = (key: Key | undefined | null) => {\n        if (key != null) {\n          manager.setFocusedKey(key);\n          if (selectOnFocus && !manager.isSelected(key)) {\n            manager.replaceSelection(key);\n          }\n        }\n      };\n      // If the user hasn't yet interacted with the collection, there will be no focusedKey set.\n      // Attempt to detect whether the user is tabbing forward or backward into the collection\n      // and either focus the first or last item accordingly.\n      let relatedTarget = e.relatedTarget as Element;\n      if (relatedTarget && (e.currentTarget.compareDocumentPosition(relatedTarget) & Node.DOCUMENT_POSITION_FOLLOWING)) {\n        navigateToKey(manager.lastSelectedKey ?? delegate.getLastKey?.());\n      } else {\n        navigateToKey(manager.firstSelectedKey ?? delegate.getFirstKey?.());\n      }\n    } else if (!isVirtualized && scrollRef.current) {\n      // Restore the scroll position to what it was before.\n      scrollRef.current.scrollTop = scrollPos.current.top;\n      scrollRef.current.scrollLeft = scrollPos.current.left;\n    }\n\n    if (manager.focusedKey != null && scrollRef.current) {\n      // Refocus and scroll the focused item into view if it exists within the scrollable region.\n      let element = getItemElement(ref, manager.focusedKey);\n      if (element instanceof HTMLElement) {\n        // This prevents a flash of focus on the first/last element in the collection, or the collection itself.\n        if (!element.contains(document.activeElement) && !shouldUseVirtualFocus) {\n          focusWithoutScrolling(element);\n        }\n\n        let modality = getInteractionModality();\n        if (modality === 'keyboard') {\n          scrollIntoViewport(element, {containingElement: ref.current});\n        }\n      }\n    }\n  };\n\n  let onBlur = (e) => {\n    // Don't set blurred and then focused again if moving focus within the collection.\n    if (!e.currentTarget.contains(e.relatedTarget as HTMLElement)) {\n      manager.setFocused(false);\n    }\n  };\n\n  // Ref to track whether the first item in the collection should be automatically focused. Specifically used for autocomplete when user types\n  // to focus the first key AFTER the collection updates.\n  // TODO: potentially expand the usage of this\n  let shouldVirtualFocusFirst = useRef(false);\n  // Add event listeners for custom virtual events. These handle updating the focused key in response to various keyboard events\n  // at the autocomplete level\n  // TODO: fix type later\n  useEvent(ref, FOCUS_EVENT, !shouldUseVirtualFocus ? undefined : (e: any) => {\n    let {detail} = e;\n    e.stopPropagation();\n    manager.setFocused(true);\n    // If the user is typing forwards, autofocus the first option in the list.\n    if (detail?.focusStrategy === 'first') {\n      shouldVirtualFocusFirst.current = true;\n    }\n  });\n\n  let updateActiveDescendant = useEffectEvent(() => {\n    let keyToFocus = delegate.getFirstKey?.() ?? null;\n\n    // If no focusable items exist in the list, make sure to clear any activedescendant that may still exist and move focus back to\n    // the original active element (e.g. the autocomplete input)\n    if (keyToFocus == null) {\n      let previousActiveElement = getActiveElement();\n      moveVirtualFocus(ref.current);\n      dispatchVirtualFocus(previousActiveElement!, null);\n\n      // If there wasn't a focusable key but the collection had items, then that means we aren't in an intermediate load state and all keys are disabled.\n      // Reset shouldVirtualFocusFirst so that we don't erronously autofocus an item when the collection is filtered again.\n      if (manager.collection.size > 0) {\n        shouldVirtualFocusFirst.current = false;\n      }\n    } else {\n      manager.setFocusedKey(keyToFocus);\n      // Only set shouldVirtualFocusFirst to false if we've successfully set the first key as the focused key\n      // If there wasn't a key to focus, we might be in a temporary loading state so we'll want to still focus the first key\n      // after the collection updates after load\n      shouldVirtualFocusFirst.current = false;\n    }\n  });\n\n  useUpdateLayoutEffect(() => {\n    if (shouldVirtualFocusFirst.current) {\n      updateActiveDescendant();\n    }\n\n  }, [manager.collection, updateActiveDescendant]);\n\n  let resetFocusFirstFlag = useEffectEvent(() => {\n    // If user causes the focused key to change in any other way, clear shouldVirtualFocusFirst so we don't\n    // accidentally move focus from under them. Skip this if the collection was empty because we might be in a load\n    // state and will still want to focus the first item after load\n    if (manager.collection.size > 0) {\n      shouldVirtualFocusFirst.current = false;\n    }\n  });\n\n  useUpdateLayoutEffect(() => {\n    resetFocusFirstFlag();\n  }, [manager.focusedKey, resetFocusFirstFlag]);\n\n  useEvent(ref, CLEAR_FOCUS_EVENT, !shouldUseVirtualFocus ? undefined : (e: any) => {\n    e.stopPropagation();\n    manager.setFocused(false);\n    if (e.detail?.clearFocusKey) {\n      manager.setFocusedKey(null);\n    }\n  });\n\n  const autoFocusRef = useRef(autoFocus);\n  const didAutoFocusRef = useRef(false);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      let focusedKey: Key | null = null;\n\n      // Check focus strategy to determine which item to focus\n      if (autoFocus === 'first') {\n        focusedKey = delegate.getFirstKey?.() ?? null;\n      } if (autoFocus === 'last') {\n        focusedKey = delegate.getLastKey?.() ?? null;\n      }\n\n      // If there are any selected keys, make the first one the new focus target\n      let selectedKeys = manager.selectedKeys;\n      if (selectedKeys.size) {\n        for (let key of selectedKeys) {\n          if (manager.canSelectItem(key)) {\n            focusedKey = key;\n            break;\n          }\n        }\n      }\n\n      manager.setFocused(true);\n      manager.setFocusedKey(focusedKey);\n\n      // If no default focus key is selected, focus the collection itself.\n      if (focusedKey == null && !shouldUseVirtualFocus && ref.current) {\n        focusSafely(ref.current);\n      }\n\n      // Wait until the collection has items to autofocus.\n      if (manager.collection.size > 0) {\n        autoFocusRef.current = false;\n        didAutoFocusRef.current = true;\n      }\n    }\n  });\n\n  // Scroll the focused element into view when the focusedKey changes.\n  let lastFocusedKey = useRef(manager.focusedKey);\n  let raf = useRef<number | null>(null);\n  useEffect(() => {\n    if (manager.isFocused && manager.focusedKey != null && (manager.focusedKey !== lastFocusedKey.current || didAutoFocusRef.current) && scrollRef.current && ref.current) {\n      let modality = getInteractionModality();\n      let element = getItemElement(ref, manager.focusedKey);\n      if (!(element instanceof HTMLElement)) {\n        // If item element wasn't found, return early (don't update autoFocusRef and lastFocusedKey).\n        // The collection may initially be empty (e.g. virtualizer), so wait until the element exists.\n        return;\n      }\n\n      if (modality === 'keyboard' || didAutoFocusRef.current) {\n\n        if (raf.current) {\n          cancelAnimationFrame(raf.current);\n        }\n\n        raf.current = requestAnimationFrame(() => {\n          if (scrollRef.current) {\n            scrollIntoView(scrollRef.current, element);\n            // Avoid scroll in iOS VO, since it may cause overlay to close (i.e. RAC submenu)\n            if (modality !== 'virtual') {\n              scrollIntoViewport(element, {containingElement: ref.current});\n            }\n          }\n        });\n      }\n    }\n\n    // If the focused key becomes null (e.g. the last item is deleted), focus the whole collection.\n    if (!shouldUseVirtualFocus && manager.isFocused && manager.focusedKey == null && lastFocusedKey.current != null && ref.current) {\n      focusSafely(ref.current);\n    }\n\n    lastFocusedKey.current = manager.focusedKey;\n    didAutoFocusRef.current = false;\n  });\n\n  useEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, []);\n\n  // Intercept FocusScope restoration since virtualized collections can reuse DOM nodes.\n  useEvent(ref, 'react-aria-focus-scope-restore', e => {\n    e.preventDefault();\n    manager.setFocused(true);\n  });\n\n  let handlers = {\n    onKeyDown,\n    onFocus,\n    onBlur,\n    onMouseDown(e) {\n      // Ignore events that bubbled through portals.\n      if (scrollRef.current === e.target) {\n        // Prevent focus going to the collection when clicking on the scrollbar.\n        e.preventDefault();\n      }\n    }\n  };\n\n  let {typeSelectProps} = useTypeSelect({\n    keyboardDelegate: delegate,\n    selectionManager: manager\n  });\n\n  if (!disallowTypeAhead) {\n    handlers = mergeProps(typeSelectProps, handlers);\n  }\n\n  // If nothing is focused within the collection, make the collection itself tabbable.\n  // This will be marshalled to either the first or last item depending on where focus came from.\n  let tabIndex: number | undefined = undefined;\n  if (!shouldUseVirtualFocus) {\n    tabIndex = manager.focusedKey == null ? 0 : -1;\n  }\n\n  let collectionId = useCollectionId(manager.collection);\n  return {\n    collectionProps: mergeProps(handlers, {\n      tabIndex,\n      'data-collection': collectionId\n    })\n  };\n}\n"], "names": [], "version": 3, "file": "useSelectableCollection.module.js.map"}