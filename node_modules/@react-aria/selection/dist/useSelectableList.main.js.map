{"mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AAuCM,SAAS,0CAAkB,KAAgC;IAChE,IAAI,oBACF,gBAAgB,cAChB,UAAU,gBACV,YAAY,OACZ,GAAG,oBACH,gBAAgB,kBAChB,cAAc,EACf,GAAG;IAEJ,0HAA0H;IAC1H,qFAAqF;IACrF,IAAI,WAAW,CAAA,GAAA,gCAAU,EAAE;QAAC,OAAO;QAAU,aAAa;IAAM;IAChE,IAAI,mBAAmB,iBAAiB,gBAAgB;IACxD,IAAI,WAAW,CAAA,GAAA,oBAAM,EAAE,IACrB,oBAAoB,IAAI,CAAA,GAAA,8CAAmB,EAAE;wBAC3C;0BACA;8BACA;iBACA;sBACA;4BACA;QACF,IACC;QAAC;QAAkB;QAAgB;QAAY;QAAc;QAAK;QAAU;KAAiB;IAEhG,IAAI,mBAAC,eAAe,EAAC,GAAG,CAAA,GAAA,iDAAsB,EAAE;QAC9C,GAAG,KAAK;aACR;0BACA;QACA,kBAAkB;IACpB;IAEA,OAAO;QACL,WAAW;IACb;AACF", "sources": ["packages/@react-aria/selection/src/useSelectableList.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaSelectableCollectionOptions, useSelectableCollection} from './useSelectableCollection';\nimport {Collection, DOMAttributes, Key, KeyboardDelegate, LayoutDelegate, Node} from '@react-types/shared';\nimport {ListKeyboardDelegate} from './ListKeyboardDelegate';\nimport {useCollator} from '@react-aria/i18n';\nimport {useMemo} from 'react';\n\nexport interface AriaSelectableListOptions extends Omit<AriaSelectableCollectionOptions, 'keyboardDelegate'> {\n  /**\n   * State of the collection.\n   */\n  collection: Collection<Node<unknown>>,\n  /**\n   * A delegate object that implements behavior for keyboard focus movement.\n   */\n  keyboardDelegate?: KeyboardDelegate,\n  /**\n   * A delegate object that provides layout information for items in the collection.\n   * By default this uses the DOM, but this can be overridden to implement things like\n   * virtualized scrolling.\n   */\n  layoutDelegate?: LayoutDelegate,\n  /**\n   * The item keys that are disabled. These items cannot be selected, focused, or otherwise interacted with.\n   */\n  disabledKeys: Set<Key>\n}\n\nexport interface SelectableListAria {\n  /**\n   * Props for the option element.\n   */\n  listProps: DOMAttributes\n}\n\n/**\n * Handles interactions with a selectable list.\n */\nexport function useSelectableList(props: AriaSelectableListOptions): SelectableListAria {\n  let {\n    selectionManager,\n    collection,\n    disabledKeys,\n    ref,\n    keyboardDelegate,\n    layoutDelegate\n  } = props;\n\n  // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n  // When virtualized, the layout object will be passed in as a prop and override this.\n  let collator = useCollator({usage: 'search', sensitivity: 'base'});\n  let disabledBehavior = selectionManager.disabledBehavior;\n  let delegate = useMemo(() => (\n    keyboardDelegate || new ListKeyboardDelegate({\n      collection,\n      disabledKeys,\n      disabledBehavior,\n      ref,\n      collator,\n      layoutDelegate\n    })\n  ), [keyboardDelegate, layoutDelegate, collection, disabledKeys, ref, collator, disabledBehavior]);\n\n  let {collectionProps} = useSelectableCollection({\n    ...props,\n    ref,\n    selectionManager,\n    keyboardDelegate: delegate\n  });\n\n  return {\n    listProps: collectionProps\n  };\n}\n"], "names": [], "version": 3, "file": "useSelectableList.main.js.map"}