import { DOMAttributes, Key, KeyboardDelegate, FocusStrategy, RefObject, DOMProps, FocusableElement, LayoutDelegate, Rect, Size, Collection, Direction, DisabledBehavior, Node, Orientation } from "@react-types/shared";
import { MultipleSelectionManager } from "@react-stately/selection";
export interface AriaTypeSelectOptions {
    /**
     * A delegate that returns collection item keys with respect to visual layout.
     */
    keyboardDelegate: KeyboardDelegate;
    /**
     * An interface for reading and updating multiple selection state.
     */
    selectionManager: MultipleSelectionManager;
    /**
     * Called when an item is focused by typing.
     */
    onTypeSelect?: (key: Key) => void;
}
export interface TypeSelectAria {
    /**
     * Props to be spread on the owner of the options.
     */
    typeSelectProps: DOMAttributes;
}
/**
 * Handles typeahead interactions with collections.
 */
export function useTypeSelect(options: AriaTypeSelectOptions): TypeSelectAria;
export interface AriaSelectableCollectionOptions {
    /**
     * An interface for reading and updating multiple selection state.
     */
    selectionManager: MultipleSelectionManager;
    /**
     * A delegate object that implements behavior for keyboard focus movement.
     */
    keyboardDelegate: KeyboardDelegate;
    /**
     * The ref attached to the element representing the collection.
     */
    ref: RefObject<HTMLElement | null>;
    /**
     * Whether the collection or one of its items should be automatically focused upon render.
     * @default false
     */
    autoFocus?: boolean | FocusStrategy;
    /**
     * Whether focus should wrap around when the end/start is reached.
     * @default false
     */
    shouldFocusWrap?: boolean;
    /**
     * Whether the collection allows empty selection.
     * @default false
     */
    disallowEmptySelection?: boolean;
    /**
     * Whether the collection allows the user to select all items via keyboard shortcut.
     * @default false
     */
    disallowSelectAll?: boolean;
    /**
     * Whether pressing the Escape should clear selection in the collection or not.
     * @default 'clearSelection'
     */
    escapeKeyBehavior?: 'clearSelection' | 'none';
    /**
     * Whether selection should occur automatically on focus.
     * @default false
     */
    selectOnFocus?: boolean;
    /**
     * Whether typeahead is disabled.
     * @default false
     */
    disallowTypeAhead?: boolean;
    /**
     * Whether the collection items should use virtual focus instead of being focused directly.
     */
    shouldUseVirtualFocus?: boolean;
    /**
     * Whether navigation through tab key is enabled.
     */
    allowsTabNavigation?: boolean;
    /**
     * Whether the collection items are contained in a virtual scroller.
     */
    isVirtualized?: boolean;
    /**
     * The ref attached to the scrollable body. Used to provide automatic scrolling on item focus for non-virtualized collections.
     * If not provided, defaults to the collection ref.
     */
    scrollRef?: RefObject<HTMLElement | null>;
    /**
     * The behavior of links in the collection.
     * - 'action': link behaves like onAction.
     * - 'selection': link follows selection interactions (e.g. if URL drives selection).
     * - 'override': links override all other interactions (link items are not selectable).
     * @default 'action'
     */
    linkBehavior?: 'action' | 'selection' | 'override';
}
export interface SelectableCollectionAria {
    /** Props for the collection element. */
    collectionProps: DOMAttributes;
}
/**
 * Handles interactions with selectable collections.
 */
export function useSelectableCollection(options: AriaSelectableCollectionOptions): SelectableCollectionAria;
export interface SelectableItemOptions extends DOMProps {
    /**
     * An interface for reading and updating multiple selection state.
     */
    selectionManager: MultipleSelectionManager;
    /**
     * A unique key for the item.
     */
    key: Key;
    /**
     * Ref to the item.
     */
    ref: RefObject<FocusableElement | null>;
    /**
     * By default, selection occurs on pointer down. This can be strange if selecting an
     * item causes the UI to disappear immediately (e.g. menus).
     */
    shouldSelectOnPressUp?: boolean;
    /**
     * Whether selection requires the pointer/mouse down and up events to occur on the same target or triggers selection on
     * the target of the pointer/mouse up event.
     */
    allowsDifferentPressOrigin?: boolean;
    /**
     * Whether the option is contained in a virtual scroller.
     */
    isVirtualized?: boolean;
    /**
     * Function to focus the item.
     */
    focus?: () => void;
    /**
     * Whether the option should use virtual focus instead of being focused directly.
     */
    shouldUseVirtualFocus?: boolean;
    /** Whether the item is disabled. */
    isDisabled?: boolean;
    /**
     * Handler that is called when a user performs an action on the item. The exact user event depends on
     * the collection's `selectionBehavior` prop and the interaction modality.
     */
    onAction?: () => void;
    /**
     * The behavior of links in the collection.
     * - 'action': link behaves like onAction.
     * - 'selection': link follows selection interactions (e.g. if URL drives selection).
     * - 'override': links override all other interactions (link items are not selectable).
     * - 'none': links are disabled for both selection and actions (e.g. handled elsewhere).
     * @default 'action'
     */
    linkBehavior?: 'action' | 'selection' | 'override' | 'none';
}
export interface SelectableItemStates {
    /** Whether the item is currently in a pressed state. */
    isPressed: boolean;
    /** Whether the item is currently selected. */
    isSelected: boolean;
    /** Whether the item is currently focused. */
    isFocused: boolean;
    /**
     * Whether the item is non-interactive, i.e. both selection and actions are disabled and the item may
     * not be focused. Dependent on `disabledKeys` and `disabledBehavior`.
     */
    isDisabled: boolean;
    /**
     * Whether the item may be selected, dependent on `selectionMode`, `disabledKeys`, and `disabledBehavior`.
     */
    allowsSelection: boolean;
    /**
     * Whether the item has an action, dependent on `onAction`, `disabledKeys`,
     * and `disabledBehavior`. It may also change depending on the current selection state
     * of the list (e.g. when selection is primary). This can be used to enable or disable hover
     * styles or other visual indications of interactivity.
     */
    hasAction: boolean;
}
export interface SelectableItemAria extends SelectableItemStates {
    /**
     * Props to be spread on the item root node.
     */
    itemProps: DOMAttributes;
}
/**
 * Handles interactions with an item in a selectable collection.
 */
export function useSelectableItem(options: SelectableItemOptions): SelectableItemAria;
export class DOMLayoutDelegate implements LayoutDelegate {
    constructor(ref: RefObject<HTMLElement | null>);
    getItemRect(key: Key): Rect | null;
    getContentSize(): Size;
    getVisibleRect(): Rect;
}
interface ListKeyboardDelegateOptions<T> {
    collection: Collection<Node<T>>;
    ref: RefObject<HTMLElement | null>;
    collator?: Intl.Collator;
    layout?: 'stack' | 'grid';
    orientation?: Orientation;
    direction?: Direction;
    disabledKeys?: Set<Key>;
    disabledBehavior?: DisabledBehavior;
    layoutDelegate?: LayoutDelegate;
}
export class ListKeyboardDelegate<T> implements KeyboardDelegate {
    constructor(collection: Collection<Node<T>>, disabledKeys: Set<Key>, ref: RefObject<HTMLElement | null>, collator?: Intl.Collator);
    constructor(options: ListKeyboardDelegateOptions<T>);
    getNextKey(key: Key): Key | null;
    getPreviousKey(key: Key): Key | null;
    getKeyBelow(key: Key): Key | null;
    getKeyAbove(key: Key): Key | null;
    getKeyRightOf?(key: Key): Key | null;
    getKeyLeftOf?(key: Key): Key | null;
    getFirstKey(): Key | null;
    getLastKey(): Key | null;
    getKeyPageAbove(key: Key): Key | null;
    getKeyPageBelow(key: Key): Key | null;
    getKeyForSearch(search: string, fromKey?: Key): Key | null;
}
export interface AriaSelectableListOptions extends Omit<AriaSelectableCollectionOptions, 'keyboardDelegate'> {
    /**
     * State of the collection.
     */
    collection: Collection<Node<unknown>>;
    /**
     * A delegate object that implements behavior for keyboard focus movement.
     */
    keyboardDelegate?: KeyboardDelegate;
    /**
     * A delegate object that provides layout information for items in the collection.
     * By default this uses the DOM, but this can be overridden to implement things like
     * virtualized scrolling.
     */
    layoutDelegate?: LayoutDelegate;
    /**
     * The item keys that are disabled. These items cannot be selected, focused, or otherwise interacted with.
     */
    disabledKeys: Set<Key>;
}
export interface SelectableListAria {
    /**
     * Props for the option element.
     */
    listProps: DOMAttributes;
}
/**
 * Handles interactions with a selectable list.
 */
export function useSelectableList(props: AriaSelectableListOptions): SelectableListAria;

//# sourceMappingURL=types.d.ts.map
