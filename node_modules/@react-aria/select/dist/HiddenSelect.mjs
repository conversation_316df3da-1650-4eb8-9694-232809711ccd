import {selectData as $58aed456727eb0f3$export$703601b7e90536f8} from "./useSelect.mjs";
import $8BE50$react, {useCallback as $8BE50$useCallback, useRef as $8BE50$useRef} from "react";
import {useFormReset as $8BE50$useFormReset} from "@react-aria/utils";
import {useFormValidation as $8BE50$useFormValidation} from "@react-aria/form";
import {useVisuallyHidden as $8BE50$useVisuallyHidden} from "@react-aria/visually-hidden";

/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 




function $bdd25dc72710631f$export$f809e80f58e251d1(props, state, triggerRef) {
    let data = (0, $58aed456727eb0f3$export$703601b7e90536f8).get(state) || {};
    let { autoComplete: autoComplete, name: name = data.name, form: form = data.form, isDisabled: isDisabled = data.isDisabled } = props;
    let { validationBehavior: validationBehavior, isRequired: isRequired } = data;
    let { visuallyHiddenProps: visuallyHiddenProps } = (0, $8BE50$useVisuallyHidden)({
        style: {
            // Prevent page scrolling.
            position: 'fixed',
            top: 0,
            left: 0
        }
    });
    (0, $8BE50$useFormReset)(props.selectRef, state.defaultSelectedKey, state.setSelectedKey);
    (0, $8BE50$useFormValidation)({
        validationBehavior: validationBehavior,
        focus: ()=>{
            var _triggerRef_current;
            return (_triggerRef_current = triggerRef.current) === null || _triggerRef_current === void 0 ? void 0 : _triggerRef_current.focus();
        }
    }, state, props.selectRef);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    let onChange = (0, $8BE50$useCallback)((e)=>state.setSelectedKey(e.currentTarget.value), [
        state.setSelectedKey
    ]);
    var _state_selectedKey;
    // In Safari, the <select> cannot have `display: none` or `hidden` for autofill to work.
    // In Firefox, there must be a <label> to identify the <select> whereas other browsers
    // seem to identify it just by surrounding text.
    // The solution is to use <VisuallyHidden> to hide the elements, which clips the elements to a
    // 1px rectangle. In addition, we hide from screen readers with aria-hidden, and make the <select>
    // non tabbable with tabIndex={-1}.
    return {
        containerProps: {
            ...visuallyHiddenProps,
            'aria-hidden': true,
            // @ts-ignore
            ['data-react-aria-prevent-focus']: true,
            // @ts-ignore
            ['data-a11y-ignore']: 'aria-hidden-focus'
        },
        inputProps: {
            style: {
                display: 'none'
            }
        },
        selectProps: {
            tabIndex: -1,
            autoComplete: autoComplete,
            disabled: isDisabled,
            required: validationBehavior === 'native' && isRequired,
            name: name,
            form: form,
            value: (_state_selectedKey = state.selectedKey) !== null && _state_selectedKey !== void 0 ? _state_selectedKey : '',
            onChange: onChange,
            onInput: onChange
        }
    };
}
function $bdd25dc72710631f$export$cbd84cdb2e668835(props) {
    let { state: state, triggerRef: triggerRef, label: label, name: name, form: form, isDisabled: isDisabled } = props;
    let selectRef = (0, $8BE50$useRef)(null);
    let inputRef = (0, $8BE50$useRef)(null);
    let { containerProps: containerProps, selectProps: selectProps } = $bdd25dc72710631f$export$f809e80f58e251d1({
        ...props,
        selectRef: state.collection.size <= 300 ? selectRef : inputRef
    }, state, triggerRef);
    // If used in a <form>, use a hidden input so the value can be submitted to a server.
    // If the collection isn't too big, use a hidden <select> element for this so that browser
    // autofill will work. Otherwise, use an <input type="hidden">.
    if (state.collection.size <= 300) return /*#__PURE__*/ (0, $8BE50$react).createElement("div", {
        ...containerProps,
        "data-testid": "hidden-select-container"
    }, /*#__PURE__*/ (0, $8BE50$react).createElement("label", null, label, /*#__PURE__*/ (0, $8BE50$react).createElement("select", {
        ...selectProps,
        ref: selectRef
    }, /*#__PURE__*/ (0, $8BE50$react).createElement("option", null), [
        ...state.collection.getKeys()
    ].map((key)=>{
        let item = state.collection.getItem(key);
        if (item && item.type === 'item') return /*#__PURE__*/ (0, $8BE50$react).createElement("option", {
            key: item.key,
            value: item.key
        }, item.textValue);
    }))));
    else if (name) {
        let data = (0, $58aed456727eb0f3$export$703601b7e90536f8).get(state) || {};
        let { validationBehavior: validationBehavior } = data;
        var _state_selectedKey;
        let inputProps = {
            type: 'hidden',
            autoComplete: selectProps.autoComplete,
            name: name,
            form: form,
            disabled: isDisabled,
            value: (_state_selectedKey = state.selectedKey) !== null && _state_selectedKey !== void 0 ? _state_selectedKey : ''
        };
        if (validationBehavior === 'native') // Use a hidden <input type="text"> rather than <input type="hidden">
        // so that an empty value blocks HTML form submission when the field is required.
        return /*#__PURE__*/ (0, $8BE50$react).createElement("input", {
            ...inputProps,
            ref: inputRef,
            style: {
                display: 'none'
            },
            type: "text",
            required: selectProps.required,
            onChange: ()=>{}
        });
        return /*#__PURE__*/ (0, $8BE50$react).createElement("input", {
            ...inputProps,
            ref: inputRef
        });
    }
    return null;
}


export {$bdd25dc72710631f$export$f809e80f58e251d1 as useHiddenSelect, $bdd25dc72710631f$export$cbd84cdb2e668835 as HiddenSelect};
//# sourceMappingURL=HiddenSelect.module.js.map
