var $23a1e93dc7fa8653$exports = require("./useSelect.main.js");
var $ccxQv$react = require("react");
var $ccxQv$reactariautils = require("@react-aria/utils");
var $ccxQv$reactariaform = require("@react-aria/form");
var $ccxQv$reactariavisuallyhidden = require("@react-aria/visually-hidden");


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useHiddenSelect", () => $6eae0e0872a4cdd4$export$f809e80f58e251d1);
$parcel$export(module.exports, "HiddenSelect", () => $6eae0e0872a4cdd4$export$cbd84cdb2e668835);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 




function $6eae0e0872a4cdd4$export$f809e80f58e251d1(props, state, triggerRef) {
    let data = (0, $23a1e93dc7fa8653$exports.selectData).get(state) || {};
    let { autoComplete: autoComplete, name: name = data.name, form: form = data.form, isDisabled: isDisabled = data.isDisabled } = props;
    let { validationBehavior: validationBehavior, isRequired: isRequired } = data;
    let { visuallyHiddenProps: visuallyHiddenProps } = (0, $ccxQv$reactariavisuallyhidden.useVisuallyHidden)({
        style: {
            // Prevent page scrolling.
            position: 'fixed',
            top: 0,
            left: 0
        }
    });
    (0, $ccxQv$reactariautils.useFormReset)(props.selectRef, state.defaultSelectedKey, state.setSelectedKey);
    (0, $ccxQv$reactariaform.useFormValidation)({
        validationBehavior: validationBehavior,
        focus: ()=>{
            var _triggerRef_current;
            return (_triggerRef_current = triggerRef.current) === null || _triggerRef_current === void 0 ? void 0 : _triggerRef_current.focus();
        }
    }, state, props.selectRef);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    let onChange = (0, $ccxQv$react.useCallback)((e)=>state.setSelectedKey(e.currentTarget.value), [
        state.setSelectedKey
    ]);
    var _state_selectedKey;
    // In Safari, the <select> cannot have `display: none` or `hidden` for autofill to work.
    // In Firefox, there must be a <label> to identify the <select> whereas other browsers
    // seem to identify it just by surrounding text.
    // The solution is to use <VisuallyHidden> to hide the elements, which clips the elements to a
    // 1px rectangle. In addition, we hide from screen readers with aria-hidden, and make the <select>
    // non tabbable with tabIndex={-1}.
    return {
        containerProps: {
            ...visuallyHiddenProps,
            'aria-hidden': true,
            // @ts-ignore
            ['data-react-aria-prevent-focus']: true,
            // @ts-ignore
            ['data-a11y-ignore']: 'aria-hidden-focus'
        },
        inputProps: {
            style: {
                display: 'none'
            }
        },
        selectProps: {
            tabIndex: -1,
            autoComplete: autoComplete,
            disabled: isDisabled,
            required: validationBehavior === 'native' && isRequired,
            name: name,
            form: form,
            value: (_state_selectedKey = state.selectedKey) !== null && _state_selectedKey !== void 0 ? _state_selectedKey : '',
            onChange: onChange,
            onInput: onChange
        }
    };
}
function $6eae0e0872a4cdd4$export$cbd84cdb2e668835(props) {
    let { state: state, triggerRef: triggerRef, label: label, name: name, form: form, isDisabled: isDisabled } = props;
    let selectRef = (0, $ccxQv$react.useRef)(null);
    let inputRef = (0, $ccxQv$react.useRef)(null);
    let { containerProps: containerProps, selectProps: selectProps } = $6eae0e0872a4cdd4$export$f809e80f58e251d1({
        ...props,
        selectRef: state.collection.size <= 300 ? selectRef : inputRef
    }, state, triggerRef);
    // If used in a <form>, use a hidden input so the value can be submitted to a server.
    // If the collection isn't too big, use a hidden <select> element for this so that browser
    // autofill will work. Otherwise, use an <input type="hidden">.
    if (state.collection.size <= 300) return /*#__PURE__*/ (0, ($parcel$interopDefault($ccxQv$react))).createElement("div", {
        ...containerProps,
        "data-testid": "hidden-select-container"
    }, /*#__PURE__*/ (0, ($parcel$interopDefault($ccxQv$react))).createElement("label", null, label, /*#__PURE__*/ (0, ($parcel$interopDefault($ccxQv$react))).createElement("select", {
        ...selectProps,
        ref: selectRef
    }, /*#__PURE__*/ (0, ($parcel$interopDefault($ccxQv$react))).createElement("option", null), [
        ...state.collection.getKeys()
    ].map((key)=>{
        let item = state.collection.getItem(key);
        if (item && item.type === 'item') return /*#__PURE__*/ (0, ($parcel$interopDefault($ccxQv$react))).createElement("option", {
            key: item.key,
            value: item.key
        }, item.textValue);
    }))));
    else if (name) {
        let data = (0, $23a1e93dc7fa8653$exports.selectData).get(state) || {};
        let { validationBehavior: validationBehavior } = data;
        var _state_selectedKey;
        let inputProps = {
            type: 'hidden',
            autoComplete: selectProps.autoComplete,
            name: name,
            form: form,
            disabled: isDisabled,
            value: (_state_selectedKey = state.selectedKey) !== null && _state_selectedKey !== void 0 ? _state_selectedKey : ''
        };
        if (validationBehavior === 'native') // Use a hidden <input type="text"> rather than <input type="hidden">
        // so that an empty value blocks HTML form submission when the field is required.
        return /*#__PURE__*/ (0, ($parcel$interopDefault($ccxQv$react))).createElement("input", {
            ...inputProps,
            ref: inputRef,
            style: {
                display: 'none'
            },
            type: "text",
            required: selectProps.required,
            onChange: ()=>{}
        });
        return /*#__PURE__*/ (0, ($parcel$interopDefault($ccxQv$react))).createElement("input", {
            ...inputProps,
            ref: inputRef
        });
    }
    return null;
}


//# sourceMappingURL=HiddenSelect.main.js.map
