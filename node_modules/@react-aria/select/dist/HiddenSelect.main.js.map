{"mappings": ";;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;AA8DM,SAAS,0CAAmB,KAA8B,EAAE,KAAqB,EAAE,UAA8C;IACtI,IAAI,OAAO,CAAA,GAAA,oCAAS,EAAE,GAAG,CAAC,UAAU,CAAC;IACrC,IAAI,gBAAC,YAAY,QAAE,OAAO,KAAK,IAAI,QAAE,OAAO,KAAK,IAAI,cAAE,aAAa,KAAK,UAAU,EAAC,GAAG;IACvF,IAAI,sBAAC,kBAAkB,cAAE,UAAU,EAAC,GAAG;IACvC,IAAI,uBAAC,mBAAmB,EAAC,GAAG,CAAA,GAAA,gDAAgB,EAAE;QAC5C,OAAO;YACL,0BAA0B;YAC1B,UAAU;YACV,KAAK;YACL,MAAM;QACR;IACF;IAEA,CAAA,GAAA,kCAAW,EAAE,MAAM,SAAS,EAAE,MAAM,kBAAkB,EAAE,MAAM,cAAc;IAC5E,CAAA,GAAA,sCAAgB,EAAE;4BAChB;QACA,OAAO;gBAAM;oBAAA,sBAAA,WAAW,OAAO,cAAlB,0CAAA,oBAAoB,KAAK;;IACxC,GAAG,OAAO,MAAM,SAAS;IAEzB,uDAAuD;IACvD,IAAI,WAAW,CAAA,GAAA,wBAAU,EAAE,CAAC,IAAiF,MAAM,cAAc,CAAC,EAAE,aAAa,CAAC,KAAK,GAAG;QAAC,MAAM,cAAc;KAAC;QA2BrK;IAzBX,wFAAwF;IACxF,sFAAsF;IACtF,gDAAgD;IAChD,8FAA8F;IAC9F,kGAAkG;IAClG,mCAAmC;IACnC,OAAO;QACL,gBAAgB;YACd,GAAG,mBAAmB;YACtB,eAAe;YACf,aAAa;YACb,CAAC,gCAAgC,EAAE;YACnC,aAAa;YACb,CAAC,mBAAmB,EAAE;QACxB;QACA,YAAY;YACV,OAAO;gBAAC,SAAS;YAAM;QACzB;QACA,aAAa;YACX,UAAU;0BACV;YACA,UAAU;YACV,UAAU,uBAAuB,YAAY;kBAC7C;kBACA;YACA,OAAO,CAAA,qBAAA,MAAM,WAAW,cAAjB,gCAAA,qBAAqB;sBAC5B;YACA,SAAS;QACX;IACF;AACF;AAMO,SAAS,0CAAgB,KAA2B;IACzD,IAAI,SAAC,KAAK,cAAE,UAAU,SAAE,KAAK,QAAE,IAAI,QAAE,IAAI,cAAE,UAAU,EAAC,GAAG;IACzD,IAAI,YAAY,CAAA,GAAA,mBAAK,EAAE;IACvB,IAAI,WAAW,CAAA,GAAA,mBAAK,EAAE;IACtB,IAAI,kBAAC,cAAc,eAAE,WAAW,EAAC,GAAG,0CAAgB;QAAC,GAAG,KAAK;QAAE,WAAW,MAAM,UAAU,CAAC,IAAI,IAAI,MAAM,YAAY;IAAQ,GAAG,OAAO;IAEvI,qFAAqF;IACrF,0FAA0F;IAC1F,+DAA+D;IAC/D,IAAI,MAAM,UAAU,CAAC,IAAI,IAAI,KAC3B,qBACE,0DAAC;QAAK,GAAG,cAAc;QAAE,eAAY;qBACnC,0DAAC,eACE,qBACD,0DAAC;QAAQ,GAAG,WAAW;QAAE,KAAK;qBAC5B,0DAAC,iBACA;WAAI,MAAM,UAAU,CAAC,OAAO;KAAG,CAAC,GAAG,CAAC,CAAA;QACnC,IAAI,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC;QACpC,IAAI,QAAQ,KAAK,IAAI,KAAK,QACxB,qBACE,0DAAC;YACC,KAAK,KAAK,GAAG;YACb,OAAO,KAAK,GAAG;WACd,KAAK,SAAS;IAIvB;SAKH,IAAI,MAAM;QACf,IAAI,OAAO,CAAA,GAAA,oCAAS,EAAE,GAAG,CAAC,UAAU,CAAC;QACrC,IAAI,sBAAC,kBAAkB,EAAC,GAAG;YAQlB;QANT,IAAI,aAAoD;YACtD,MAAM;YACN,cAAc,YAAY,YAAY;kBACtC;kBACA;YACA,UAAU;YACV,OAAO,CAAA,qBAAA,MAAM,WAAW,cAAjB,gCAAA,qBAAqB;QAC9B;QAEA,IAAI,uBAAuB,UACzB,qEAAqE;QACrE,iFAAiF;QACjF,qBACE,0DAAC;YACE,GAAG,UAAU;YACd,KAAK;YACL,OAAO;gBAAC,SAAS;YAAM;YACvB,MAAK;YACL,UAAU,YAAY,QAAQ;YAC9B,UAAU,KAAmC;;QAInD,qBACE,0DAAC;YAAO,GAAG,UAAU;YAAE,KAAK;;IAEhC;IAEA,OAAO;AACT", "sources": ["packages/@react-aria/select/src/HiddenSelect.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport React, {InputHTMLAttributes, JSX, ReactNode, useCallback, useRef} from 'react';\nimport {selectData} from './useSelect';\nimport {SelectState} from '@react-stately/select';\nimport {useFormReset} from '@react-aria/utils';\nimport {useFormValidation} from '@react-aria/form';\nimport {useVisuallyHidden} from '@react-aria/visually-hidden';\n\nexport interface AriaHiddenSelectProps {\n  /**\n   * Describes the type of autocomplete functionality the input should provide if any. See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#htmlattrdefautocomplete).\n   */\n  autoComplete?: string,\n\n  /** The text label for the select. */\n  label?: ReactNode,\n\n  /** HTML form input name. */\n  name?: string,\n\n  /**\n   * The `<form>` element to associate the input with.\n   * The value of this attribute must be the id of a `<form>` in the same document.\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#form).\n   */\n  form?: string,\n\n  /** Sets the disabled state of the select and input. */\n  isDisabled?: boolean\n}\n\nexport interface HiddenSelectProps<T> extends AriaHiddenSelectProps {\n  /** State for the select. */\n  state: SelectState<T>,\n\n  /** A ref to the trigger element. */\n  triggerRef: RefObject<FocusableElement | null>\n}\n\nexport interface AriaHiddenSelectOptions extends AriaHiddenSelectProps {\n  /** A ref to the hidden `<select>` element. */\n  selectRef?: RefObject<HTMLSelectElement | HTMLInputElement | null>\n}\n\nexport interface HiddenSelectAria {\n  /** Props for the container element. */\n  containerProps: React.HTMLAttributes<FocusableElement>,\n\n  /** Props for the hidden input element. */\n  inputProps: React.InputHTMLAttributes<HTMLInputElement>,\n\n  /** Props for the hidden select element. */\n  selectProps: React.SelectHTMLAttributes<HTMLSelectElement>\n}\n\n/**\n * Provides the behavior and accessibility implementation for a hidden `<select>` element, which\n * can be used in combination with `useSelect` to support browser form autofill, mobile form\n * navigation, and native HTML form submission.\n */\nexport function useHiddenSelect<T>(props: AriaHiddenSelectOptions, state: SelectState<T>, triggerRef: RefObject<FocusableElement | null>): HiddenSelectAria {\n  let data = selectData.get(state) || {};\n  let {autoComplete, name = data.name, form = data.form, isDisabled = data.isDisabled} = props;\n  let {validationBehavior, isRequired} = data;\n  let {visuallyHiddenProps} = useVisuallyHidden({\n    style: {\n      // Prevent page scrolling.\n      position: 'fixed',\n      top: 0,\n      left: 0\n    }\n  });\n\n  useFormReset(props.selectRef, state.defaultSelectedKey, state.setSelectedKey);\n  useFormValidation({\n    validationBehavior,\n    focus: () => triggerRef.current?.focus()\n  }, state, props.selectRef);\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  let onChange = useCallback((e: React.ChangeEvent<HTMLSelectElement> | React.FormEvent<HTMLSelectElement>) => state.setSelectedKey(e.currentTarget.value), [state.setSelectedKey]);\n\n  // In Safari, the <select> cannot have `display: none` or `hidden` for autofill to work.\n  // In Firefox, there must be a <label> to identify the <select> whereas other browsers\n  // seem to identify it just by surrounding text.\n  // The solution is to use <VisuallyHidden> to hide the elements, which clips the elements to a\n  // 1px rectangle. In addition, we hide from screen readers with aria-hidden, and make the <select>\n  // non tabbable with tabIndex={-1}.\n  return {\n    containerProps: {\n      ...visuallyHiddenProps,\n      'aria-hidden': true,\n      // @ts-ignore\n      ['data-react-aria-prevent-focus']: true,\n      // @ts-ignore\n      ['data-a11y-ignore']: 'aria-hidden-focus'\n    },\n    inputProps: {\n      style: {display: 'none'}\n    },\n    selectProps: {\n      tabIndex: -1,\n      autoComplete,\n      disabled: isDisabled,\n      required: validationBehavior === 'native' && isRequired,\n      name,\n      form,\n      value: state.selectedKey ?? '',\n      onChange,\n      onInput: onChange\n    }\n  };\n}\n\n/**\n * Renders a hidden native `<select>` element, which can be used to support browser\n * form autofill, mobile form navigation, and native form submission.\n */\nexport function HiddenSelect<T>(props: HiddenSelectProps<T>): JSX.Element | null {\n  let {state, triggerRef, label, name, form, isDisabled} = props;\n  let selectRef = useRef(null);\n  let inputRef = useRef(null);\n  let {containerProps, selectProps} = useHiddenSelect({...props, selectRef: state.collection.size <= 300 ? selectRef : inputRef}, state, triggerRef);\n\n  // If used in a <form>, use a hidden input so the value can be submitted to a server.\n  // If the collection isn't too big, use a hidden <select> element for this so that browser\n  // autofill will work. Otherwise, use an <input type=\"hidden\">.\n  if (state.collection.size <= 300) {\n    return (\n      <div {...containerProps} data-testid=\"hidden-select-container\">\n        <label>\n          {label}\n          <select {...selectProps} ref={selectRef}>\n            <option />\n            {[...state.collection.getKeys()].map(key => {\n              let item = state.collection.getItem(key);\n              if (item && item.type === 'item') {\n                return (\n                  <option\n                    key={item.key}\n                    value={item.key}>\n                    {item.textValue}\n                  </option>\n                );\n              }\n            })}\n          </select>\n        </label>\n      </div>\n    );\n  } else if (name) {\n    let data = selectData.get(state) || {};\n    let {validationBehavior} = data;\n\n    let inputProps: InputHTMLAttributes<HTMLInputElement> = {\n      type: 'hidden',\n      autoComplete: selectProps.autoComplete,\n      name,\n      form,\n      disabled: isDisabled,\n      value: state.selectedKey ?? ''\n    };\n\n    if (validationBehavior === 'native') {\n      // Use a hidden <input type=\"text\"> rather than <input type=\"hidden\">\n      // so that an empty value blocks HTML form submission when the field is required.\n      return (\n        <input\n          {...inputProps}\n          ref={inputRef}\n          style={{display: 'none'}}\n          type=\"text\"\n          required={selectProps.required}\n          onChange={() => {/** Ignore react warning. */}} />\n      );\n    }\n\n    return (\n      <input {...inputProps} ref={inputRef} />\n    );\n  }\n\n  return null;\n}\n"], "names": [], "version": 3, "file": "HiddenSelect.main.js.map"}