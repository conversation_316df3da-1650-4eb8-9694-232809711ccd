{"mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;AAuDM,MAAM,4CAAoD,IAAI;AAQ9D,SAAS,0CAAa,KAA2B,EAAE,KAAqB,EAAE,GAAkC;IACjH,IAAI,oBACF,gBAAgB,cAChB,UAAU,cACV,UAAU,QACV,IAAI,QACJ,IAAI,sBACJ,qBAAqB,QACtB,GAAG;IAEJ,0HAA0H;IAC1H,qFAAqF;IACrF,IAAI,WAAW,CAAA,GAAA,gCAAU,EAAE;QAAC,OAAO;QAAU,aAAa;IAAM;IAChE,IAAI,WAAW,CAAA,GAAA,oBAAM,EAAE,IAAM,oBAAoB,IAAI,CAAA,GAAA,8CAAmB,EAAE,MAAM,UAAU,EAAE,MAAM,YAAY,EAAE,KAAK,WAAW;QAAC;QAAkB,MAAM,UAAU;QAAE,MAAM,YAAY;QAAE;QAAU;KAAI;IAEvM,IAAI,oBAAC,gBAAgB,aAAE,SAAS,EAAC,GAAG,CAAA,GAAA,mCAAa,EAC/C;oBACE;QACA,MAAM;IACR,GACA,OACA;IAGF,IAAI,YAAY,CAAC;QACf,OAAQ,EAAE,GAAG;YACX,KAAK;gBAAa;wBAIsB,uBAA4C;oBAHlF,+BAA+B;oBAC/B,EAAE,cAAc;oBAEhB,IAAI,MAAM,MAAM,WAAW,IAAI,QAAO,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA,UAAuB,MAAM,WAAW,KAAI,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA;oBAClF,IAAI,KACF,MAAM,cAAc,CAAC;oBAEvB;gBACF;YACA,KAAK;gBAAc;wBAIqB,uBAA4C;oBAHlF,+BAA+B;oBAC/B,EAAE,cAAc;oBAEhB,IAAI,MAAM,MAAM,WAAW,IAAI,QAAO,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA,UAAuB,MAAM,WAAW,KAAI,yBAAA,SAAS,WAAW,cAApB,6CAAA,4BAAA;oBAClF,IAAI,KACF,MAAM,cAAc,CAAC;oBAEvB;gBACF;QACF;IACF;IAEA,IAAI,mBAAC,eAAe,EAAC,GAAG,CAAA,GAAA,uCAAY,EAAE;QACpC,kBAAkB;QAClB,kBAAkB,MAAM,gBAAgB;QACxC,cAAa,GAAG;YACd,MAAM,cAAc,CAAC;QACvB;IACF;IAEA,IAAI,aAAC,SAAS,oBAAE,gBAAgB,qBAAE,iBAAiB,EAAC,GAAG,MAAM,iBAAiB;IAC9E,IAAI,cAAC,UAAU,cAAE,UAAU,oBAAE,gBAAgB,qBAAE,iBAAiB,EAAC,GAAG,CAAA,GAAA,8BAAO,EAAE;QAC3E,GAAG,KAAK;QACR,kBAAkB;mBAClB;QACA,cAAc,MAAM,YAAY,IAAI;IACtC;IAEA,gBAAgB,SAAS,GAAG,gBAAgB,gBAAgB;IAC5D,OAAO,gBAAgB,gBAAgB;IAEvC,IAAI,WAAW,CAAA,GAAA,oCAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI,eAAe,CAAA,GAAA,gCAAS,EAAE,iBAAiB,kBAAkB;IAEjE,IAAI,UAAU,CAAA,GAAA,2BAAI;IAElB,0CAAW,GAAG,CAAC,OAAO;oBACpB;oBACA;cACA;cACA;4BACA;IACF;IAEA,OAAO;QACL,YAAY;YACV,GAAG,UAAU;YACb,SAAS;gBACP,IAAI,CAAC,MAAM,UAAU,EAAE;wBACrB;qBAAA,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,KAAK;oBAElB,yDAAyD;oBACzD,CAAA,GAAA,mDAAqB,EAAE;gBACzB;YACF;QACF;QACA,cAAc,CAAA,GAAA,gCAAS,EAAE,UAAU;YACjC,GAAG,YAAY;wBACf;YACA,WAAW,CAAA,GAAA,2BAAI,EAAE,aAAa,SAAS,EAAE,WAAW,MAAM,SAAS;YACnE,SAAS,MAAM,OAAO;YACtB,mBAAmB;gBACjB;gBACA,YAAY,CAAC,kBAAkB;gBAC/B,YAAY,CAAC,aAAa,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,aAAa,EAAE,GAAG;aACpF,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;YACvB,SAAQ,CAAa;gBACnB,IAAI,MAAM,SAAS,EACjB;gBAGF,IAAI,MAAM,OAAO,EACf,MAAM,OAAO,CAAC;gBAGhB,IAAI,MAAM,aAAa,EACrB,MAAM,aAAa,CAAC;gBAGtB,MAAM,UAAU,CAAC;YACnB;YACA,QAAO,CAAa;gBAClB,IAAI,MAAM,MAAM,EACd;gBAGF,IAAI,MAAM,MAAM,EACd,MAAM,MAAM,CAAC;gBAGf,IAAI,MAAM,aAAa,EACrB,MAAM,aAAa,CAAC;gBAGtB,MAAM,UAAU,CAAC;YACnB;QACF;QACA,YAAY;YACV,IAAI;QACN;QACA,WAAW;YACT,GAAG,SAAS;YACZ,WAAW,MAAM,aAAa,IAAI;YAClC,uBAAuB;YACvB,oBAAoB;YACpB,wBAAwB;YACxB,cAAc;YACd,QAAQ,CAAC;gBACP,IAAI,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,aAAa,GAC1C;gBAGF,IAAI,MAAM,MAAM,EACd,MAAM,MAAM,CAAC;gBAGf,IAAI,MAAM,aAAa,EACrB,MAAM,aAAa,CAAC;gBAGtB,MAAM,UAAU,CAAC;YACnB;YACA,mBAAmB;gBACjB,UAAU,CAAC,kBAAkB;gBAC7B,YAAY,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,kBAAkB,GAAG,aAAa,EAAE,GAAG;aAClF,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACzB;0BACA;2BACA;mBACA;0BACA;2BACA;QACA,mBAAmB;wBACjB;kBACA;YACA,OAAO,MAAM,KAAK;mBAClB;YACA,YAAY;kBACZ;QACF;IACF;AACF", "sources": ["packages/@react-aria/select/src/useSelect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaButtonProps} from '@react-types/button';\nimport {AriaListBoxOptions} from '@react-aria/listbox';\nimport {AriaSelectProps} from '@react-types/select';\nimport {chain, filterDOMProps, mergeProps, useId} from '@react-aria/utils';\nimport {DOMAttributes, KeyboardDelegate, RefObject, ValidationResult} from '@react-types/shared';\nimport {FocusEvent, useMemo} from 'react';\nimport {HiddenSelectProps} from './HiddenSelect';\nimport {ListKeyboardDelegate, useTypeSelect} from '@react-aria/selection';\nimport {SelectState} from '@react-stately/select';\nimport {setInteractionModality} from '@react-aria/interactions';\nimport {useCollator} from '@react-aria/i18n';\nimport {useField} from '@react-aria/label';\nimport {useMenuTrigger} from '@react-aria/menu';\n\nexport interface AriaSelectOptions<T> extends Omit<AriaSelectProps<T>, 'children'> {\n  /**\n   * An optional keyboard delegate implementation for type to select,\n   * to override the default.\n   */\n  keyboardDelegate?: KeyboardDelegate\n}\n\nexport interface SelectAria<T> extends ValidationResult {\n  /** Props for the label element. */\n  labelProps: DOMAttributes,\n\n  /** Props for the popup trigger element. */\n  triggerProps: AriaButtonProps,\n\n  /** Props for the element representing the selected value. */\n  valueProps: DOMAttributes,\n\n  /** Props for the popup. */\n  menuProps: AriaListBoxOptions<T>,\n\n  /** Props for the select's description element, if any. */\n  descriptionProps: DOMAttributes,\n\n  /** Props for the select's error message element, if any. */\n  errorMessageProps: DOMAttributes,\n\n  /** Props for the hidden select element. */\n  hiddenSelectProps: HiddenSelectProps<T>\n}\n\ninterface SelectData {\n  isDisabled?: boolean,\n  isRequired?: boolean,\n  name?: string,\n  form?: string,\n  validationBehavior?: 'aria' | 'native'\n}\n\nexport const selectData: WeakMap<SelectState<any>, SelectData> = new WeakMap<SelectState<any>, SelectData>();\n\n/**\n * Provides the behavior and accessibility implementation for a select component.\n * A select displays a collapsible list of options and allows a user to select one of them.\n * @param props - Props for the select.\n * @param state - State for the select, as returned by `useListState`.\n */\nexport function useSelect<T>(props: AriaSelectOptions<T>, state: SelectState<T>, ref: RefObject<HTMLElement | null>): SelectAria<T> {\n  let {\n    keyboardDelegate,\n    isDisabled,\n    isRequired,\n    name,\n    form,\n    validationBehavior = 'aria'\n  } = props;\n\n  // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n  // When virtualized, the layout object will be passed in as a prop and override this.\n  let collator = useCollator({usage: 'search', sensitivity: 'base'});\n  let delegate = useMemo(() => keyboardDelegate || new ListKeyboardDelegate(state.collection, state.disabledKeys, ref, collator), [keyboardDelegate, state.collection, state.disabledKeys, collator, ref]);\n\n  let {menuTriggerProps, menuProps} = useMenuTrigger<T>(\n    {\n      isDisabled,\n      type: 'listbox'\n    },\n    state,\n    ref\n  );\n\n  let onKeyDown = (e: KeyboardEvent) => {\n    switch (e.key) {\n      case 'ArrowLeft': {\n        // prevent scrolling containers\n        e.preventDefault();\n\n        let key = state.selectedKey != null ? delegate.getKeyAbove?.(state.selectedKey) : delegate.getFirstKey?.();\n        if (key) {\n          state.setSelectedKey(key);\n        }\n        break;\n      }\n      case 'ArrowRight': {\n        // prevent scrolling containers\n        e.preventDefault();\n\n        let key = state.selectedKey != null ? delegate.getKeyBelow?.(state.selectedKey) : delegate.getFirstKey?.();\n        if (key) {\n          state.setSelectedKey(key);\n        }\n        break;\n      }\n    }\n  };\n\n  let {typeSelectProps} = useTypeSelect({\n    keyboardDelegate: delegate,\n    selectionManager: state.selectionManager,\n    onTypeSelect(key) {\n      state.setSelectedKey(key);\n    }\n  });\n\n  let {isInvalid, validationErrors, validationDetails} = state.displayValidation;\n  let {labelProps, fieldProps, descriptionProps, errorMessageProps} = useField({\n    ...props,\n    labelElementType: 'span',\n    isInvalid,\n    errorMessage: props.errorMessage || validationErrors\n  });\n\n  typeSelectProps.onKeyDown = typeSelectProps.onKeyDownCapture;\n  delete typeSelectProps.onKeyDownCapture;\n\n  let domProps = filterDOMProps(props, {labelable: true});\n  let triggerProps = mergeProps(typeSelectProps, menuTriggerProps, fieldProps);\n\n  let valueId = useId();\n\n  selectData.set(state, {\n    isDisabled,\n    isRequired,\n    name,\n    form,\n    validationBehavior\n  });\n\n  return {\n    labelProps: {\n      ...labelProps,\n      onClick: () => {\n        if (!props.isDisabled) {\n          ref.current?.focus();\n\n          // Show the focus ring so the user knows where focus went\n          setInteractionModality('keyboard');\n        }\n      }\n    },\n    triggerProps: mergeProps(domProps, {\n      ...triggerProps,\n      isDisabled,\n      onKeyDown: chain(triggerProps.onKeyDown, onKeyDown, props.onKeyDown),\n      onKeyUp: props.onKeyUp,\n      'aria-labelledby': [\n        valueId,\n        triggerProps['aria-labelledby'],\n        triggerProps['aria-label'] && !triggerProps['aria-labelledby'] ? triggerProps.id : null\n      ].filter(Boolean).join(' '),\n      onFocus(e: FocusEvent) {\n        if (state.isFocused) {\n          return;\n        }\n\n        if (props.onFocus) {\n          props.onFocus(e);\n        }\n\n        if (props.onFocusChange) {\n          props.onFocusChange(true);\n        }\n\n        state.setFocused(true);\n      },\n      onBlur(e: FocusEvent) {\n        if (state.isOpen) {\n          return;\n        }\n\n        if (props.onBlur) {\n          props.onBlur(e);\n        }\n\n        if (props.onFocusChange) {\n          props.onFocusChange(false);\n        }\n\n        state.setFocused(false);\n      }\n    }),\n    valueProps: {\n      id: valueId\n    },\n    menuProps: {\n      ...menuProps,\n      autoFocus: state.focusStrategy || true,\n      shouldSelectOnPressUp: true,\n      shouldFocusOnHover: true,\n      disallowEmptySelection: true,\n      linkBehavior: 'selection',\n      onBlur: (e) => {\n        if (e.currentTarget.contains(e.relatedTarget as Node)) {\n          return;\n        }\n\n        if (props.onBlur) {\n          props.onBlur(e);\n        }\n\n        if (props.onFocusChange) {\n          props.onFocusChange(false);\n        }\n\n        state.setFocused(false);\n      },\n      'aria-labelledby': [\n        fieldProps['aria-labelledby'],\n        triggerProps['aria-label'] && !fieldProps['aria-labelledby'] ? triggerProps.id : null\n      ].filter(Boolean).join(' ')\n    },\n    descriptionProps,\n    errorMessageProps,\n    isInvalid,\n    validationErrors,\n    validationDetails,\n    hiddenSelectProps: {\n      isDisabled,\n      name,\n      label: props.label,\n      state,\n      triggerRef: ref,\n      form\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useSelect.main.js.map"}