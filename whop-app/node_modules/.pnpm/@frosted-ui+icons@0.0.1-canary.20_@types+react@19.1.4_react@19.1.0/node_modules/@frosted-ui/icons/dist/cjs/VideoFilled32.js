"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VideoFilled32 = void 0;
var React = __importStar(require("react"));
var VideoFilled32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M9.75167 4H5.75C3.12665 4 1 6.12665 1 8.75V11.5H2.25166L9.75167 4ZM4.37298 11.5H11.2517L18.7517 4H11.873L4.37298 11.5ZM31 11.5H22.1857L28.8871 4.79865C30.1611 5.65059 31 7.10232 31 8.75V11.5ZM26.25 4C26.6535 4 27.0453 4.05032 27.4194 4.14502L20.0644 11.5H13.373L20.873 4H26.25ZM31 13H1V23.25C1 25.8734 3.12665 28 5.75 28H26.25C28.8734 28 31 25.8734 31 23.25V13ZM12.65 24.1474V16.3568C12.65 15.973 13.0645 15.7323 13.3978 15.9225L20.2582 19.8372C20.5953 20.0295 20.5943 20.5159 20.2564 20.7068L13.3959 24.5827C13.0626 24.771 12.65 24.5302 12.65 24.1474Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.VideoFilled32 = VideoFilled32;
exports.default = exports.VideoFilled32;
//# sourceMappingURL=VideoFilled32.js.map