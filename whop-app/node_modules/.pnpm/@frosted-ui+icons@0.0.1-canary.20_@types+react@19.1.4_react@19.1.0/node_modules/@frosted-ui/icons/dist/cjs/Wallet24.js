"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Wallet24 = void 0;
var React = __importStar(require("react"));
var Wallet24 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "24", height: "24", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M19.2493 6.5C19.2493 5.5335 18.4658 4.75 17.4993 4.75H3.94922C3.25886 4.75 2.69922 5.30964 2.69922 6M2.69922 6V17.25C2.69922 18.3546 3.59465 19.25 4.69922 19.25H19.1992C20.3038 19.25 21.1992 18.3546 21.1992 17.25V9.25C21.1992 8.14543 20.3038 7.25 19.1992 7.25H3.94922C3.25886 7.25 2.69922 6.69036 2.69922 6ZM17.2493 13.25C17.2493 13.6642 16.9135 14 16.4993 14C16.0851 14 15.7493 13.6642 15.7493 13.25C15.7493 12.8358 16.0851 12.5 16.4993 12.5C16.9135 12.5 17.2493 12.8358 17.2493 13.25Z", stroke: color, strokeWidth: "1.5", strokeLinecap: "round", strokeLinejoin: "round" })));
};
exports.Wallet24 = Wallet24;
exports.default = exports.Wallet24;
//# sourceMappingURL=Wallet24.js.map