"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Shop32 = void 0;
var React = __importStar(require("react"));
var Shop32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M11.3592 2.5H7.35049C5.28696 2.5 3.41472 3.70862 2.56517 5.58916L1.89056 7.08245C1.63314 7.65226 1.5 8.27036 1.5 8.89562V8.99149C1.5 11.3709 3.42886 13.2997 5.80824 13.2997C7.87367 13.2997 9.64851 11.8339 10.0388 9.80569L11.2679 3.41945L11.3592 2.5ZM11.5082 1H7.35049C4.69749 1 2.29043 2.55388 1.19819 4.97161L0.523578 6.4649C0.178484 7.22879 0 8.0574 0 8.89562V8.99149C0 11.2207 1.25581 13.1566 3.09857 14.1302V24.8209C3.09857 27.7792 5.49677 30.1774 8.45511 30.1774H11.571L11.5764 30.1774L11.5818 30.1774H20.4028L20.4082 30.1774L20.4136 30.1774H23.5295C26.4879 30.1774 28.8861 27.7792 28.8861 24.8209V14.1383C30.7372 13.1673 32 11.2269 32 8.99149V8.89562C32 8.0574 31.8215 7.22879 31.4764 6.4649L30.8018 4.97161C29.7096 2.55388 27.3025 1 24.6495 1H20.4768H20.2665H18.7389H13.2611H11.7335H11.5082ZM12.7538 3.63576L12.9724 2.5H19.0276L19.2152 3.47459L19.776 9.1218C19.9982 11.3592 18.2409 13.2997 15.9925 13.2997C13.7441 13.2997 11.9867 11.3592 12.2089 9.12181L12.7538 3.63576ZM11.0657 11.4618C10.1271 13.4606 8.10041 14.7997 5.80824 14.7997C5.39342 14.7997 4.98876 14.7562 4.59857 14.6736V24.8209C4.59857 26.9508 6.3252 28.6774 8.45511 28.6774H10.8264V20.9384C10.8264 20.03 11.5628 19.2936 12.4712 19.2936H19.5135C20.4219 19.2936 21.1582 20.03 21.1582 20.9384V28.6774H23.5295C25.6595 28.6774 27.3861 26.9508 27.3861 24.8209V14.6768C27.0006 14.7574 26.6011 14.7997 26.1918 14.7997C23.893 14.7997 21.8612 13.4529 20.9262 11.4445C20.154 13.3958 18.2509 14.7997 15.9925 14.7997C13.7408 14.7997 11.8423 13.4041 11.0657 11.4618ZM20.7011 3.25827L21.9612 9.80569C22.3515 11.8339 24.1263 13.2997 26.1918 13.2997C28.5711 13.2997 30.5 11.3709 30.5 8.99149V8.89562C30.5 8.27036 30.3669 7.65226 30.1094 7.08245L29.4348 5.58916C28.5853 3.70862 26.713 2.5 24.6495 2.5H20.6258L20.7011 3.25827ZM19.6582 28.6774H12.3264V20.9384C12.3264 20.8584 12.3912 20.7936 12.4712 20.7936H19.5135C19.5934 20.7936 19.6582 20.8584 19.6582 20.9384V28.6774Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.Shop32 = Shop32;
exports.default = exports.Shop32;
//# sourceMappingURL=Shop32.js.map