"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TelegramFilled20 = void 0;
var React = __importStar(require("react"));
var TelegramFilled20 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "20", height: "20", viewBox: "0 0 20 20", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M3.09981 9.08131C7.39558 7.21617 10.2594 5.98645 11.6913 5.39235C15.7845 3.69589 16.6339 3.40126 17.1887 3.39139C17.3107 3.38933 17.5824 3.41939 17.7597 3.56234C17.907 3.68282 17.9485 3.84574 17.9692 3.96001C17.9876 4.07429 18.0129 4.33473 17.9923 4.53804C17.7713 6.86026 16.8113 12.4955 16.3232 15.0965C16.1183 16.1971 15.7108 16.5661 15.3171 16.6021C14.4607 16.6806 13.8115 16.0385 12.9827 15.4972C11.6867 14.6498 10.9546 14.1224 9.69537 13.2957C8.24044 12.3402 9.18428 11.8149 10.0131 10.9567C10.2294 10.7321 14.0003 7.31415 14.0717 7.00414C14.0809 6.96536 14.0902 6.82079 14.0026 6.74461C13.9175 6.66819 13.7907 6.69436 13.6987 6.715C13.5674 6.74438 11.4979 8.10925 7.48296 10.8094C6.89592 11.2119 6.36414 11.4081 5.88529 11.3978C5.36041 11.3865 4.34754 11.1013 3.59475 10.8576C2.6739 10.5586 1.93948 10.4005 2.00394 9.89265C2.03617 9.62831 2.40227 9.35777 3.09981 9.08131Z", fill: color })));
};
exports.TelegramFilled20 = TelegramFilled20;
exports.default = exports.TelegramFilled20;
//# sourceMappingURL=TelegramFilled20.js.map