"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThumbUpFilled32 = void 0;
var React = __importStar(require("react"));
var ThumbUpFilled32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M8.71437 13.5833H4.99996C3.84938 13.5833 2.91663 14.516 2.91663 15.6666V25.6666C2.91663 26.8172 3.84938 27.75 4.99996 27.75H23.2293C25.5767 27.75 27.5721 26.0352 27.9252 23.7145L29.0412 16.3812C29.4786 13.5063 27.2533 10.9166 24.3452 10.9166H18.2308L18.8633 6.85964C19.1867 4.7855 17.5757 2.91663 15.4813 2.91663C14.7023 2.91663 13.9853 3.35141 13.6276 4.04588L8.71437 13.5833ZM8.24996 26.25V15.0833H4.99996C4.67779 15.0833 4.41663 15.3444 4.41663 15.6666V25.6666C4.41663 25.9888 4.67779 26.25 4.99996 26.25H8.24996Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.ThumbUpFilled32 = ThumbUpFilled32;
exports.default = exports.ThumbUpFilled32;
//# sourceMappingURL=ThumbUpFilled32.js.map