"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TiktokFilled16 = void 0;
var React = __importStar(require("react"));
var TiktokFilled16 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "16", height: "16", viewBox: "0 0 16 16", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M13.8102 6.80897C12.6238 6.80897 11.5253 6.43201 10.6283 5.79136V10.4484C10.6283 12.7781 8.7388 14.6667 6.40801 14.6667C5.53835 14.6667 4.73003 14.4039 4.05856 13.9532C2.93033 13.1961 2.1875 11.9087 2.1875 10.4484C2.1875 8.11891 4.07707 6.23033 6.40807 6.23037C6.60179 6.23028 6.79527 6.24343 6.98713 6.26965V6.78671L6.98707 8.60271C6.80233 8.54411 6.60544 8.51231 6.40115 8.51231C5.33486 8.51231 4.47063 9.37624 4.47063 10.4418C4.47063 11.1952 4.90263 11.8476 5.53258 12.1654C5.79378 12.2971 6.08877 12.3713 6.40116 12.3713C7.46527 12.3713 8.328 11.5109 8.33167 10.4484V1.33337H10.6283V1.6269C10.6364 1.71465 10.6481 1.80207 10.6633 1.88892C10.8227 2.79762 11.3663 3.574 12.1209 4.04461C12.6275 4.36073 13.2131 4.52785 13.8103 4.52694L13.8102 6.80897Z", fill: color })));
};
exports.TiktokFilled16 = TiktokFilled16;
exports.default = exports.TiktokFilled16;
//# sourceMappingURL=TiktokFilled16.js.map