"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorefrontItem32 = void 0;
var React = __importStar(require("react"));
var StorefrontItem32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M30.25 12.25V6.75C30.25 4.54086 28.4591 2.75 26.25 2.75H5.75C3.54086 2.75 1.75 4.54086 1.75 6.75V21.25C1.75 23.4591 3.54086 25.25 5.75 25.25H10.25", stroke: color, strokeWidth: "1.5", strokeLinecap: "round", strokeLinejoin: "round" }),
        React.createElement("path", { d: "M22 12.5C21.6978 12.5 21.4018 12.5857 21.1463 12.7472L15.0638 16.5919C14.7128 16.8138 14.5 17.2 14.5 17.6152V24.3848C14.5 24.8 14.7128 25.1862 15.0638 25.4081L21.1463 29.2528C21.4018 29.4143 21.6978 29.5 22 29.5C22.3022 29.5 22.5982 29.4143 22.8537 29.2528L28.9362 25.4081C29.2872 25.1862 29.5 24.8 29.5 24.3848V17.6152C29.5 17.2 29.2872 16.8138 28.9362 16.5919L22.8537 12.7472C22.5982 12.5857 22.3022 12.5 22 12.5ZM20.3448 11.4793C20.8401 11.1662 21.4141 11 22 11C22.5859 11 23.1599 11.1662 23.6552 11.4793L29.7377 15.324C30.5235 15.8207 31 16.6856 31 17.6152V24.3848C31 25.3144 30.5235 26.1793 29.7377 26.676L23.6552 30.5207C23.1599 30.8338 22.5859 31 22 31C21.4141 31 20.8401 30.8338 20.3448 30.5207L14.2623 26.676C13.4765 26.1793 13 25.3144 13 24.3848V17.6152C13 16.6856 13.4765 15.8207 14.2623 15.324L20.3448 11.4793Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" }),
        React.createElement("path", { d: "M16.8382 18.8461C16.474 18.6489 16.3385 18.1938 16.5356 17.8295C16.7328 17.4652 17.1879 17.3297 17.5522 17.5269L21.9971 19.9325L26.4419 17.5269C26.8062 17.3297 27.2614 17.4652 27.4585 17.8295C27.6557 18.1938 27.5202 18.6489 27.1559 18.8461L22.7437 21.234V26.4672C22.7437 26.8815 22.4079 27.2172 21.9937 27.2172C21.5795 27.2172 21.2437 26.8815 21.2437 26.4672V21.2303L16.8382 18.8461Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" }),
        React.createElement("circle", { cx: "6.5", cy: "7.5", r: "1.5", fill: color }),
        React.createElement("circle", { cx: "11.5", cy: "7.5", r: "1.5", fill: color }),
        React.createElement("circle", { cx: "16.5", cy: "7.5", r: "1.5", fill: color })));
};
exports.StorefrontItem32 = StorefrontItem32;
exports.default = exports.StorefrontItem32;
//# sourceMappingURL=StorefrontItem32.js.map