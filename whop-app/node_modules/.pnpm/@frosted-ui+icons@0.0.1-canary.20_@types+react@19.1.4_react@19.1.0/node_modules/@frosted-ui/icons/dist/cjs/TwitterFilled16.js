"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwitterFilled16 = void 0;
var React = __importStar(require("react"));
var TwitterFilled16 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "16", height: "16", viewBox: "0 0 16 16", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M15.5 3.42083C14.9484 3.66143 14.3558 3.82399 13.7329 3.89747C14.3683 3.52292 14.8559 2.92858 15.0858 2.22109C14.4913 2.56833 13.8326 2.82063 13.1317 2.95589C12.5701 2.36805 11.7708 2 10.8856 2C9.18647 2 7.8084 3.35645 7.8084 5.03024C7.8084 5.26693 7.8362 5.49713 7.88767 5.71952C5.32912 5.59272 3.06254 4.38647 1.54444 2.55403C1.28019 3.00271 1.12825 3.52292 1.12825 4.07695C1.12825 5.12778 1.67194 6.05571 2.49705 6.59868C1.99234 6.58242 1.51801 6.44651 1.10315 6.21957V6.25859C1.10315 7.72627 2.16344 8.95067 3.57187 9.229C3.31291 9.29793 3.0414 9.33433 2.76129 9.33433C2.56311 9.33433 2.36955 9.31613 2.18193 9.281C2.57368 10.484 3.70995 11.3606 5.05695 11.384C4.00326 12.1968 2.67608 12.6813 1.23461 12.6813C0.986213 12.6813 0.741127 12.6669 0.5 12.639C1.86219 13.4993 3.47939 14 5.21681 14C10.8783 14 13.9733 9.3844 13.9733 5.38138C13.9733 5.25133 13.97 5.11997 13.9641 4.98992C14.5652 4.56205 15.0871 4.02948 15.4987 3.42278L15.5 3.42083Z", fill: color })));
};
exports.TwitterFilled16 = TwitterFilled16;
exports.default = exports.TwitterFilled16;
//# sourceMappingURL=TwitterFilled16.js.map