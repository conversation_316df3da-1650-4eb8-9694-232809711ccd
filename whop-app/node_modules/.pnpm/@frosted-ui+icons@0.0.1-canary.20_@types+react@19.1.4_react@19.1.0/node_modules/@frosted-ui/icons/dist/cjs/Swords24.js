"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Swords24 = void 0;
var React = __importStar(require("react"));
var Swords24 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "24", height: "24", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("g", { clipPath: "url(#clip0_1083_205)" },
            React.createElement("path", { d: "M4.53648 0.469796C4.3409 0.274214 4.05355 0.202155 3.78882 0.282305C3.52409 0.362455 3.32497 0.581804 3.27072 0.853026C2.81823 3.11525 3.52635 5.45391 5.15762 7.08518L14.2384 16.166C13.0239 17.8839 13.1856 20.2771 14.7237 21.8152L15.3692 22.4607C17.0841 24.1756 19.8619 24.1796 21.5817 22.4727L21.594 22.4607L23.5305 20.5242C23.8233 20.2313 23.8233 19.7565 23.5305 19.4636L18.2513 14.1844L18.6891 13.7466C18.982 13.4537 18.982 12.9789 18.6891 12.686C18.3962 12.3931 17.9213 12.3931 17.6284 12.686L17.1905 13.1239L4.53648 0.469796ZM16.1299 14.1845L4.66069 2.71532C4.77772 3.9502 5.31859 5.12483 6.21828 6.02452L15.2541 15.0603L16.1299 14.1845ZM20.7293 0.853042C20.6751 0.581816 20.4759 0.362461 20.2112 0.282307C19.9465 0.202153 19.6591 0.274212 19.4635 0.469796L13.201 6.73233L12.6707 7.26266L13.7313 8.32332L14.2617 7.79299L19.3393 2.71534C19.2223 3.95022 18.6814 5.12484 17.7817 6.02451L17.7817 6.02451L15.1374 8.66877L14.6071 9.1991L15.6678 10.2598L16.1981 9.72943L18.8424 7.08518L18.8424 7.08518C20.4737 5.4539 21.1817 3.11524 20.7293 0.853042ZM5.3109 12.686C5.60379 12.3931 6.07867 12.3931 6.37156 12.686L6.80944 13.1239L7.73227 12.2011L8.26261 11.6707L9.32326 12.7314L8.79292 13.2617L7.8702 14.1844L8.746 15.0602L9.66876 14.1375L10.1991 13.6072L11.2597 14.6678L10.7294 15.1982L9.76156 16.166C10.9761 17.8839 10.8143 20.2771 9.27626 21.8152L8.63078 22.4607C6.91192 24.1796 4.12511 24.1796 2.40624 22.4607L0.469804 20.5243C0.20445 20.2589 0.179501 19.8442 0.394955 19.5507C0.413599 19.5254 0.434044 19.5009 0.45629 19.4775L0.469944 19.4634L5.74882 14.1846L5.3109 13.7466C5.01801 13.4537 5.01801 12.9789 5.3109 12.686ZM7.68526 16.121L6.80947 15.2452L2.06092 19.9938L2.93671 20.8696L7.68526 16.121ZM8.2156 20.7546C9.1653 19.8049 9.319 18.3606 8.67671 17.2508L4.0664 21.8612C5.17618 22.5034 6.62043 22.3497 7.57012 21.4L8.2156 20.7546ZM15.7844 20.7546C14.8346 19.8048 14.681 18.3605 15.3234 17.2507L19.9338 21.8611C18.824 22.5035 17.3796 22.3498 16.4299 21.4L15.7844 20.7546ZM21.0636 20.8696L21.9394 19.9938L17.1908 15.2452L16.315 16.121L21.0636 20.8696Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })),
        React.createElement("defs", null,
            React.createElement("clipPath", { id: "clip0_1083_205" },
                React.createElement("path", { fill: color, d: "M0 0H24V24H0z" })))));
};
exports.Swords24 = Swords24;
exports.default = exports.Swords24;
//# sourceMappingURL=Swords24.js.map