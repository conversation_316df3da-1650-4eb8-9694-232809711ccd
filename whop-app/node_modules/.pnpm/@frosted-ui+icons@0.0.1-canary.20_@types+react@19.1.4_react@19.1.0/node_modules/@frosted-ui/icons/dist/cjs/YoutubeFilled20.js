"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.YoutubeFilled20 = void 0;
var React = __importStar(require("react"));
var YoutubeFilled20 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "20", height: "20", viewBox: "0 0 20 20", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M16.8369 3.33976C17.5903 3.57298 18.1824 4.25767 18.3841 5.12892C18.7485 6.70651 18.75 9.99996 18.75 9.99996C18.75 9.99996 18.75 13.2935 18.3841 14.871C18.1824 15.7423 17.5903 16.427 16.8369 16.6601C15.4727 17.0833 10 17.0833 10 17.0833C10 17.0833 4.52728 17.0833 3.16307 16.6601C2.40966 16.427 1.81758 15.7423 1.6159 14.871C1.25 13.2935 1.25 9.99996 1.25 9.99996C1.25 9.99996 1.25 6.70651 1.6159 5.12892C1.81758 4.25767 2.40966 3.57298 3.16307 3.33976C4.52728 2.91663 10 2.91663 10 2.91663C10 2.91663 15.4727 2.91663 16.8369 3.33976ZM12.9278 10.0002L8.16487 12.7499V7.2505L12.9278 10.0002Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.YoutubeFilled20 = YoutubeFilled20;
exports.default = exports.YoutubeFilled20;
//# sourceMappingURL=YoutubeFilled20.js.map