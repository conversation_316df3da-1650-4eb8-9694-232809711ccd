"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhopLogo24 = void 0;
var React = __importStar(require("react"));
var WhopLogo24 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "24", height: "24", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M5.1796 6.87563C3.86519 6.87563 2.9591 7.45237 2.27341 8.10457 2.27341 8.10457 1.99653 8.36695 2.00003 8.37495L4.87973 11.2546 7.75893 8.37495C7.21368 7.62429 6.18564 6.87563 5.1796 6.87563zM12.2904 6.87563C10.976 6.87563 10.0699 7.45237 9.3842 8.10457 9.3842 8.10457 9.13131 8.35996 9.11982 8.37495L5.56042 11.9348 8.43563 14.81 14.8697 8.37495C14.3245 7.62429 13.2969 6.87563 12.2904 6.87563zM19.4207 6.87563C18.1063 6.87563 17.2002 7.45237 16.5145 8.10457 16.5145 8.10457 16.2511 8.36195 16.2411 8.37495L9.12085 15.4962 9.87451 16.2499C11.0405 17.4159 12.9491 17.4159 14.1151 16.2499L21.991 8.37495H22C21.4548 7.62429 20.4272 6.87563 19.4207 6.87563z", fill: color })));
};
exports.WhopLogo24 = WhopLogo24;
exports.default = exports.WhopLogo24;
//# sourceMappingURL=WhopLogo24.js.map