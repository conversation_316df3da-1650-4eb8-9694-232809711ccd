"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Telephone12 = void 0;
var React = __importStar(require("react"));
var Telephone12 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "12", height: "12", viewBox: "0 0 12 12", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M7.8495 4.34005L7.92413 5.08633L7.92413 5.08633L7.8495 4.34005ZM6.32473 4.49253L6.2501 3.74625C5.89487 3.78177 5.61392 4.06276 5.57844 4.41799L6.32473 4.49253ZM6.25002 6L7.00002 6L6.25002 6ZM6.32471 7.50747L5.57843 7.58201C5.61391 7.93724 5.89486 8.21823 6.25008 8.25375L6.32471 7.50747ZM7.8495 7.65995L7.92413 6.91367L7.92413 6.91367L7.8495 7.65995ZM7.75 11.25L7.75 12L7.75 11.25ZM7.25002 11.25L7.25002 10.5L7.25002 11.25ZM6.25 11.25L6.25 10.5L6.25 11.25ZM6.04189 11.25L6.04189 12L6.04189 11.25ZM5.09865 10.8888L5.66 10.3914L5.09865 10.8888ZM5.09865 1.11124L5.66 1.60862L5.66 1.60862L5.09865 1.11124ZM7.75 -4.37114e-08L7.25002 -6.55663e-08L7.25002 1.5L7.75 1.5L7.75 -4.37114e-08ZM9.5 1.75C9.5 0.783502 8.7165 -1.46443e-09 7.75 -4.37114e-08L7.75 1.5C7.88807 1.5 8 1.61193 8 1.75L9.5 1.75ZM9.5 3.34501L9.5 1.75L8 1.75L8 3.34501L9.5 3.34501ZM7.92413 5.08633C8.81873 4.99687 9.5 4.24408 9.5 3.34501L8 3.34501C8 3.47345 7.90268 3.58099 7.77487 3.59377L7.92413 5.08633ZM6.39935 5.23881L7.92413 5.08633L7.77488 3.59377L6.2501 3.74625L6.39935 5.23881ZM7.00002 6C7.00002 5.48983 7.02669 5.01085 7.07101 4.56707L5.57844 4.41799C5.52942 4.90877 5.50002 5.43756 5.50002 6L7.00002 6ZM7.071 7.43293C7.02668 6.98915 7.00002 6.51018 7.00002 6L5.50002 6C5.50002 6.56244 5.52941 7.09122 5.57843 7.58201L7.071 7.43293ZM6.25008 8.25375L7.77488 8.40623L7.92413 6.91367L6.39934 6.76119L6.25008 8.25375ZM7.77488 8.40623C7.90268 8.41901 8 8.52655 8 8.65499L9.5 8.65499C9.5 7.75592 8.81873 7.00313 7.92413 6.91367L7.77488 8.40623ZM8 8.65499L8 10.25L9.5 10.25L9.5 8.65499L8 8.65499ZM8 10.25C8 10.3881 7.88807 10.5 7.75 10.5L7.75 12C8.7165 12 9.5 11.2165 9.5 10.25L8 10.25ZM7.75 10.5L7.25002 10.5L7.25002 12L7.75 12L7.75 10.5ZM7.25002 10.5L6.25 10.5L6.25 12L7.25002 12L7.25002 10.5ZM6.04189 12L6.25 12L6.25 10.5L6.04189 10.5L6.04189 12ZM4.53731 11.3861C4.97031 11.8748 5.57301 12 6.04189 12L6.04189 10.5C5.80107 10.5 5.69765 10.4339 5.66 10.3914L4.53731 11.3861ZM3.00002 6C3.00002 9.06612 3.87977 10.6441 4.53731 11.3861L5.66 10.3914C5.28423 9.96729 4.50002 8.76242 4.50002 6L3.00002 6ZM4.53731 0.613852C3.87977 1.35595 3.00002 2.93388 3.00002 6L4.50002 6C4.50002 3.23758 5.28423 2.03271 5.66 1.60862L4.53731 0.613852ZM6.04189 -1.18375e-07C5.57301 -1.38871e-07 4.97031 0.12517 4.53731 0.613852L5.66 1.60862C5.69765 1.56612 5.80107 1.5 6.04189 1.5L6.04189 -1.18375e-07ZM6.25 -1.09278e-07L6.04189 -1.18375e-07L6.04189 1.5L6.25 1.5L6.25 -1.09278e-07ZM7.25002 -6.55663e-08L6.25 -1.09278e-07L6.25 1.5L7.25002 1.5L7.25002 -6.55663e-08Z", fill: color })));
};
exports.Telephone12 = Telephone12;
exports.default = exports.Telephone12;
//# sourceMappingURL=Telephone12.js.map