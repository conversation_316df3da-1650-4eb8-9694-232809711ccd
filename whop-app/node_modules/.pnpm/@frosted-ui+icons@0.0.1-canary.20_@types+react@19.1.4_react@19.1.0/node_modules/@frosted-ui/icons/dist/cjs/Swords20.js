"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Swords20 = void 0;
var React = __importStar(require("react"));
var Swords20 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "20", height: "20", viewBox: "0 0 20 20", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("g", { clipPath: "url(#clip0_1083_207)" },
            React.createElement("path", { d: "M17.2759 0.85292C17.2217 0.581694 17.0226 0.362339 16.7578 0.282185C16.4931 0.202031 16.2057 0.27409 16.0102 0.469674L11.1931 5.28678L10.6627 5.81711L11.7234 6.87777L12.2537 6.34744L15.8125 2.78868C15.6539 3.62009 15.2501 4.39855 14.6341 5.01448L14.6341 5.01449L12.7774 6.87115L12.2471 7.40148L13.3078 8.46214L13.8381 7.93181L15.6947 6.07516L15.6948 6.07515C17.0617 4.70822 17.655 2.74853 17.2759 0.85292ZM4.43048 10.4649C4.72338 10.172 5.19825 10.172 5.49114 10.4649L5.75301 10.7267L6.20859 10.2711L6.73892 9.7408L7.79959 10.8014L7.26926 11.3318L6.81376 11.7873L7.33749 12.311L7.793 11.8555L8.32333 11.3252L9.384 12.3858L8.85367 12.9162L8.34484 13.425C9.30475 14.876 9.14571 16.8492 7.86774 18.1271L7.33962 18.6553C5.88001 20.1149 3.51353 20.1149 2.05392 18.6553L0.469548 17.0709C0.176655 16.778 0.176655 16.3031 0.469548 16.0102L0.476777 16.0031L4.69243 11.7875L4.43048 11.5255C4.13759 11.2326 4.13759 10.7577 4.43048 10.4649ZM6.2768 13.3718L5.75308 12.8481L2.0607 16.5405L2.58443 17.0642L6.2768 13.3718ZM6.80708 17.0665C7.49487 16.3787 7.6413 15.3546 7.24637 14.5236L3.73603 18.0339C4.56704 18.4288 5.59117 18.2824 6.27896 17.5946L6.80708 17.0665ZM17.9461 18.6553L19.5304 17.0709C19.8233 16.778 19.8233 16.3032 19.5304 16.0103L15.3075 11.7873L15.5693 11.5255C15.8622 11.2326 15.8622 10.7577 15.5693 10.4649C15.2764 10.172 14.8015 10.172 14.5087 10.4649L14.1586 10.8149L3.9016 0.557812C3.22597 3.93565 6.04411 6.75365 8.48422 9.19363L8.52465 9.23406L12.1321 12.8415C10.6725 14.3011 10.6725 16.6675 12.1321 18.1271L12.6602 18.6553C14.1158 20.1109 16.4734 20.1149 17.9339 18.6672L17.9461 18.6553ZM13.1927 17.0665C12.5049 16.3787 12.3585 15.3544 12.7535 14.5234L16.2639 18.0338C15.4329 18.4288 14.4087 18.2824 13.7208 17.5946L13.1927 17.0665ZM17.4156 17.0642L17.9394 16.5405L14.247 12.8481L13.7233 13.3718L17.4156 17.0642Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })),
        React.createElement("defs", null,
            React.createElement("clipPath", { id: "clip0_1083_207" },
                React.createElement("path", { fill: color, d: "M0 0H20V20H0z" })))));
};
exports.Swords20 = Swords20;
exports.default = exports.Swords20;
//# sourceMappingURL=Swords20.js.map