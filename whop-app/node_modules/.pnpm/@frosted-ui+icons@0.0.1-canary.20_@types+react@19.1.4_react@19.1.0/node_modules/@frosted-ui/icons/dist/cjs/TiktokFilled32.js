"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TiktokFilled32 = void 0;
var React = __importStar(require("react"));
var TiktokFilled32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M27.6204 13.6178C25.2476 13.6178 23.0507 12.8639 21.2567 11.5826V20.8968C21.2567 25.5561 17.4776 29.3333 12.816 29.3333C11.0767 29.3333 9.46005 28.8077 8.11712 27.9064C5.86065 26.3921 4.375 23.8173 4.375 20.8968C4.375 16.2377 8.15415 12.4605 12.8161 12.4606C13.2036 12.4604 13.5905 12.4867 13.9743 12.5392V13.5733L13.9741 17.2053C13.6047 17.0881 13.2109 17.0245 12.8023 17.0245C10.6697 17.0245 8.94125 18.7524 8.94125 20.8836C8.94125 22.3904 9.80525 23.6952 11.0652 24.3308C11.5876 24.5941 12.1775 24.7425 12.8023 24.7425C14.9305 24.7425 16.656 23.0217 16.6633 20.8968V2.66663H21.2567V3.25368C21.2728 3.42919 21.2961 3.60403 21.3267 3.77772C21.6453 5.59512 22.7327 7.14788 24.2417 8.08909C25.2551 8.72133 26.4261 9.05557 27.6205 9.05376L27.6204 13.6178Z", fill: color })));
};
exports.TiktokFilled32 = TiktokFilled32;
exports.default = exports.TiktokFilled32;
//# sourceMappingURL=TiktokFilled32.js.map