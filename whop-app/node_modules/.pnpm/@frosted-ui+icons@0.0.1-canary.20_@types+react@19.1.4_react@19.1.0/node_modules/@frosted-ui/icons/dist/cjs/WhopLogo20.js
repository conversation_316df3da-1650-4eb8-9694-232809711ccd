"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhopLogo20 = void 0;
var React = __importStar(require("react"));
var WhopLogo20 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "20", height: "20", viewBox: "0 0 20 20", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M4.54368 5.9005C3.49215 5.9005 2.76728 6.36189 2.21873 6.88365 2.21873 6.88365 1.99723 7.09356 2.00003 7.09996L4.30379 9.40372 6.60715 7.09996C6.17094 6.49943 5.34851 5.9005 4.54368 5.9005zM10.2322 5.9005C9.18071 5.9005 8.45584 6.36189 7.90729 6.88365 7.90729 6.88365 7.70498 7.08796 7.69578 7.09996L4.84827 9.94787 7.14843 12.248 12.2957 7.09996C11.8595 6.49943 11.0375 5.9005 10.2322 5.9005zM15.9365 5.9005C14.885 5.9005 14.1601 6.36189 13.6116 6.88365 13.6116 6.88365 13.4009 7.08956 13.3929 7.09996L7.69666 12.797 8.29958 13.3999C9.23236 14.3327 10.7593 14.3327 11.6921 13.3999L17.9928 7.09996H18C17.5638 6.49943 16.7418 5.9005 15.9365 5.9005z", fill: color })));
};
exports.WhopLogo20 = WhopLogo20;
exports.default = exports.WhopLogo20;
//# sourceMappingURL=WhopLogo20.js.map