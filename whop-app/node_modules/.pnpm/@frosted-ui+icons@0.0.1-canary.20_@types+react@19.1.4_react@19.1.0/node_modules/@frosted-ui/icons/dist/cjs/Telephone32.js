"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Telephone32 = void 0;
var React = __importStar(require("react"));
var Telephone32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M15.3602 2.75L15.3602 3.5L15.3602 2.75ZM15.25 2.75L15.25 2C15.0511 2 14.8603 2.07902 14.7197 2.21967C14.579 2.36032 14.5 2.55109 14.5 2.75L15.25 2.75ZM21.25 4.25L20.5 4.25L21.25 4.25ZM21.25 8.47931L22 8.47931L21.25 8.47931ZM19.9966 9.9589L19.8733 9.2191L19.8733 9.2191L19.9966 9.9589ZM15.2854 10.7441L15.1621 10.0043C14.8526 10.0559 14.6079 10.2947 14.5488 10.6028L15.2854 10.7441ZM14.75 16L15.5 16L14.75 16ZM15.2854 21.2559L14.5488 21.3972C14.6079 21.7053 14.8526 21.9441 15.1621 21.9957L15.2854 21.2559ZM19.9966 22.0411L20.1199 21.3013L20.1199 21.3013L19.9966 22.0411ZM15.25 29.25L14.5 29.25C14.5 29.4489 14.579 29.6397 14.7197 29.7803C14.8603 29.921 15.0511 30 15.25 30L15.25 29.25ZM15.25 29.248L16 29.248C16 28.8445 15.6809 28.5134 15.2777 28.4985L15.25 29.248ZM13.0052 27.8273L13.6884 27.5179L13.0052 27.8273ZM13.0052 4.17267L12.322 3.86327L12.322 3.86328L13.0052 4.17267ZM15.25 2.75203L15.2777 3.50152C15.6809 3.48661 16 3.15546 16 2.75203L15.25 2.75203ZM15.3602 2L15.25 2L15.25 3.5L15.3602 3.5L15.3602 2ZM17.75 2L15.3602 2L15.3602 3.5L17.75 3.5L17.75 2ZM19.75 2L17.75 2L17.75 3.5L19.75 3.5L19.75 2ZM22 4.25C22 3.00736 20.9926 2 19.75 2L19.75 3.5C20.1642 3.5 20.5 3.83579 20.5 4.25L22 4.25ZM22 8.47931L22 4.25L20.5 4.25L20.5 8.47931L22 8.47931ZM20.1199 10.6987C21.2048 10.5179 22 9.5792 22 8.47931L20.5 8.47931C20.5 8.84594 20.2349 9.15883 19.8733 9.2191L20.1199 10.6987ZM15.4087 11.4839L20.1199 10.6987L19.8733 9.2191L15.1621 10.0043L15.4087 11.4839ZM15.5 16C15.5 14.2535 15.7107 12.5085 16.022 10.8854L14.5488 10.6028C14.2244 12.2946 14 14.1367 14 16L15.5 16ZM16.0219 21.1146C15.7107 19.4915 15.5 17.7465 15.5 16L14 16C14 17.8633 14.2243 19.7054 14.5488 21.3972L16.0219 21.1146ZM20.1199 21.3013L15.4087 20.5161L15.1621 21.9957L19.8733 22.7809L20.1199 21.3013ZM22 23.5207C22 22.4208 21.2048 21.4821 20.1199 21.3013L19.8733 22.7809C20.2349 22.8412 20.5 23.1541 20.5 23.5207L22 23.5207ZM22 27.75L22 23.5207L20.5 23.5207L20.5 27.75L22 27.75ZM19.75 30C20.9926 30 22 28.9926 22 27.75L20.5 27.75C20.5 28.1642 20.1642 28.5 19.75 28.5L19.75 30ZM17.75 30L19.75 30L19.75 28.5L17.75 28.5L17.75 30ZM15.3602 30L17.75 30L17.75 28.5L15.3602 28.5L15.3602 30ZM15.25 30L15.3602 30L15.3602 28.5L15.25 28.5L15.25 30ZM14.5 29.248L14.5 29.25L16 29.25L16 29.248L14.5 29.248ZM12.322 28.1367C12.8577 29.3197 14.0232 29.9531 15.2223 29.9975L15.2777 28.4985C14.5505 28.4716 13.952 28.1 13.6884 27.5179L12.322 28.1367ZM10 16C10 22.0095 11.395 26.0898 12.322 28.1367L13.6884 27.5179C12.8385 25.641 11.5 21.7708 11.5 16L10 16ZM12.322 3.86328C11.395 5.91023 10 9.99054 10 16L11.5 16C11.5 10.2292 12.8385 6.35896 13.6884 4.48207L12.322 3.86328ZM15.2223 2.00255C14.0232 2.0469 12.8577 2.68034 12.322 3.86327L13.6884 4.48207C13.952 3.90001 14.5505 3.52842 15.2777 3.50152L15.2223 2.00255ZM14.5 2.75L14.5 2.75203L16 2.75203L16 2.75L14.5 2.75Z", fill: color })));
};
exports.Telephone32 = Telephone32;
exports.default = exports.Telephone32;
//# sourceMappingURL=Telephone32.js.map