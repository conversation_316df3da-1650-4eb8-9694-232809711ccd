"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RotateCard32 = void 0;
var React = __importStar(require("react"));
var RotateCard32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M17.4452 2.04938C13.8684 1.28931 10.5 4.01711 10.5 7.6738V12.2497C10.5 12.6639 10.8358 12.9997 11.25 12.9997C11.6642 12.9997 12 12.6639 12 12.2497V7.6738C12 4.97103 14.4897 2.95483 17.1334 3.51662L27.1334 5.64162C29.0963 6.05874 30.5 7.79204 30.5 9.7988V22.2005C30.5 24.2073 29.0963 25.9406 27.1334 26.3577L17.1334 28.4827C14.4897 29.0445 12 27.0283 12 24.3255V19.9997C12 19.5854 11.6642 19.2497 11.25 19.2497C10.8358 19.2497 10.5 19.5854 10.5 19.9997V24.3255C10.5 27.9822 13.8684 30.71 17.4452 29.9499L27.4452 27.8249C30.1009 27.2606 32 24.9155 32 22.2005V9.7988C32 7.08377 30.1009 4.73873 27.4452 4.17438L17.4452 2.04938ZM19.5303 10.9693C19.2374 10.6764 18.7626 10.6764 18.4697 10.9693C18.1768 11.2621 18.1768 11.737 18.4697 12.0299L21.6893 15.2496H21.6732H21.6436H21.6138H21.5838H21.5537H21.5234H21.4928H21.4621H21.4313H21.4002H21.369H21.3375H21.3059H21.2741H21.2422H21.2101H21.1778H21.1453H21.1126H21.0798H21.0468H21.0137H20.9804H20.9469H20.9132H20.8794H20.8454H20.8113H20.777H20.7426H20.708H20.6732H20.6383H20.6032H20.568H20.5326H20.4971H20.4614H20.4256H20.3896H20.3535H20.3173H20.2809H20.2444H20.2077H20.1709H20.1339H20.0968H20.0596H20.0222H19.9847H19.9471H19.9094H19.8715H19.8335H19.7953H19.7571H19.7187H19.6802H19.6415H19.6028H19.5639H19.5249H19.4858H19.4465H19.4072H19.3677H19.3282H19.2885H19.2487H19.2088H19.1688H19.1286H19.0884H19.0481H19.0077H18.9671H18.9265H18.8857H18.8449H18.804H18.7629H18.7218H18.6806H18.6393H18.5979H18.5564H18.5148H18.4731H18.4314H18.3895H18.3476H18.3056H18.2635H18.2213H18.1791H18.1367H18.0943H18.0518H18.0093H17.9666H17.9239H17.8812H17.8383H17.7954H17.7524H17.7094H17.6662H17.6231H17.5798H17.5365H17.4931H17.4497H17.4062H17.3627H17.3191H17.2754H17.2317H17.1879H17.1441H17.1003H17.0564H17.0124H16.9684H16.9243H16.8802H16.8361H16.7919H16.7477H16.7034H16.6591H16.6148H16.5704H16.526H16.4816H16.4371H16.3926H16.3481H16.3035H16.2589H16.2143H16.1697H16.125H16.0803H16.0356H15.9909H15.9461H15.9014H15.8566H15.8118H15.7669H15.7221H15.6773H15.6324H15.5876H15.5427H15.4978H15.4529H15.408H15.3631H15.3182H15.2733H15.2284H15.1835H15.1387H15.0938H15.0489H15.004H14.9591H14.9142H14.8694H14.8245H14.7797H14.7349H14.6901H14.6453H14.6005H14.5557H14.511H14.4663H14.4216H14.3769H14.3322H14.2876H14.243H14.1984H14.1538H14.1093H14.0648H14.0204H13.9759H13.9315H13.8872H13.8429H13.7986H13.7543H13.7101H13.666H13.6218H13.5778H13.5337H13.4897H13.4458H13.4019H13.358H13.3143H13.2705H13.2268H13.1832H13.1396H13.0961H13.0526H13.0092H12.9659H12.9226H12.8793H12.8362H12.7931H12.7501H12.7071H12.6642H12.6214H12.5786H12.5359H12.4933H12.4508H12.4083H12.366H12.3237H12.2814H12.2393H12.1972H12.1552H12.1133H12.0715H12.0298H11.9882H11.9466H11.9052H11.8638H11.8225H11.7813H11.7402H11.6992H11.6583H11.6175H11.5768H11.5362H11.4957H11.4553H11.415H11.3749H11.3348H11.2948H11.2549H11.2152H11.1755H11.136H11.0966H11.0573H11.0181H10.979H10.9401H10.9012H10.8625H10.8239H10.7854H10.7471H10.7089H10.6708H10.6328H10.595H10.5573H10.5197H10.4822H10.4449H10.4078H10.3707H10.3338H10.297H10.2604H10.2239H10.1876H10.1514H10.1153H10.0794H10.0437H10.008H9.97257H9.93725H9.90208H9.86707H9.83221H9.7975H9.76295H9.72856H9.69433H9.66025H9.62634H9.59258H9.55899H9.52557H9.49231H9.45922H9.42629H9.39354H9.36095H9.32854H9.29629H9.26423H9.23233H9.20062H9.16908H9.13771H9.10653H9.07553H9.04471H9.01408H8.98363H8.95336H8.92328H8.89339H8.86369H8.83418H8.80486H8.77573H8.7468H8.71806H8.68952H8.66118H8.63303H8.60509H8.57734H8.5498H8.52246H8.49533H8.4684H8.44168H8.41516H8.38886H8.36276H8.33688H8.31121H8.28576H8.26052H8.23549H8.21069H8.1861H8.16173H8.13759H8.11366H8.08996H8.06649H8.04324H8.02021H7.99742H7.97485H7.95252H7.93042H7.90855H7.88691H7.86551H7.84435H7.82342H7.80274H7.78229H7.76208H7.74212H7.7224H7.70293H7.6837H7.66472H7.64599H7.6275H7.60927H7.59129H7.57356H7.55609H7.53887H7.52191H7.50521H7.48876H7.47258H7.45666H7.441H7.4256H7.41047H7.39561H7.38101H7.36668H7.35262H7.33883H7.32532H7.31207H7.29911H7.28641H7.274H7.26186H7.25C6.32531 15.2496 5.56407 15.5624 5.03217 16.0943C4.50732 16.6191 4.25 17.3141 4.25 17.9996C4.25 18.6851 4.5073 19.3801 5.03208 19.9051C5.56395 20.4371 6.3252 20.7501 7.25 20.7501C7.66421 20.7501 8 20.4143 8 20.0001C8 19.5859 7.66421 19.2501 7.25 19.2501C6.6748 19.2501 6.31105 19.0628 6.09292 18.8446C5.8677 18.6193 5.75 18.3141 5.75 17.9996C5.75 17.6851 5.86768 17.3801 6.09283 17.1549C6.31093 16.9368 6.67469 16.7496 7.25 16.7496H7.26186H7.274H7.28641H7.29911H7.31207H7.32532H7.33883H7.35262H7.36668H7.38101H7.39561H7.41047H7.4256H7.441H7.45666H7.47258H7.48876H7.50521H7.52191H7.53887H7.55609H7.57356H7.59129H7.60927H7.6275H7.64599H7.66472H7.6837H7.70293H7.7224H7.74212H7.76208H7.78229H7.80274H7.82342H7.84435H7.86551H7.88691H7.90855H7.93042H7.95252H7.97485H7.99742H8.02021H8.04324H8.06649H8.08996H8.11366H8.13759H8.16173H8.1861H8.21069H8.23549H8.26052H8.28576H8.31121H8.33688H8.36276H8.38886H8.41516H8.44168H8.4684H8.49533H8.52246H8.5498H8.57734H8.60509H8.63303H8.66118H8.68952H8.71806H8.7468H8.77573H8.80486H8.83418H8.86369H8.89339H8.92328H8.95336H8.98363H9.01408H9.04471H9.07553H9.10653H9.13771H9.16908H9.20062H9.23233H9.26423H9.29629H9.32854H9.36095H9.39354H9.42629H9.45922H9.49231H9.52557H9.55899H9.59258H9.62634H9.66025H9.69433H9.72856H9.76295H9.7975H9.83221H9.86707H9.90208H9.93725H9.97257H10.008H10.0437H10.0794H10.1153H10.1514H10.1876H10.2239H10.2604H10.297H10.3338H10.3707H10.4078H10.4449H10.4822H10.5197H10.5573H10.595H10.6328H10.6708H10.7089H10.7471H10.7854H10.8239H10.8625H10.9012H10.9401H10.979H11.0181H11.0573H11.0966H11.136H11.1755H11.2152H11.2549H11.2948H11.3348H11.3749H11.415H11.4553H11.4957H11.5362H11.5768H11.6175H11.6583H11.6992H11.7402H11.7813H11.8225H11.8638H11.9052H11.9466H11.9882H12.0298H12.0715H12.1133H12.1552H12.1972H12.2393H12.2814H12.3237H12.366H12.4083H12.4508H12.4933H12.5359H12.5786H12.6214H12.6642H12.7071H12.7501H12.7931H12.8362H12.8793H12.9226H12.9659H13.0092H13.0526H13.0961H13.1396H13.1832H13.2268H13.2705H13.3143H13.358H13.4019H13.4458H13.4897H13.5337H13.5778H13.6218H13.666H13.7101H13.7543H13.7986H13.8429H13.8872H13.9315H13.9759H14.0204H14.0648H14.1093H14.1538H14.1984H14.243H14.2876H14.3322H14.3769H14.4216H14.4663H14.511H14.5557H14.6005H14.6453H14.6901H14.7349H14.7797H14.8245H14.8694H14.9142H14.9591H15.004H15.0489H15.0938H15.1387H15.1835H15.2284H15.2733H15.3182H15.3631H15.408H15.4529H15.4978H15.5427H15.5876H15.6324H15.6773H15.7221H15.7669H15.8118H15.8566H15.9014H15.9461H15.9909H16.0356H16.0803H16.125H16.1697H16.2143H16.2589H16.3035H16.3481H16.3926H16.4371H16.4816H16.526H16.5704H16.6148H16.6591H16.7034H16.7477H16.7919H16.8361H16.8802H16.9243H16.9684H17.0124H17.0564H17.1003H17.1441H17.1879H17.2317H17.2754H17.3191H17.3627H17.4062H17.4497H17.4931H17.5365H17.5798H17.6231H17.6662H17.7094H17.7524H17.7954H17.8383H17.8812H17.9239H17.9666H18.0093H18.0518H18.0943H18.1367H18.1791H18.2213H18.2635H18.3056H18.3476H18.3895H18.4314H18.4731H18.5148H18.5564H18.5979H18.6393H18.6806H18.7218H18.7629H18.804H18.8449H18.8857H18.9265H18.9671H19.0077H19.0481H19.0884H19.1286H19.1688H19.2088H19.2487H19.2885H19.3282H19.3677H19.4072H19.4465H19.4858H19.5249H19.5639H19.6028H19.6415H19.6802H19.7187H19.7571H19.7953H19.8335H19.8715H19.9094H19.9471H19.9847H20.0222H20.0596H20.0968H20.1339H20.1709H20.2077H20.2444H20.2809H20.3173H20.3535H20.3896H20.4256H20.4614H20.4971H20.5326H20.568H20.6032H20.6383H20.6732H20.708H20.7426H20.777H20.8113H20.8454H20.8794H20.9132H20.9469H20.9804H21.0137H21.0468H21.0798H21.1126H21.1453H21.1778H21.2101H21.2422H21.2741H21.3059H21.3375H21.369H21.4002H21.4313H21.4621H21.4928H21.5234H21.5537H21.5838H21.6138H21.6436H21.6732H21.6893L18.4697 19.9693C18.1768 20.2621 18.1768 20.737 18.4697 21.0299C18.7626 21.3228 19.2374 21.3228 19.5303 21.0299L24.0303 16.5299C24.3232 16.237 24.3232 15.7621 24.0303 15.4693L19.5303 10.9693Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.RotateCard32 = RotateCard32;
exports.default = exports.RotateCard32;
//# sourceMappingURL=RotateCard32.js.map