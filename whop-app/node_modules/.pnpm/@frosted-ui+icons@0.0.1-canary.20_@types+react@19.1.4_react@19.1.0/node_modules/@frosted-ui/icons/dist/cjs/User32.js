"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User32 = void 0;
var React = __importStar(require("react"));
var User32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M16 2.5C12.9624 2.5 10.5 4.96243 10.5 8 10.5 11.0376 12.9624 13.5 16 13.5 19.0376 13.5 21.5 11.0376 21.5 8 21.5 4.96243 19.0376 2.5 16 2.5zM9 8C9 4.13401 12.134 1 16 1 19.866 1 23 4.13401 23 8 23 11.866 19.866 15 16 15 12.134 15 9 11.866 9 8zM16 18.5C9.64485 18.5 4.5 22.83 4.5 29.25 4.5 29.6642 4.16421 30 3.75 30 3.33579 30 3 29.6642 3 29.25 3 21.8629 8.96225 17 16 17 23.0378 17 29 21.8629 29 29.25 29 29.6642 28.6642 30 28.25 30 27.8358 30 27.5 29.6642 27.5 29.25 27.5 22.83 22.3552 18.5 16 18.5z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.User32 = User32;
exports.default = exports.User32;
//# sourceMappingURL=User32.js.map