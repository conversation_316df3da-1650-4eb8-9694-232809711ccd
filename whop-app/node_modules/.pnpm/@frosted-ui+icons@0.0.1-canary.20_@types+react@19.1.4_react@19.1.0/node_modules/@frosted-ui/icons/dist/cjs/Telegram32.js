"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Telegram32 = void 0;
var React = __importStar(require("react"));
var Telegram32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M3.92467 14.3923C11.4423 11.1283 16.454 8.97627 18.9597 7.9366C26.1229 4.9678 27.6094 4.45219 28.5803 4.43492C28.7938 4.43131 29.2692 4.48391 29.5794 4.73409C29.8372 4.94491 29.9098 5.23003 29.9461 5.43001C29.9783 5.62999 30.0226 6.08577 29.9865 6.44156C29.5997 10.5054 27.9197 20.3671 27.0656 24.9189C26.7071 26.845 25.9939 27.4907 25.305 27.5537C23.8063 27.6911 22.6702 26.5674 21.2198 25.62C18.9517 24.1371 17.6706 23.2142 15.4669 21.7675C12.9208 20.0953 14.5725 19.1761 16.0229 17.6743C16.4015 17.2812 23.0005 11.2997 23.1254 10.7572C23.1416 10.6894 23.1578 10.4364 23.0046 10.3031C22.8556 10.1693 22.6338 10.2151 22.4727 10.2512C22.243 10.3027 18.6213 12.6912 11.5952 17.4164C10.5679 18.1208 9.63724 18.4641 8.79926 18.4461C7.88072 18.4264 6.1082 17.9272 4.79081 17.5008C3.17932 16.9775 1.89409 16.7008 2.0069 15.8121C2.0633 15.3495 2.70397 14.8761 3.92467 14.3923Z", stroke: color, strokeWidth: "1.5" })));
};
exports.Telegram32 = Telegram32;
exports.default = exports.Telegram32;
//# sourceMappingURL=Telegram32.js.map