"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Telephone16 = void 0;
var React = __importStar(require("react"));
var Telephone16 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "16", height: "16", viewBox: "0 0 16 16", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M10.37 0.75L10.37 -3.8466e-08L10.37 0.75ZM8.75002 0.75L8.75002 1.5L8.75002 0.75ZM11.25 1.63L12 1.63L11.25 1.63ZM11.25 3.78859L10.5 3.78859L11.25 3.78859ZM10.5544 4.64906L10.3972 3.91571L10.3972 3.91571L10.5544 4.64906ZM7.88268 5.22157L7.72553 4.48822C7.40608 4.55667 7.16769 4.82405 7.13618 5.14923L7.88268 5.22157ZM7.75002 8L8.50002 8L7.75002 8ZM7.88266 10.7784L7.13616 10.8508C7.16767 11.1759 7.40606 11.4433 7.72552 11.5118L7.88266 10.7784ZM10.5544 11.3509L10.3972 12.0843L10.3972 12.0843L10.5544 11.3509ZM6.37755 14.6809L5.72997 15.0592L6.37755 14.6809ZM6.37755 1.31909L5.72997 0.940764L5.72997 0.940764L6.37755 1.31909ZM10.37 -3.8466e-08L8.75002 -1.09278e-07L8.75002 1.5L10.37 1.5L10.37 -3.8466e-08ZM12 1.63C12 0.729776 11.2702 8.84026e-10 10.37 -3.8466e-08L10.37 1.5C10.4418 1.5 10.5 1.5582 10.5 1.63L12 1.63ZM12 3.78859L12 1.63L10.5 1.63L10.5 3.78859L12 3.78859ZM10.7115 5.38241C11.4631 5.22137 12 4.5572 12 3.78859L10.5 3.78859C10.5 3.84989 10.4572 3.90286 10.3972 3.91571L10.7115 5.38241ZM8.03983 5.95492L10.7115 5.38241L10.3972 3.91571L7.72553 4.48822L8.03983 5.95492ZM8.50002 8C8.50002 7.00853 8.55087 6.10204 8.62918 5.29391L7.13618 5.14923C7.0535 6.00244 7.00002 6.95729 7.00002 8L8.50002 8ZM8.62917 10.7061C8.55086 9.89796 8.50001 8.99147 8.50002 8L7.00002 8C7.00001 9.0427 7.05348 9.99755 7.13616 10.8508L8.62917 10.7061ZM7.72552 11.5118L10.3972 12.0843L10.7115 10.6176L8.03981 10.0451L7.72552 11.5118ZM10.3972 12.0843C10.4572 12.0971 10.5 12.1501 10.5 12.2114L12 12.2114C12 11.4428 11.4631 10.7786 10.7115 10.6176L10.3972 12.0843ZM10.5 12.2114L10.5 14.37L12 14.37L12 12.2114L10.5 12.2114ZM10.5 14.37C10.5 14.4418 10.4418 14.5 10.37 14.5L10.37 16C11.2702 16 12 15.2702 12 14.37L10.5 14.37ZM10.37 14.5L8.75002 14.5L8.75002 16L10.37 16L10.37 14.5ZM8.75002 14.5L8.25 14.5L8.25 16L8.75002 16L8.75002 14.5ZM7.43164 16L8.25 16L8.25 14.5L7.43164 14.5L7.43164 16ZM5.72997 15.0592C6.10287 15.6975 6.77943 16 7.43164 16L7.43164 14.5C7.22269 14.5 7.08664 14.4079 7.02514 14.3026L5.72997 15.0592ZM4.00002 8C4.00002 11.5819 5.0987 13.9787 5.72997 15.0592L7.02514 14.3026C6.49923 13.4024 5.50002 11.2642 5.50002 8L4.00002 8ZM5.72997 0.940764C5.0987 2.0213 4.00002 4.41812 4.00002 8L5.50002 8C5.50002 4.73581 6.49923 2.59762 7.02514 1.69742L5.72997 0.940764ZM7.43164 -1.66906e-07C6.77944 -1.95415e-07 6.10287 0.302473 5.72997 0.940764L7.02514 1.69742C7.08664 1.59215 7.22269 1.5 7.43164 1.5L7.43164 -1.66906e-07ZM8.25 -1.31134e-07L7.43164 -1.66906e-07L7.43164 1.5L8.25 1.5L8.25 -1.31134e-07ZM8.75002 -1.09278e-07L8.25 -1.31134e-07L8.25 1.5L8.75002 1.5L8.75002 -1.09278e-07Z", fill: color })));
};
exports.Telephone16 = Telephone16;
exports.default = exports.Telephone16;
//# sourceMappingURL=Telephone16.js.map