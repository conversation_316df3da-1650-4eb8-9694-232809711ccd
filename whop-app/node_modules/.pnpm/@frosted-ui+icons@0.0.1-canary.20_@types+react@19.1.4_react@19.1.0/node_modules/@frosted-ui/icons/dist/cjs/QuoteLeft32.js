"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuoteLeft32 = void 0;
var React = __importStar(require("react"));
var QuoteLeft32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M23.3468 25.3335C26.629 25.3335 29.3334 22.7332 29.3334 19.4724 29.3334 16.2115 26.629 13.6112 23.3468 13.6112 22.0948 13.6112 20.9268 13.9896 19.9611 14.6387 20.2327 13.9201 20.5834 13.2921 20.9951 12.7245 22.1636 11.1141 23.8812 9.90096 26.0288 8.50525 26.4919 8.20429 26.6234 7.58492 26.3224 7.12184 26.0214 6.65876 25.402 6.52733 24.939 6.82829 22.8124 8.21036 20.7902 9.60138 19.3763 11.55 17.9316 13.5413 17.1774 16.0276 17.3604 19.5003 17.3758 22.748 20.0742 25.3335 23.3468 25.3335zM8.68009 25.3335C11.9622 25.3335 14.6666 22.7332 14.6666 19.4724 14.6666 16.2115 11.9622 13.6112 8.68009 13.6112 7.42809 13.6112 6.26014 13.9896 5.29433 14.6387 5.5659 13.9201 5.91658 13.2921 6.32841 12.7245 7.49686 11.1141 9.21453 9.90096 11.3621 8.50525 11.8252 8.20429 11.9566 7.58492 11.6556 7.12184 11.3547 6.65876 10.7353 6.52733 10.2722 6.82829 8.14565 8.21036 6.12346 9.60138 4.70962 11.55 3.26484 13.5413 2.51061 16.0276 2.69368 19.5003 2.70904 22.748 5.40737 25.3335 8.68009 25.3335z", fill: color })));
};
exports.QuoteLeft32 = QuoteLeft32;
exports.default = exports.QuoteLeft32;
//# sourceMappingURL=QuoteLeft32.js.map