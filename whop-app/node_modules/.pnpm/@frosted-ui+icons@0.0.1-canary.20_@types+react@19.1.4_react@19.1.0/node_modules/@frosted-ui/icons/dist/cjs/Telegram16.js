"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Telegram16 = void 0;
var React = __importStar(require("react"));
var Telegram16 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "16", height: "16", viewBox: "0 0 16 16", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M2.82486 7.31097C6.04668 5.91211 8.19456 4.98982 9.26845 4.54425C12.3384 3.27191 12.9755 3.05093 13.3915 3.04353C13.4831 3.04198 13.6868 3.06452 13.8197 3.17174C13.9302 3.2621 13.9613 3.38429 13.9769 3.46999C13.9907 3.5557 14.0097 3.75103 13.9942 3.90352C13.8285 5.64518 13.1085 9.87161 12.7424 11.8224C12.5887 12.6478 12.2831 12.9246 11.9879 12.9516C11.3455 13.0104 10.8587 12.5289 10.2371 12.1229C9.26501 11.4873 8.71598 11.0918 7.77153 10.4718C6.68033 9.75511 7.38821 9.3612 8.00981 8.71753C8.17207 8.54906 11.0002 5.9856 11.0537 5.75309C11.0607 5.724 11.0676 5.61558 11.002 5.55844C10.9381 5.50113 10.8431 5.52075 10.774 5.53624C10.6756 5.55827 9.12341 6.58192 6.11222 8.60702C5.67194 8.9089 5.2731 9.05603 4.91397 9.04831C4.52031 9.03986 3.76066 8.82594 3.19606 8.64318C2.50542 8.41893 1.95461 8.30034 2.00296 7.91947C2.02713 7.72121 2.3017 7.51831 2.82486 7.31097Z", stroke: color, strokeWidth: "1.25" })));
};
exports.Telegram16 = Telegram16;
exports.default = exports.Telegram16;
//# sourceMappingURL=Telegram16.js.map