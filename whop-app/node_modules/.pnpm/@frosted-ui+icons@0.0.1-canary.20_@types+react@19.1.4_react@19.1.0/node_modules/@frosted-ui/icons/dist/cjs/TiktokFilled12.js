"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TiktokFilled12 = void 0;
var React = __importStar(require("react"));
var TiktokFilled12 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "12", height: "12", viewBox: "0 0 12 12", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M10.3576 5.1067C9.46785 5.1067 8.644 4.82398 7.97125 4.34349V7.8363C7.97125 9.58355 6.5541 11 4.80601 11C4.15376 11 3.54752 10.8029 3.04392 10.4649C2.19775 9.89705 1.64062 8.9315 1.64062 7.8363C1.64062 6.08915 3.05781 4.67271 4.80606 4.67275C4.95134 4.67268 5.09645 4.68255 5.24035 4.70221V5.09L5.2403 6.452C5.10175 6.40805 4.95408 6.3842 4.80086 6.3842C4.00114 6.3842 3.35297 7.03215 3.35297 7.83135C3.35297 8.3964 3.67697 8.8857 4.14944 9.12405C4.34534 9.2228 4.56658 9.27845 4.80087 9.27845C5.59895 9.27845 6.246 8.63315 6.24875 7.8363V1H7.97125V1.22014C7.9773 1.28596 7.98605 1.35152 7.9975 1.41666C8.117 2.09818 8.52475 2.68047 9.09065 3.03343C9.47065 3.27052 9.9098 3.39585 10.3577 3.39517L10.3576 5.1067Z", fill: color })));
};
exports.TiktokFilled12 = TiktokFilled12;
exports.default = exports.TiktokFilled12;
//# sourceMappingURL=TiktokFilled12.js.map