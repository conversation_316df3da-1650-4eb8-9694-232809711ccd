"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SealCheckmark24 = void 0;
var React = __importStar(require("react"));
var SealCheckmark24 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "24", height: "24", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("g", { clipPath: "url(#clip0_882_1054)" },
            React.createElement("path", { d: "M20.7325 8.38291L20.0025 8.21085C19.9285 8.52487 20.0636 8.85105 20.338 9.02076L20.7325 8.38291ZM15.6171 3.26754L14.9793 3.66206C15.149 3.93645 15.4751 4.07155 15.7892 3.99753L15.6171 3.26754ZM8.38287 3.26754L8.21081 3.99753C8.52484 4.07155 8.85101 3.93645 9.02072 3.66206L8.38287 3.26754ZM4.39858 4.39863L4.92891 4.92896V4.92896L4.39858 4.39863ZM3.2675 8.38292L3.66202 9.02077C3.93641 8.85105 4.07151 8.52488 3.99749 8.21086L3.2675 8.38292ZM3.26751 15.6171L3.99751 15.7892C4.07153 15.4752 3.93642 15.149 3.66203 14.9793L3.26751 15.6171ZM4.3986 19.6014L3.86827 20.1318H3.86827L4.3986 19.6014ZM8.38288 20.7325L9.02073 20.338C8.85102 20.0636 8.52485 19.9285 8.21082 20.0025L8.38288 20.7325ZM15.6171 20.7325L15.7892 20.0025C15.4751 19.9285 15.149 20.0636 14.9793 20.338L15.6171 20.7325ZM19.6014 19.6014L20.1317 20.1318L20.1317 20.1318L19.6014 19.6014ZM20.7325 15.6171L20.3379 14.9793C20.0636 15.149 19.9285 15.4752 20.0025 15.7892L20.7325 15.6171ZM20.338 9.02076C21.3371 9.63872 22 10.7423 22 12H23.5C23.5 10.2012 22.5495 8.62492 21.127 7.74506L20.338 9.02076ZM19.0711 4.92895C19.9604 5.81833 20.272 7.06742 20.0025 8.21085L21.4625 8.55497C21.8462 6.92693 21.4037 5.14028 20.1317 3.86829L19.0711 4.92895ZM15.7892 3.99753C16.9326 3.72802 18.1817 4.03958 19.0711 4.92895L20.1317 3.86829C18.8597 2.5963 17.0731 2.15381 15.445 2.53754L15.7892 3.99753ZM12 2.00002C13.2577 2.00002 14.3613 2.66296 14.9793 3.66206L16.255 2.87302C15.3751 1.45048 13.7989 0.500023 12 0.500023V2.00002ZM9.02072 3.66206C9.63868 2.66296 10.7422 2.00002 12 2.00002V0.500023C10.2011 0.500023 8.62488 1.45048 7.74502 2.87302L9.02072 3.66206ZM4.92891 4.92896C5.81829 4.03958 7.06738 3.72803 8.21081 3.99753L8.55493 2.53754C6.92689 2.15381 5.14024 2.59631 3.86825 3.8683L4.92891 4.92896ZM3.99749 8.21086C3.72798 7.06743 4.03954 5.81833 4.92891 4.92896L3.86825 3.8683C2.59626 5.14028 2.15377 6.92694 2.5375 8.55498L3.99749 8.21086ZM2 12C2 10.7423 2.66293 9.63872 3.66202 9.02077L2.87297 7.74507C1.45045 8.62493 0.5 10.2012 0.5 12H2ZM3.66203 14.9793C2.66294 14.3613 2 13.2578 2 12H0.5C0.5 13.7989 1.45045 15.3751 2.87299 16.255L3.66203 14.9793ZM4.92893 19.0711C4.03956 18.1817 3.728 16.9326 3.99751 15.7892L2.53752 15.4451C2.15379 17.0731 2.59629 18.8598 3.86827 20.1318L4.92893 19.0711ZM8.21082 20.0025C7.06739 20.272 5.8183 19.9605 4.92893 19.0711L3.86827 20.1318C5.14026 21.4037 6.9269 21.8462 8.55494 21.4625L8.21082 20.0025ZM12 22C10.7422 22 9.63869 21.3371 9.02073 20.338L7.74503 21.127C8.62489 22.5496 10.2011 23.5 12 23.5V22ZM14.9793 20.338C14.3613 21.3371 13.2577 22 12 22V23.5C13.7989 23.5 15.3751 22.5496 16.255 21.127L14.9793 20.338ZM19.0711 19.0711C18.1817 19.9605 16.9326 20.272 15.7892 20.0025L15.445 21.4625C17.0731 21.8462 18.8597 21.4037 20.1317 20.1318L19.0711 19.0711ZM20.0025 15.7892C20.272 16.9326 19.9604 18.1817 19.071 19.0711L20.1317 20.1318C21.4037 18.8598 21.8462 17.0731 21.4625 15.4451L20.0025 15.7892ZM22 12C22 13.2578 21.3371 14.3613 20.3379 14.9793L21.127 16.255C22.5495 15.3751 23.5 13.7989 23.5 12H22Z", fill: color }),
            React.createElement("path", { d: "M7.5 12L10.5 15.1875L16.5 9", stroke: color, strokeWidth: "1.5", strokeLinecap: "round", strokeLinejoin: "round" })),
        React.createElement("defs", null,
            React.createElement("clipPath", { id: "clip0_882_1054" },
                React.createElement("path", { fill: color, d: "M0 0H24V24H0z" })))));
};
exports.SealCheckmark24 = SealCheckmark24;
exports.default = exports.SealCheckmark24;
//# sourceMappingURL=SealCheckmark24.js.map