"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwitterFilled12 = void 0;
var React = __importStar(require("react"));
var TwitterFilled12 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "12", height: "12", viewBox: "0 0 12 12", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M11.625 2.56562C11.2113 2.74607 10.7669 2.868 10.2997 2.92311C10.7762 2.64219 11.1419 2.19643 11.3144 1.66582C10.8685 1.92625 10.3744 2.11547 9.84875 2.21692C9.4276 1.77604 8.8281 1.5 8.1642 1.5C6.88985 1.5 5.8563 2.51734 5.8563 3.77268C5.8563 3.9502 5.87715 4.12284 5.91575 4.28964C3.99684 4.19454 2.2969 3.28986 1.15833 1.91552C0.960145 2.25203 0.846185 2.64219 0.846185 3.05771C0.846185 3.84583 1.25395 4.54178 1.87278 4.94901C1.49425 4.93681 1.13851 4.83489 0.82736 4.66468V4.69394C0.82736 5.7947 1.62258 6.713 2.67891 6.92175C2.48468 6.97345 2.28105 7.00075 2.07097 7.00075C1.92234 7.00075 1.77716 6.9871 1.63645 6.96075C1.93026 7.863 2.78246 8.52045 3.79271 8.538C3.00244 9.1476 2.00706 9.51095 0.925955 9.51095C0.73966 9.51095 0.555845 9.5002 0.375 9.47925C1.39664 10.1244 2.60954 10.5 3.91261 10.5C8.15875 10.5 10.48 7.0383 10.48 4.03604C10.48 3.9385 10.4775 3.83998 10.4731 3.74244C10.9239 3.42154 11.3153 3.02211 11.624 2.56709L11.625 2.56562Z", fill: color })));
};
exports.TwitterFilled12 = TwitterFilled12;
exports.default = exports.TwitterFilled12;
//# sourceMappingURL=TwitterFilled12.js.map