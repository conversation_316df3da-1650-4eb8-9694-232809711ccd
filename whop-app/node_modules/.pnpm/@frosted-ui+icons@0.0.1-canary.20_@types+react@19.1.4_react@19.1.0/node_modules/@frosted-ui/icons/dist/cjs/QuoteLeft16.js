"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuoteLeft16 = void 0;
var React = __importStar(require("react"));
var QuoteLeft16 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "16", height: "16", viewBox: "0 0 16 16", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M11.6734 12.6667C13.3144 12.6667 14.6666 11.3666 14.6666 9.73621 14.6666 8.10574 13.3144 6.80561 11.6734 6.80561 11.0474 6.80561 10.4634 6.99481 9.98048 7.31934 10.1163 6.96008 10.2916 6.64608 10.4975 6.36228 11.0818 5.55707 11.9406 4.9505 13.0144 4.25264 13.2459 4.10216 13.3116 3.79248 13.1612 3.56094 13.0106 3.3294 12.701 3.26368 12.4694 3.41416 11.4062 4.1052 10.395 4.8007 9.68808 5.77501 8.96575 6.77068 8.58862 8.01381 8.68015 9.75014 8.68782 11.374 10.037 12.6667 11.6734 12.6667zM4.34011 12.6667C5.98115 12.6667 7.33337 11.3666 7.33337 9.73621 7.33337 8.10574 5.98115 6.80561 4.34011 6.80561 3.71411 6.80561 3.13013 6.99481 2.64723 7.31934 2.78301 6.96008 2.95835 6.64608 3.16427 6.36228 3.74849 5.55707 4.60733 4.9505 5.68111 4.25264 5.91265 4.10216 5.97836 3.79248 5.82788 3.56094 5.6774 3.3294 5.36771 3.26368 5.13617 3.41416 4.07289 4.1052 3.06179 4.8007 2.35487 5.77501 1.63248 6.77068 1.25537 8.01381 1.3469 9.75014 1.35458 11.374 2.70375 12.6667 4.34011 12.6667z", fill: color })));
};
exports.QuoteLeft16 = QuoteLeft16;
exports.default = exports.QuoteLeft16;
//# sourceMappingURL=QuoteLeft16.js.map