"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RotateCard16 = void 0;
var React = __importStar(require("react"));
var RotateCard16 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "16", height: "16", viewBox: "0 0 16 16", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M8.91697 0.393328C7.18132 -0.0405854 5.5 1.27215 5.5 3.06122V4.23875C5.5 4.65297 5.83579 4.98875 6.25 4.98875C6.66421 4.98875 7 4.65297 7 4.23875V3.06122C7 2.24801 7.76424 1.65131 8.55317 1.84854L13.5532 3.09854C14.1096 3.23766 14.5 3.73764 14.5 4.31122V11.6881C14.5 12.2617 14.1096 12.7617 13.5532 12.9008L8.55317 14.1508C7.76424 14.348 7 13.7513 7 12.9381V12C7 11.5858 6.66421 11.25 6.25 11.25C5.83579 11.25 5.5 11.5858 5.5 12V12.9381C5.5 14.7272 7.18132 16.0399 8.91697 15.606L13.917 14.356C15.1412 14.05 16 12.95 16 11.6881V4.31122C16 3.04933 15.1412 1.94938 13.917 1.64333L8.91697 0.393328ZM10.2803 4.9693C9.98744 4.67641 9.51256 4.67641 9.21967 4.9693C8.92678 5.26219 8.92678 5.73707 9.21967 6.02996L10.4393 7.24963H10.4292H10.4095H10.3897H10.3698H10.3499H10.3298H10.3097H10.2896H10.2693H10.249H10.2286H10.2082H10.1877H10.1671H10.1464H10.1257H10.1049H10.0841H10.0632H10.0422H10.0211H10H9.97887H9.95764H9.93635H9.915H9.89359H9.87212H9.85059H9.829H9.80735H9.78564H9.76388H9.74206H9.72019H9.69826H9.67627H9.65423H9.63214H9.60999H9.5878H9.56555H9.54324H9.52089H9.49849H9.47604H9.45354H9.43099H9.40839H9.38574H9.36305H9.34031H9.31753H9.2947H9.27182H9.2489H9.22594H9.20294H9.17989H9.1568H9.13367H9.1105H9.08728H9.06403H9.04074H9.01741H8.99405H8.97064H8.9472H8.92372H8.90021H8.87666H8.85308H8.82946H8.80581H8.78212H8.75841H8.73466H8.71088H8.68707H8.66323H8.63936H8.61546H8.59153H8.56758H8.5436H8.51959H8.49555H8.47149H8.4474H8.42329H8.39916H8.375H8.35082H8.32662H8.30239H8.27814H8.25388H8.22959H8.20528H8.18096H8.15661H8.13225H8.10787H8.08347H8.05906H8.03463H8.01019H7.98573H7.96126H7.93678H7.91228H7.88777H7.86325H7.83871H7.81417H7.78962H7.76505H7.74048H7.7159H7.69131H7.66672H7.64212H7.61751H7.5929H7.56828H7.54365H7.51903H7.4944H7.46976H7.44513H7.42049H7.39585H7.37121H7.34658H7.32194H7.2973H7.27267H7.24803H7.2234H7.19878H7.17415H7.14954H7.12492H7.10031H7.07571H7.05112H7.02653H7.00195H6.97738H6.95282H6.92827H6.90372H6.87919H6.85467H6.83016H6.80566H6.78118H6.75671H6.73225H6.70781H6.68338H6.65897H6.63458H6.6102H6.58584H6.5615H6.53717H6.51287H6.48858H6.46432H6.44007H6.41585H6.39165H6.36747H6.34331H6.31918H6.29507H6.27098H6.24692H6.22289H6.19888H6.1749H6.15095H6.12703H6.10313H6.07926H6.05542H6.03162H6.00784H5.98409H5.96038H5.9367H5.91305H5.88944H5.86586H5.84231H5.8188H5.79532H5.77189H5.74848H5.72512H5.70179H5.67851H5.65526H5.63205H5.60888H5.58575H5.56267H5.53962H5.51662H5.49366H5.47075H5.44788H5.42505H5.40227H5.37953H5.35684H5.3342H5.31161H5.28906H5.26656H5.24412H5.22172H5.19937H5.17707H5.15483H5.13263H5.11049H5.0884H5.06637H5.04439H5.02246H5.00059H4.97878H4.95702H4.93532H4.91367H4.89209H4.87056H4.8491H4.82769H4.80634H4.78505H4.76383H4.74267H4.72157H4.70053H4.67956H4.65865H4.6378H4.61703H4.59631H4.57567H4.55509H4.53458H4.51413H4.49376H4.47345H4.45322H4.43305H4.41296H4.39294H4.37299H4.35311H4.33331H4.31358H4.29392H4.27434H4.25483H4.2354H4.21605H4.19677H4.17757H4.15845H4.13941H4.12045H4.10157H4.08277H4.06405H4.04541H4.02685H4.00838H3.98999H3.97168H3.95346H3.93532H3.91727H3.8993H3.88142H3.86363H3.84593H3.82831H3.81078H3.79335H3.776H3.75874H3.74157H3.7245H3.70752H3.69063H3.67383H3.65713H3.64052H3.62401H3.60759H3.59127H3.57505H3.55892H3.54289H3.52696H3.51113H3.4954H3.47976H3.46423H3.4488H3.43347H3.41825H3.40312H3.3881H3.37318H3.35837H3.34367H3.32906H3.31457H3.30018H3.2859H3.27173H3.25766H3.24371H3.22986H3.21612H3.2025H3.18899H3.17558H3.16229H3.14912H3.13605H3.1231H3.11027H3.09755H3.08494H3.07246H3.06008H3.04783H3.03569H3.02368H3.01178H3C2.07531 7.24963 1.31407 7.5624 0.78217 8.0943C0.257323 8.61915 0 9.31411 0 9.99963C0 10.6851 0.257302 11.3802 0.782083 11.9051C1.31395 12.4372 2.0752 12.7501 3 12.7501C3.41421 12.7501 3.75 12.4143 3.75 12.0001C3.75 11.5859 3.41421 11.2501 3 11.2501C2.4248 11.2501 2.06105 11.0628 1.84292 10.8446C1.6177 10.6193 1.5 10.3141 1.5 9.99963C1.5 9.68515 1.61768 9.38011 1.84283 9.15496C2.06093 8.93686 2.42469 8.74963 3 8.74963H3.01178H3.02368H3.03569H3.04783H3.06008H3.07246H3.08494H3.09755H3.11027H3.1231H3.13605H3.14912H3.16229H3.17558H3.18899H3.2025H3.21612H3.22986H3.24371H3.25766H3.27173H3.2859H3.30018H3.31457H3.32906H3.34367H3.35837H3.37318H3.3881H3.40312H3.41825H3.43347H3.4488H3.46423H3.47976H3.4954H3.51113H3.52696H3.54289H3.55892H3.57505H3.59127H3.60759H3.62401H3.64052H3.65713H3.67383H3.69063H3.70752H3.7245H3.74157H3.75874H3.776H3.79335H3.81078H3.82831H3.84593H3.86363H3.88142H3.8993H3.91727H3.93532H3.95346H3.97168H3.98999H4.00838H4.02685H4.04541H4.06405H4.08277H4.10157H4.12045H4.13941H4.15845H4.17757H4.19677H4.21605H4.2354H4.25483H4.27434H4.29392H4.31358H4.33331H4.35311H4.37299H4.39294H4.41296H4.43305H4.45322H4.47345H4.49376H4.51413H4.53458H4.55509H4.57567H4.59631H4.61703H4.6378H4.65865H4.67956H4.70053H4.72157H4.74267H4.76383H4.78505H4.80634H4.82769H4.8491H4.87056H4.89209H4.91367H4.93532H4.95702H4.97878H5.00059H5.02246H5.04439H5.06637H5.0884H5.11049H5.13263H5.15483H5.17707H5.19937H5.22172H5.24412H5.26656H5.28906H5.31161H5.3342H5.35684H5.37953H5.40227H5.42505H5.44788H5.47075H5.49366H5.51662H5.53962H5.56267H5.58575H5.60888H5.63205H5.65526H5.67851H5.70179H5.72512H5.74848H5.77189H5.79532H5.8188H5.84231H5.86586H5.88944H5.91305H5.9367H5.96038H5.98409H6.00784H6.03162H6.05542H6.07926H6.10313H6.12703H6.15095H6.1749H6.19888H6.22289H6.24692H6.27098H6.29507H6.31918H6.34331H6.36747H6.39165H6.41585H6.44007H6.46432H6.48858H6.51287H6.53717H6.5615H6.58584H6.6102H6.63458H6.65897H6.68338H6.70781H6.73225H6.75671H6.78118H6.80566H6.83016H6.85467H6.87919H6.90372H6.92827H6.95282H6.97738H7.00195H7.02653H7.05112H7.07571H7.10031H7.12492H7.14954H7.17415H7.19878H7.2234H7.24803H7.27267H7.2973H7.32194H7.34658H7.37121H7.39585H7.42049H7.44513H7.46976H7.4944H7.51903H7.54365H7.56828H7.5929H7.61751H7.64212H7.66672H7.69131H7.7159H7.74048H7.76505H7.78962H7.81417H7.83871H7.86325H7.88777H7.91228H7.93678H7.96126H7.98573H8.01019H8.03463H8.05906H8.08347H8.10787H8.13225H8.15661H8.18096H8.20528H8.22959H8.25388H8.27814H8.30239H8.32662H8.35082H8.375H8.39916H8.42329H8.4474H8.47149H8.49555H8.51959H8.5436H8.56758H8.59153H8.61546H8.63936H8.66323H8.68707H8.71088H8.73466H8.75841H8.78212H8.80581H8.82946H8.85308H8.87666H8.90021H8.92372H8.9472H8.97064H8.99405H9.01741H9.04074H9.06403H9.08728H9.1105H9.13367H9.1568H9.17989H9.20294H9.22594H9.2489H9.27182H9.2947H9.31753H9.34031H9.36305H9.38574H9.40839H9.43099H9.45354H9.47604H9.49849H9.52089H9.54324H9.56555H9.5878H9.60999H9.63214H9.65423H9.67627H9.69826H9.72019H9.74206H9.76388H9.78564H9.80735H9.829H9.85059H9.87212H9.89359H9.915H9.93635H9.95764H9.97887H10H10.0211H10.0422H10.0632H10.0841H10.1049H10.1257H10.1464H10.1671H10.1877H10.2082H10.2286H10.249H10.2693H10.2896H10.3097H10.3298H10.3499H10.3698H10.3897H10.4095H10.4292H10.4393L9.21967 9.9693C8.92678 10.2622 8.92678 10.7371 9.21967 11.03C9.51256 11.3229 9.98744 11.3229 10.2803 11.03L12.7803 8.52996C13.0732 8.23707 13.0732 7.76219 12.7803 7.4693L10.2803 4.9693Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.RotateCard16 = RotateCard16;
exports.default = exports.RotateCard16;
//# sourceMappingURL=RotateCard16.js.map