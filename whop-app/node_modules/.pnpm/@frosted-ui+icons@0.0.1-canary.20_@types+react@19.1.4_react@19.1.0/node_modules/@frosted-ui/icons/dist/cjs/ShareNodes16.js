"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareNodes16 = void 0;
var React = __importStar(require("react"));
var ShareNodes16 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "16", height: "16", viewBox: "0 0 16 16", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M12.5 0.5C10.8431 0.5 9.5 1.84315 9.5 3.5C9.5 3.75002 9.53059 3.99291 9.58822 4.2251L5.69528 5.9553C5.14761 5.36755 4.36673 5 3.5 5C1.84315 5 0.5 6.34314 0.5 8C0.5 9.65685 1.84315 11 3.5 11C4.36675 11 5.14764 10.6324 5.69531 10.0447L9.58823 11.7749C9.53059 12.0071 9.5 12.25 9.5 12.5C9.5 14.1569 10.8431 15.5 12.5 15.5C14.1569 15.5 15.5 14.1569 15.5 12.5C15.5 10.8431 14.1569 9.5 12.5 9.5C11.6345 9.5 10.8546 9.86653 10.307 10.4528L6.41256 8.72197C6.46969 8.49072 6.5 8.2489 6.5 8C6.5 7.75108 6.46969 7.50925 6.41255 7.27799L10.307 5.54713C10.8545 6.13346 11.6345 6.5 12.5 6.5C14.1569 6.5 15.5 5.15685 15.5 3.5C15.5 1.84315 14.1569 0.5 12.5 0.5ZM11 3.5C11 2.67157 11.6716 2 12.5 2C13.3284 2 14 2.67157 14 3.5C14 4.32843 13.3284 5 12.5 5C11.6716 5 11 4.32843 11 3.5ZM2 8C2 7.17157 2.67157 6.5 3.5 6.5C4.32843 6.5 5 7.17157 5 8C5 8.82842 4.32843 9.5 3.5 9.5C2.67157 9.5 2 8.82842 2 8ZM12.5 11C11.6716 11 11 11.6716 11 12.5C11 13.3284 11.6716 14 12.5 14C13.3284 14 14 13.3284 14 12.5C14 11.6716 13.3284 11 12.5 11Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.ShareNodes16 = ShareNodes16;
exports.default = exports.ShareNodes16;
//# sourceMappingURL=ShareNodes16.js.map