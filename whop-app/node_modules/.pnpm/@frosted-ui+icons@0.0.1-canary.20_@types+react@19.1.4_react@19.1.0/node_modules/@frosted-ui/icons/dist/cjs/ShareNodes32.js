"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareNodes32 = void 0;
var React = __importStar(require("react"));
var ShareNodes32 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "32", height: "32", viewBox: "0 0 32 32", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("path", { d: "M24.5 3C21.7386 3 19.5 5.23858 19.5 8C19.5 8.45795 19.5616 8.90152 19.6769 9.32288L11.4759 12.9677C10.5623 11.7717 9.12129 11 7.5 11C4.73858 11 2.5 13.2386 2.5 16C2.5 18.7614 4.73858 21 7.5 21C9.12129 21 10.5623 20.2283 11.4759 19.0323L19.6769 22.6771C19.5616 23.0985 19.5 23.5421 19.5 24C19.5 26.7614 21.7386 29 24.5 29C27.2614 29 29.5 26.7614 29.5 24C29.5 21.2386 27.2614 19 24.5 19C22.7303 19 21.1753 19.9194 20.2867 21.3067L12.1991 17.7122C12.3938 17.1781 12.5 16.6014 12.5 16C12.5 15.3986 12.3938 14.8219 12.1991 14.2878L20.2867 10.6933C21.1753 12.0806 22.7303 13 24.5 13C27.2614 13 29.5 10.7614 29.5 8C29.5 5.23858 27.2614 3 24.5 3ZM21 8C21 6.067 22.567 4.5 24.5 4.5C26.433 4.5 28 6.067 28 8C28 9.933 26.433 11.5 24.5 11.5C22.567 11.5 21 9.933 21 8ZM4 16C4 14.067 5.567 12.5 7.5 12.5C9.433 12.5 11 14.067 11 16C11 17.933 9.433 19.5 7.5 19.5C5.567 19.5 4 17.933 4 16ZM24.5 20.5C22.567 20.5 21 22.067 21 24C21 25.933 22.567 27.5 24.5 27.5C26.433 27.5 28 25.933 28 24C28 22.067 26.433 20.5 24.5 20.5Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" })));
};
exports.ShareNodes32 = ShareNodes32;
exports.default = exports.ShareNodes32;
//# sourceMappingURL=ShareNodes32.js.map