"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SealCheckmarkFilled20 = void 0;
var React = __importStar(require("react"));
var SealCheckmarkFilled20 = function (_a) {
    var _b = _a.color, color = _b === void 0 ? 'currentColor' : _b, props = __rest(_a, ["color"]);
    return (React.createElement("svg", __assign({ width: "20", height: "20", viewBox: "0 0 20 20", fill: "none", xmlns: "http://www.w3.org/2000/svg", "data-fui-icon": "true" }, props),
        React.createElement("g", { clipPath: "url(#clip0_2948_30)" },
            React.createElement("path", { d: "M18.75 10C18.75 8.73941 18.0323 7.64645 16.9833 7.10743C17.3439 5.98448 17.0786 4.70417 16.1872 3.8128C15.2958 2.92143 14.0155 2.65608 12.8926 3.01674C12.3535 1.96768 11.2606 1.25 10 1.25C8.73942 1.25 7.64647 1.96768 7.10744 3.01674C5.9845 2.65608 4.70417 2.92144 3.81281 3.8128C2.92144 4.70417 2.65608 5.9845 3.01674 7.10744C1.96768 7.64647 1.25 8.73942 1.25 10C1.25 11.2606 1.96769 12.3536 3.01677 12.8926C2.65611 14.0155 2.92147 15.2958 3.81283 16.1872C4.7042 17.0786 5.98451 17.3439 7.10745 16.9833C7.64648 18.0323 8.73942 18.75 10 18.75C11.2606 18.75 12.3535 18.0323 12.8926 16.9833C14.0155 17.3439 15.2958 17.0786 16.1872 16.1872C17.0786 15.2958 17.3439 14.0155 16.9833 12.8926C18.0323 12.3535 18.75 11.2606 18.75 10Z", fill: color, fillRule: "evenodd", clipRule: "evenodd" }),
            React.createElement("path", { d: "M16.9833 7.10743L16.2692 6.87809C16.157 7.2275 16.3141 7.6068 16.6405 7.77452L16.9833 7.10743ZM16.1872 3.8128L15.6569 4.34313L16.1872 3.8128ZM12.8926 3.01674L12.2255 3.3595C12.3932 3.68592 12.7725 3.84303 13.1219 3.73081L12.8926 3.01674ZM7.10744 3.01674L6.87811 3.73081C7.22752 3.84303 7.60682 3.68592 7.77454 3.3595L7.10744 3.01674ZM3.01674 7.10744L3.3595 7.77454C3.68593 7.60681 3.84303 7.22751 3.73081 6.8781L3.01674 7.10744ZM3.01677 12.8926L3.73084 13.1219C3.84306 12.7725 3.68595 12.3932 3.35952 12.2255L3.01677 12.8926ZM3.81283 16.1872L3.2825 16.7175H3.2825L3.81283 16.1872ZM7.10745 16.9833L7.77454 16.6405C7.60682 16.3141 7.22753 16.157 6.87812 16.2692L7.10745 16.9833ZM12.8926 16.9833L13.1219 16.2692C12.7725 16.157 12.3932 16.3141 12.2255 16.6405L12.8926 16.9833ZM16.9833 12.8926L16.6405 12.2255C16.3141 12.3932 16.157 12.7725 16.2692 13.1219L16.9833 12.8926ZM16.6405 7.77452C17.4493 8.19011 18 9.03139 18 10H19.5C19.5 8.44743 18.6153 7.10278 17.326 6.44033L16.6405 7.77452ZM15.6569 4.34313C16.3418 5.02804 16.5473 6.01229 16.2692 6.87809L17.6973 7.33677C18.1406 5.95668 17.8154 4.3803 16.7175 3.28247L15.6569 4.34313ZM13.1219 3.73081C13.9877 3.45274 14.972 3.65822 15.6569 4.34313L16.7175 3.28247C15.6197 2.18464 14.0433 1.85942 12.6632 2.30266L13.1219 3.73081ZM10 2C10.9686 2 11.8099 2.55067 12.2255 3.3595L13.5596 2.67397C12.8972 1.38469 11.5526 0.5 10 0.5V2ZM7.77454 3.3595C8.19013 2.55067 9.0314 2 10 2V0.5C8.44745 0.5 7.10281 1.38469 6.44035 2.67397L7.77454 3.3595ZM4.34314 4.34313C5.02805 3.65822 6.0123 3.45274 6.87811 3.73081L7.33678 2.30266C5.95669 1.85942 4.3803 2.18465 3.28248 3.28247L4.34314 4.34313ZM3.73081 6.8781C3.45274 6.0123 3.65823 5.02804 4.34314 4.34313L3.28248 3.28247C2.18465 4.3803 1.85942 5.95669 2.30266 7.33678L3.73081 6.8781ZM2 10C2 9.0314 2.55067 8.19013 3.3595 7.77454L2.67397 6.44035C1.38469 7.10281 0.5 8.44745 0.5 10H2ZM3.35952 12.2255C2.55068 11.8099 2 10.9686 2 10H0.5C0.5 11.5526 1.38471 12.8972 2.67401 13.5597L3.35952 12.2255ZM4.34316 15.6569C3.65826 14.972 3.45278 13.9877 3.73084 13.1219L2.30269 12.6632C1.85945 14.0433 2.18468 15.6197 3.2825 16.7175L4.34316 15.6569ZM6.87812 16.2692C6.01232 16.5473 5.02807 16.3418 4.34316 15.6569L3.2825 16.7175C4.38032 17.8153 5.9567 18.1406 7.33679 17.6973L6.87812 16.2692ZM10 18C9.0314 18 8.19014 17.4493 7.77454 16.6405L6.44036 17.326C7.10282 18.6153 8.44745 19.5 10 19.5V18ZM12.2255 16.6405C11.8099 17.4493 10.9686 18 10 18V19.5C11.5526 19.5 12.8972 18.6153 13.5597 17.326L12.2255 16.6405ZM15.6569 15.6569C14.972 16.3418 13.9877 16.5473 13.1219 16.2692L12.6632 17.6973C14.0433 18.1406 15.6197 17.8154 16.7175 16.7175L15.6569 15.6569ZM16.2692 13.1219C16.5473 13.9877 16.3418 14.972 15.6569 15.6569L16.7175 16.7175C17.8154 15.6197 18.1406 14.0433 17.6973 12.6632L16.2692 13.1219ZM18 10C18 10.9686 17.4494 11.8099 16.6405 12.2255L17.326 13.5597C18.6153 12.8972 19.5 11.5526 19.5 10H18Z", fill: color }),
            React.createElement("path", { d: "M6.25 10L8.75 12.6562L13.75 7.5", stroke: color, strokeWidth: "1.5", strokeLinecap: "round", strokeLinejoin: "round" })),
        React.createElement("defs", null,
            React.createElement("clipPath", { id: "clip0_2948_30" },
                React.createElement("path", { fill: color, d: "M0 0H20V20H0z" })))));
};
exports.SealCheckmarkFilled20 = SealCheckmarkFilled20;
exports.default = exports.SealCheckmarkFilled20;
//# sourceMappingURL=SealCheckmarkFilled20.js.map