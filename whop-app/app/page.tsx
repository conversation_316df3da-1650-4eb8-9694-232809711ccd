'use client';

import { useState } from 'react';
import { Head<PERSON>, <PERSON>, <PERSON><PERSON>, Card, Separator, Theme, Tooltip, OTPField, TextField, Select, Badge, DropdownMenu, DateRangePicker } from '@whop/react/components';
import * as Icons from '@frosted-ui/icons';

export default function Page() {
	const [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions' | 'create'>('leaderboards');

	return (
		<Theme appearance="dark" accentColor="blue" hasBackground={true}>
			<div className="min-h-screen bg-black">
				{/* Top Navigation Bar */}
				<nav className="bg-black/50 backdrop-blur-sm border-b border-gray-8/20">
					<div className="max-w-6xl mx-auto px-6 py-6">
						<div className="flex items-center justify-center">
							<div className="flex items-center gap-1 bg-gray-2/80 backdrop-blur-sm rounded-xl p-1.5 border border-gray-6/50" style={{
								boxShadow: '0 0 60px rgba(59, 130, 246, 0.15), 0 0 120px rgba(59, 130, 246, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
							}}>
								<Button
									variant={activeTab === 'leaderboards' ? 'solid' : 'ghost'}
									color={activeTab === 'leaderboards' ? 'blue' : 'gray'}
									onClick={() => setActiveTab('leaderboards')}
									size="2"
									className={`text-sm font-medium transition-all duration-200 ${
										activeTab === 'leaderboards'
											? 'shadow-lg shadow-blue-9/20 text-white'
											: 'text-gray-11 hover:text-white hover:bg-gray-11/10'
									}`}
									style={activeTab === 'leaderboards' ? {
										boxShadow: '0 0 20px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
									} : {}}
								>
									<Icons.BarChart16 />
									Leaderboards
								</Button>
								<Button
									variant={activeTab === 'competitions' ? 'solid' : 'ghost'}
									color={activeTab === 'competitions' ? 'blue' : 'gray'}
									onClick={() => setActiveTab('competitions')}
									size="2"
									className={`text-sm font-medium transition-all duration-200 ${
										activeTab === 'competitions'
											? 'shadow-lg shadow-blue-9/20 text-white'
											: 'text-gray-11 hover:text-white hover:bg-gray-11/10'
									}`}
									style={activeTab === 'competitions' ? {
										boxShadow: '0 0 20px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
									} : {}}
								>
									<Icons.Gift16 />
									Competitions
								</Button>
								<Button
									variant={activeTab === 'create' ? 'solid' : 'ghost'}
									color={activeTab === 'create' ? 'blue' : 'gray'}
									onClick={() => setActiveTab('create')}
									size="2"
									className={`text-sm font-medium transition-all duration-200 ${
										activeTab === 'create'
											? 'shadow-lg shadow-blue-9/20 text-white'
											: 'text-gray-11 hover:text-white hover:bg-gray-11/10'
									}`}
									style={activeTab === 'create' ? {
										boxShadow: '0 0 20px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
									} : {}}
								>
									<Icons.Plus16 />
									Create
								</Button>
							</div>
						</div>
					</div>
				</nav>

				{/* Content */}
				<div className="max-w-6xl mx-auto px-6 py-8 bg-black">
					{activeTab === 'leaderboards' && <LeaderboardsPage />}
					{activeTab === 'competitions' && <CompetitionsPage />}
					{activeTab === 'create' && <CreateCompetitionPage />}
				</div>
			</div>
		</Theme>
	);
}

function LeaderboardsPage() {
	const leaderboards = [
		{
			title: "Overall Leaderboard",
			description: "Top performers across all competitions",
			data: [
				{ rank: 1, name: "Jack Sharkey", handle: "@shark", earnings: 26800 },
				{ rank: 2, name: "Tyler", handle: "@methodicalstew", earnings: 21344 },
				{ rank: 3, name: "Shaq", handle: "@shaq4257", earnings: 14565 },
				{ rank: 4, name: "Ilya Miskov", handle: "@ilyamiskov", earnings: 13915 },
				{ rank: 5, name: "Savnatra", handle: "@savnatra", earnings: 11141 },
			]
		},
		{
			title: "Monthly Leaders",
			description: "This month's top earners",
			data: [
				{ rank: 1, name: "Sarah Kim", handle: "@sarahk", earnings: 18900 },
				{ rank: 2, name: "Mike Johnson", handle: "@mikej", earnings: 16500 },
				{ rank: 3, name: "Alex Chen", handle: "@alexc", earnings: 14200 },
				{ rank: 4, name: "Emma Davis", handle: "@emmad", earnings: 12800 },
				{ rank: 5, name: "Ryan Wilson", handle: "@ryanw", earnings: 11400 },
			]
		},
		{
			title: "Weekly Champions",
			description: "This week's rising stars",
			data: [
				{ rank: 1, name: "Lisa Park", handle: "@lisap", earnings: 8900 },
				{ rank: 2, name: "David Lee", handle: "@davidl", earnings: 7600 },
				{ rank: 3, name: "Maya Singh", handle: "@mayas", earnings: 6800 },
				{ rank: 4, name: "Tom Brown", handle: "@tomb", earnings: 5900 },
				{ rank: 5, name: "Zoe Miller", handle: "@zoem", earnings: 5200 },
			]
		}
	];

	return (
		<div className="flex gap-8">
			{leaderboards.map((leaderboard, index) => (
				<Card key={index} size="1" variant="classic" className="w-80 relative overflow-hidden">
					{/* Blue glow at top */}
					<div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-blue-9/15 to-transparent pointer-events-none"></div>

					<div className="relative space-y-4">
						{/* Title */}
						<div>
							<Text size="3" weight="medium" className="text-white leading-tight" style={{
								textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
							}}>
								{leaderboard.title}
							</Text>
							<Text size="1" className="text-gray-11 mt-1">
								{leaderboard.description}
							</Text>
						</div>

						{/* Leaderboard entries */}
						<div className="space-y-3">
							{leaderboard.data.map((player) => (
								<div key={player.rank} className="flex items-center justify-between">
									<div className="flex items-center gap-3">
										<div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
											player.rank === 1 ? 'bg-yellow-9 text-black' :
											player.rank === 2 ? 'bg-gray-8 text-white' :
											player.rank === 3 ? 'bg-orange-9 text-white' :
											'bg-blue-9/20 text-blue-9'
										}`}>
											{player.rank}
										</div>

										<div>
											<Text size="2" weight="medium" className="text-white">
												{player.name}
											</Text>
											<Text size="1" className="text-gray-11">
												{player.handle}
											</Text>
										</div>
									</div>

									<Text size="2" weight="bold" className="text-blue-11">
										${player.earnings.toLocaleString()}
									</Text>
								</div>
							))}
						</div>
					</div>
				</Card>
			))}
		</div>
	);
}

function CompetitionsPage() {
	const competition = {
		title: "Lambo Challenge",
		grandPrize: "Lamborghini Aventador",
		smallerPrizes: "+7 smaller prizes",
		winCondition: "Most money earned",
		days: "03",
		hours: "14",
		minutes: "15",
		seconds: "03"
	};

	const competition2 = {
		title: "Social Media Growth Challenge",
		grandPrize: "Tesla Model S",
		smallerPrizes: "+5 smaller prizes",
		winCondition: "Most followers gained",
		days: "07",
		hours: "22",
		minutes: "45",
		seconds: "18"
	};

	const competition3 = {
		title: "Content Creation Mastery",
		grandPrize: "MacBook Pro M3",
		smallerPrizes: "+3 smaller prizes",
		winCondition: "Best viral content",
		days: "12",
		hours: "08",
		minutes: "30",
		seconds: "42"
	};

	return (
		<div className="flex gap-8">
			<Card size="1" variant="classic" className="w-80 relative overflow-hidden">
				{/* Blue glow at top */}
				<div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-blue-9/20 to-transparent pointer-events-none"></div>

				<div className="relative space-y-3">
					{/* Title with glow */}
					<div className="mb-3">
						<Text size="3" weight="medium" className="text-white leading-tight" style={{
							textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
						}}>
							{competition.title}
						</Text>
					</div>

					{/* Filter buttons */}
					<div className="flex gap-2 mb-4">
						<Button
							variant="ghost"
							size="1"
							className="px-3 py-1 h-7 text-xs border border-gray-8 text-gray-11"
							style={{
								background: 'linear-gradient(to right, rgba(251, 146, 60, 0.1), rgba(17, 24, 39, 0.8))'
							}}
						>
							Active
						</Button>
						<Button
							variant="ghost"
							size="1"
							className="px-3 py-1 h-7 text-xs border border-gray-8 text-gray-11"
							style={{
								background: 'linear-gradient(to right, rgba(34, 197, 94, 0.1), rgba(17, 24, 39, 0.8))'
							}}
						>
							Upcoming
						</Button>
					</div>

					{/* Prize section */}
					<div className="space-y-2">
						<Text size="1" className="text-gray-11">
							Grand prize
						</Text>

						<div className="space-y-2">
							<Text size="2" weight="medium" className="text-white leading-tight">
								{competition.grandPrize}
							</Text>
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-blue-9"></div>
								<Text size="1" className="text-gray-11">
									{competition.smallerPrizes}
								</Text>
							</div>
						</div>
					</div>

					<Separator className="w-full my-3" />

					{/* Win condition - Y stack */}
					<div className="space-y-1.5">
						<Text size="1" className="text-gray-11">
							Win condition
						</Text>
						<Text size="2" weight="medium" className="text-white block">
							{competition.winCondition}
						</Text>
					</div>

					{/* Join button */}
					<Button
						color="blue"
						variant="classic"
						className="w-full mt-3"
						size="2"
						style={{
							boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
						}}
					>
						Join Competition
					</Button>

					{/* Timer */}
					<div className="space-y-3 mt-4">
						<Text size="1" className="text-gray-11">
							Starts in
						</Text>

						<div className="flex items-center justify-center gap-1.5">
							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition.days}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Days
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition.hours}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Hours
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition.minutes}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Mins
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition.seconds}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Secs
								</Text>
							</div>
						</div>
					</div>
				</div>

			</Card>

			<Card size="1" variant="classic" className="w-80 relative overflow-hidden bg-gray-12/50 border border-gray-11/20">
				{/* Blue glow at top */}
				<div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-blue-9/25 to-transparent pointer-events-none"></div>

				<div className="relative space-y-3">
					{/* Title with glow */}
					<div className="mb-3">
						<Text size="3" weight="medium" className="text-white leading-tight" style={{
							textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
						}}>
							{competition2.title}
						</Text>
					</div>

					{/* Filter buttons */}
					<div className="flex gap-2 mb-4">
						<Button
							variant="ghost"
							size="1"
							className="px-3 py-1 h-7 text-xs border border-gray-8 text-gray-11"
							style={{
								background: 'linear-gradient(to right, rgba(251, 146, 60, 0.1), rgba(17, 24, 39, 0.8))'
							}}
						>
							Live
						</Button>
						<Button
							variant="ghost"
							size="1"
							className="px-3 py-1 h-7 text-xs border border-gray-8 text-gray-11"
							style={{
								background: 'linear-gradient(to right, rgba(34, 197, 94, 0.1), rgba(17, 24, 39, 0.8))'
							}}
						>
							Featured
						</Button>
					</div>

					{/* Prize section */}
					<div className="space-y-2">
						<Text size="1" className="text-gray-11">
							Grand prize
						</Text>

						<div className="space-y-2">
							<Text size="2" weight="medium" className="text-white leading-tight">
								{competition2.grandPrize}
							</Text>
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-blue-9"></div>
								<Text size="1" className="text-gray-11">
									{competition2.smallerPrizes}
								</Text>
							</div>
						</div>
					</div>

					<Separator className="w-full my-3" />

					{/* Win condition - Y stack */}
					<div className="space-y-1.5">
						<Text size="1" className="text-gray-11">
							Win condition
						</Text>
						<Text size="2" weight="medium" className="text-white block">
							{competition2.winCondition}
						</Text>
					</div>

					{/* Join button */}
					<Button
						color="blue"
						variant="classic"
						className="w-full mt-3"
						size="2"
						style={{
							boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
						}}
					>
						Join Competition
					</Button>

					{/* Timer */}
					<div className="space-y-3 mt-4">
						<Text size="1" className="text-gray-11">
							Starts in
						</Text>

						<div className="flex items-center justify-center gap-1.5">
							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition2.days}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Days
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition2.hours}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Hours
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition2.minutes}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Mins
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition2.seconds}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Secs
								</Text>
							</div>
						</div>
					</div>
				</div>
			</Card>

			<Card size="1" variant="classic" className="w-80 relative overflow-hidden border border-gray-6">
				{/* Blue glow at top */}
				<div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-blue-9/20 to-transparent pointer-events-none"></div>

				<div className="relative space-y-3">
					{/* Title with glow */}
					<div className="mb-3">
						<Text size="3" weight="medium" className="text-white leading-tight" style={{
							textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
						}}>
							{competition3.title}
						</Text>
					</div>

					{/* Prize section */}
					<div className="space-y-2">
						<Text size="1" className="text-gray-11">
							Grand prize
						</Text>

						<div className="space-y-2">
							<Text size="2" weight="medium" className="text-white leading-tight">
								{competition3.grandPrize}
							</Text>
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-blue-9"></div>
								<Text size="1" className="text-gray-11">
									{competition3.smallerPrizes}
								</Text>
							</div>
						</div>
					</div>

					<Separator className="w-full my-3" />

					{/* Win condition - Y stack */}
					<div className="space-y-1.5">
						<Text size="1" className="text-gray-11">
							Win condition
						</Text>
						<Text size="2" weight="medium" className="text-white block">
							{competition3.winCondition}
						</Text>
					</div>

					{/* Join button */}
					<Button
						color="blue"
						variant="classic"
						className="w-full mt-3"
						size="2"
						style={{
							boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
						}}
					>
						Join Competition
					</Button>

					{/* Timer */}
					<div className="space-y-3 mt-4">
						<Text size="1" className="text-gray-11">
							Starts in
						</Text>

						<div className="flex items-center justify-center gap-1.5">
							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition3.days}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Days
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition3.hours}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Hours
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition3.minutes}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Mins
								</Text>
							</div>

							<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

							<div className="flex flex-col items-center gap-1.5">
								<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
									<Text size="2" weight="bold" className="text-white font-mono">
										{competition3.seconds}
									</Text>
								</div>
								<Text size="1" className="text-gray-10 text-xs">
									Secs
								</Text>
							</div>
						</div>
					</div>
				</div>
			</Card>
		</div>
	);
}

function CreateCompetitionPage() {
	const [competitionType, setCompetitionType] = useState("Money earned");
	const [startTime, setStartTime] = useState("Choose time");
	const [endTime, setEndTime] = useState("Choose time");

	return (
		<div className="flex items-center justify-center min-h-screen py-8">
			<div className="w-full max-w-2xl mx-auto">
				<Card size="1" variant="classic" className="relative overflow-hidden bg-gray-1/80 backdrop-blur-xl border border-gray-6/50">
					{/* Header */}
					<div className="flex items-center justify-between p-5 border-b border-gray-6/30">
						<Text size="3" weight="medium" className="text-white">
							Competition details
						</Text>
						<Button variant="ghost" size="1" className="text-gray-11 hover:text-white w-6 h-6 p-0 flex items-center justify-center">
							<Text size="3">×</Text>
						</Button>
					</div>

					<div className="p-5">
						{/* Left Column */}
						<div className="space-y-4">
							{/* Competition Type */}
							<div className="space-y-2">
								<Text size="2" weight="medium" className="text-white">
									Competition Type
								</Text>
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										<Button
											variant="surface"
											size="2"
											className="w-full justify-between text-white bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
										>
											<Text size="2">{competitionType}</Text>
											<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
										</Button>
									</DropdownMenu.Trigger>
									<DropdownMenu.Content size="2" variant="translucent">
										<DropdownMenu.Item onClick={() => setCompetitionType("Money earned")}>
											Money earned
										</DropdownMenu.Item>
										<DropdownMenu.Item onClick={() => setCompetitionType("Most followers")}>
											Most followers
										</DropdownMenu.Item>
										<DropdownMenu.Item onClick={() => setCompetitionType("Best content")}>
											Best content
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</div>

							{/* Start Date */}
							<div className="space-y-2">
								<Text size="2" weight="medium" className="text-white">
									Start date
								</Text>
								<div className="flex gap-2">
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											<Button
												variant="surface"
												size="2"
												className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
											>
												<Text size="2">Pick a date</Text>
												<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
											</Button>
										</DropdownMenu.Trigger>
										<DropdownMenu.Content size="2" variant="translucent">
											<DropdownMenu.Item>Today</DropdownMenu.Item>
											<DropdownMenu.Item>Tomorrow</DropdownMenu.Item>
											<DropdownMenu.Item>Next week</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											<Button
												variant="surface"
												size="2"
												className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
											>
												<Text size="2">{startTime}</Text>
												<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
											</Button>
										</DropdownMenu.Trigger>
										<DropdownMenu.Content size="2" variant="translucent">
											<DropdownMenu.Item onClick={() => setStartTime("09:00 AM")}>
												09:00 AM
											</DropdownMenu.Item>
											<DropdownMenu.Item onClick={() => setStartTime("10:00 AM")}>
												10:00 AM
											</DropdownMenu.Item>
											<DropdownMenu.Item onClick={() => setStartTime("11:00 AM")}>
												11:00 AM
											</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								</div>
							</div>

							{/* End Date */}
							<div className="space-y-2">
								<Text size="2" weight="medium" className="text-white">
									End date
								</Text>
								<div className="flex gap-2">
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											<Button
												variant="surface"
												size="2"
												className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
											>
												<Text size="2">Pick a date</Text>
												<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
											</Button>
										</DropdownMenu.Trigger>
										<DropdownMenu.Content size="2" variant="translucent">
											<DropdownMenu.Item>Today</DropdownMenu.Item>
											<DropdownMenu.Item>Tomorrow</DropdownMenu.Item>
											<DropdownMenu.Item>Next week</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											<Button
												variant="surface"
												size="2"
												className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
											>
												<Text size="2">{endTime}</Text>
												<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
											</Button>
										</DropdownMenu.Trigger>
										<DropdownMenu.Content size="2" variant="translucent">
											<DropdownMenu.Item onClick={() => setEndTime("07:00 PM")}>
												07:00 PM
											</DropdownMenu.Item>
											<DropdownMenu.Item onClick={() => setEndTime("08:00 PM")}>
												08:00 PM
											</DropdownMenu.Item>
											<DropdownMenu.Item onClick={() => setEndTime("09:00 PM")}>
												09:00 PM
											</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								</div>
							</div>
						</div>

						{/* Right Column */}
						<div className="space-y-4">
							{/* Prizes Section */}
							<div className="space-y-2">
								<Text size="2" weight="medium" className="text-white">
									Prizes
								</Text>

								{/* 1st Place */}
								<div className="space-y-2 p-3 bg-gray-2/30 rounded-lg border border-gray-6/30">
									<div className="flex items-center justify-between">
										<Text size="2" weight="medium" className="text-white">
											1st place
										</Text>
										<Button variant="ghost" size="1" className="w-5 h-5 p-0 flex items-center justify-center bg-red-9 hover:bg-red-10 rounded">
											<Icons.Trash16 className="w-3 h-3 text-white" />
										</Button>
									</div>

									<div className="flex gap-2">
										<Button
											variant="solid"
											color="gray"
											size="1"
											className="flex-1 bg-gray-6 text-white h-8"
										>
											Cash
										</Button>
										<Button
											variant="ghost"
											color="gray"
											size="1"
											className="flex-1 text-gray-11 h-8"
										>
											Custom
										</Button>
									</div>

									<input
										type="text"
										placeholder="Enter $ amount"
										className="w-full px-3 py-2 bg-gray-3/50 border border-gray-6/50 rounded-md text-white text-sm placeholder-gray-11 focus:outline-none focus:border-blue-9 h-8"
									/>
								</div>

								{/* 2nd Place */}
								<div className="space-y-2 p-3 bg-gray-2/30 rounded-lg border border-gray-6/30">
									<div className="flex items-center justify-between">
										<Text size="2" weight="medium" className="text-white">
											2nd place
										</Text>
										<Button variant="ghost" size="1" className="w-5 h-5 p-0 flex items-center justify-center bg-red-9 hover:bg-red-10 rounded">
											<Icons.Trash16 className="w-3 h-3 text-white" />
										</Button>
									</div>

									<div className="flex gap-2">
										<Button
											variant="solid"
											color="gray"
											size="1"
											className="flex-1 bg-gray-6 text-white h-8"
										>
											Cash
										</Button>
										<Button
											variant="ghost"
											color="gray"
											size="1"
											className="flex-1 text-gray-11 h-8"
										>
											Custom
										</Button>
									</div>

									<input
										type="text"
										placeholder="Enter prize"
										className="w-full px-3 py-2 bg-gray-3/50 border border-gray-6/50 rounded-md text-white text-sm placeholder-gray-11 focus:outline-none focus:border-blue-9 h-8"
									/>
								</div>
							</div>

							{/* Upload Image */}
							<div className="flex flex-col items-center justify-center py-6 border-2 border-dashed border-gray-6/50 rounded-lg bg-gray-2/20">
								<div className="w-8 h-8 bg-gray-4 rounded-lg flex items-center justify-center mb-2">
									<Icons.Upload16 className="text-gray-11 w-4 h-4" />
								</div>
								<Text size="2" weight="medium" className="text-white mb-1">
									Upload image
								</Text>
								<Text size="1" className="text-gray-11 text-center">
									We recommend uploading images<br />with a 1:1 aspect ratio.
								</Text>
							</div>
						</div>
					</div>

					{/* Create Competition Button - Full Width at Bottom */}
					<div className="p-5 pt-0">
						<Button
							variant="solid"
							color="blue"
							size="2"
							className="w-full py-3"
							style={{
								boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
							}}
						>
							Create Competition
						</Button>
					</div>
				</Card>
			</div>
		</div>
	);
}

// Icons showcase component for development
function IconsShowcase() {
	return (
		<div style={{
			display: 'flex',
			flexDirection: 'row',
			width: 400,
			flexWrap: 'wrap'
		}}>
			{Object.entries(Icons).map(([name, Icon]) => (
				<div key={name} style={{
					width: '20%',
					padding: 8
				}}>
					<Tooltip content={name} delayDuration={0}>
						{/* @ts-ignore */}
						<Icon />
					</Tooltip>
				</div>
			))}
		</div>
	);
}
