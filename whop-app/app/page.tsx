"use client";

import { useState } from "react";
import { But<PERSON>, Card, Text, Separator, DropdownMenu, Icons } from "@whop/frosted-ui";

export default function Home() {
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [competitionType, setCompetitionType] = useState("Money earned");
	const [startTime, setStartTime] = useState("09:00 AM");
	const [endTime, setEndTime] = useState("07:00 PM");

	// Sample competition data
	const competition = {
		title: "Weekly Creator Challenge",
		grandPrize: "$5,000",
		smallerPrizes: "10 smaller prizes",
		winCondition: "Most revenue generated",
		days: "05",
		hours: "12",
		minutes: "34",
		seconds: "56"
	};

	const competition2 = {
		title: "Monthly Growth Sprint",
		grandPrize: "$10,000",
		smallerPrizes: "15 smaller prizes",
		winCondition: "Highest follower growth",
		days: "12",
		hours: "08",
		minutes: "45",
		seconds: "23"
	};

	const competition3 = {
		title: "Content Creator Showdown",
		grandPrize: "$7,500",
		smallerPrizes: "8 smaller prizes",
		winCondition: "Best engagement rate",
		days: "03",
		hours: "16",
		minutes: "12",
		seconds: "41"
	};

	return (
		<div className="min-h-screen bg-black text-white">
			{/* Navbar */}
			<nav className="flex items-center justify-between p-6 border-b border-gray-6/30">
				<div className="flex items-center gap-8">
					<Text size="4" weight="bold" className="text-white" style={{
						textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
					}}>
						Leaderboard
					</Text>

					<div className="flex items-center gap-6">
						<Button variant="ghost" size="2" className="text-white hover:text-blue-11">
							<Icons.Home16 className="w-4 h-4 mr-2" />
							Home
						</Button>
						<Button variant="ghost" size="2" className="text-white hover:text-blue-11">
							<Icons.Trophy16 className="w-4 h-4 mr-2" />
							Competitions
						</Button>
						<Button variant="ghost" size="2" className="text-white hover:text-blue-11">
							<Icons.BarChart16 className="w-4 h-4 mr-2" />
							Leaderboard
						</Button>
					</div>
				</div>

				<div className="flex items-center gap-4">
					<Button
						variant="solid"
						color="blue"
						size="2"
						onClick={() => setShowCreateModal(true)}
						style={{
							boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
						}}
					>
						<Icons.Plus16 className="w-4 h-4 mr-2" />
						Create Competition
					</Button>

					<div className="w-8 h-8 bg-gradient-to-br from-blue-9 to-blue-11 rounded-full flex items-center justify-center">
						<Text size="2" weight="bold" className="text-white">U</Text>
					</div>
				</div>
			</nav>

			{/* Main Content */}
			<div className="p-8">
				{/* Main Leaderboard */}
				<div className="mb-16">
					<Card size="1" variant="classic" className="max-w-4xl mx-auto relative overflow-hidden bg-gray-1/80 backdrop-blur-xl border border-gray-6/50">
						{/* Header with blue glow */}
						<div className="relative">
							<div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-blue-9/20 to-transparent pointer-events-none"></div>

							<div className="relative p-6 border-b border-gray-6/30">
								<div className="flex items-center justify-between mb-4">
									<Text size="4" weight="medium" className="text-white" style={{
										textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
									}}>
										Global Leaderboard
									</Text>

									{/* Filter buttons */}
									<div className="flex gap-2">
										<Button
											variant="ghost"
											size="1"
											className="px-3 py-1 h-7 text-xs border border-gray-11 text-orange-11 hover:text-orange-10 transition-all duration-200 flex items-center gap-1.5"
											style={{
												background: 'linear-gradient(135deg, rgba(255, 140, 0, 0.15), rgba(0, 0, 0, 0.8))',
												borderRadius: '25px'
											}}
										>
											This Week
										</Button>
										<Button
											variant="ghost"
											size="1"
											className="px-3 py-1 h-7 text-xs border border-gray-11 text-green-11 hover:text-green-10 transition-all duration-200 flex items-center gap-1.5"
											style={{
												background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(0, 0, 0, 0.8))',
												borderRadius: '25px'
											}}
										>
											All Time
										</Button>
									</div>
								</div>

								{/* Leaderboard header */}
								<div className="grid grid-cols-12 gap-4 px-4 py-2 text-gray-11 text-sm">
									<div className="col-span-1">Rank</div>
									<div className="col-span-6">User</div>
									<div className="col-span-3">Earnings</div>
									<div className="col-span-2">Competitions</div>
								</div>
							</div>
						</div>

						{/* Leaderboard entries */}
						<div className="p-6 space-y-3">
							{/* Top 3 with special styling */}
							<div className="space-y-2">
								{/* 1st Place */}
								<div className="grid grid-cols-12 gap-4 p-4 bg-gradient-to-r from-yellow-9/10 to-transparent border border-yellow-9/20 rounded-lg">
									<div className="col-span-1 flex items-center">
										<div className="w-8 h-8 bg-yellow-9 rounded-full flex items-center justify-center">
											<Text size="2" weight="bold" className="text-black">1</Text>
										</div>
									</div>
									<div className="col-span-6 flex items-center gap-3">
										<div className="w-10 h-10 bg-gradient-to-br from-blue-9 to-blue-11 rounded-full flex items-center justify-center">
											<Text size="2" weight="bold" className="text-white">JD</Text>
										</div>
										<div>
											<Text size="2" weight="medium" className="text-white">John Doe</Text>
											<Text size="1" className="text-gray-11">@johndoe</Text>
										</div>
									</div>
									<div className="col-span-3 flex items-center">
										<Text size="3" weight="bold" className="text-yellow-11">$12,450</Text>
									</div>
									<div className="col-span-2 flex items-center">
										<Text size="2" className="text-gray-11">8 wins</Text>
									</div>
								</div>

								{/* 2nd Place */}
								<div className="grid grid-cols-12 gap-4 p-4 bg-gradient-to-r from-gray-9/10 to-transparent border border-gray-9/20 rounded-lg">
									<div className="col-span-1 flex items-center">
										<div className="w-8 h-8 bg-gray-9 rounded-full flex items-center justify-center">
											<Text size="2" weight="bold" className="text-white">2</Text>
										</div>
									</div>
									<div className="col-span-6 flex items-center gap-3">
										<div className="w-10 h-10 bg-gradient-to-br from-green-9 to-green-11 rounded-full flex items-center justify-center">
											<Text size="2" weight="bold" className="text-white">SM</Text>
										</div>
										<div>
											<Text size="2" weight="medium" className="text-white">Sarah Miller</Text>
											<Text size="1" className="text-gray-11">@sarahmiller</Text>
										</div>
									</div>
									<div className="col-span-3 flex items-center">
										<Text size="3" weight="bold" className="text-gray-11">$9,820</Text>
									</div>
									<div className="col-span-2 flex items-center">
										<Text size="2" className="text-gray-11">6 wins</Text>
									</div>
								</div>

								{/* 3rd Place */}
								<div className="grid grid-cols-12 gap-4 p-4 bg-gradient-to-r from-orange-9/10 to-transparent border border-orange-9/20 rounded-lg">
									<div className="col-span-1 flex items-center">
										<div className="w-8 h-8 bg-orange-9 rounded-full flex items-center justify-center">
											<Text size="2" weight="bold" className="text-white">3</Text>
										</div>
									</div>
									<div className="col-span-6 flex items-center gap-3">
										<div className="w-10 h-10 bg-gradient-to-br from-purple-9 to-purple-11 rounded-full flex items-center justify-center">
											<Text size="2" weight="bold" className="text-white">MJ</Text>
										</div>
										<div>
											<Text size="2" weight="medium" className="text-white">Mike Johnson</Text>
											<Text size="1" className="text-gray-11">@mikejohnson</Text>
										</div>
									</div>
									<div className="col-span-3 flex items-center">
										<Text size="3" weight="bold" className="text-orange-11">$7,650</Text>
									</div>
									<div className="col-span-2 flex items-center">
										<Text size="2" className="text-gray-11">5 wins</Text>
									</div>
								</div>
							</div>

							<Separator className="w-full border-gray-6/30" />

							{/* Regular entries */}
							<div className="space-y-1">
								{[
									{ rank: 4, name: "Alex Chen", handle: "@alexchen", earnings: "$6,200", wins: "4 wins", initials: "AC", color: "from-red-9 to-red-11" },
									{ rank: 5, name: "Emma Wilson", handle: "@emmawilson", earnings: "$5,890", wins: "3 wins", initials: "EW", color: "from-indigo-9 to-indigo-11" },
									{ rank: 6, name: "David Brown", handle: "@davidbrown", earnings: "$4,750", wins: "3 wins", initials: "DB", color: "from-teal-9 to-teal-11" },
									{ rank: 7, name: "Lisa Garcia", handle: "@lisagarcia", earnings: "$4,320", wins: "2 wins", initials: "LG", color: "from-pink-9 to-pink-11" },
									{ rank: 8, name: "Tom Anderson", handle: "@tomanderson", earnings: "$3,980", wins: "2 wins", initials: "TA", color: "from-cyan-9 to-cyan-11" }
								].map((user) => (
									<div key={user.rank} className="grid grid-cols-12 gap-4 p-3 hover:bg-gray-3/20 rounded-lg transition-colors duration-200">
										<div className="col-span-1 flex items-center">
											<Text size="2" weight="medium" className="text-gray-11">{user.rank}</Text>
										</div>
										<div className="col-span-6 flex items-center gap-3">
											<div className={`w-8 h-8 bg-gradient-to-br ${user.color} rounded-full flex items-center justify-center`}>
												<Text size="1" weight="bold" className="text-white">{user.initials}</Text>
											</div>
											<div>
												<Text size="2" weight="medium" className="text-white">{user.name}</Text>
												<Text size="1" className="text-gray-11">{user.handle}</Text>
											</div>
										</div>
										<div className="col-span-3 flex items-center">
											<Text size="2" weight="medium" className="text-white">{user.earnings}</Text>
										</div>
										<div className="col-span-2 flex items-center">
											<Text size="1" className="text-gray-11">{user.wins}</Text>
										</div>
									</div>
								))}
							</div>
						</div>
					</Card>
				</div>

				{/* Competitions Grid */}
				<div className="flex gap-8 justify-center flex-wrap">
					{/* Competition Card 1 */}
					<Card size="1" variant="classic" className="w-80 relative overflow-hidden">
						<div className="relative space-y-3">
							{/* Title */}
							<div className="mb-3">
								<Text size="3" weight="bold" className="text-white leading-tight">
									{competition.title}
								</Text>
							</div>

							{/* Filter buttons */}
							<div className="flex gap-2 mb-4">
								<Button
									variant="ghost"
									size="1"
									className="px-3 py-1 h-7 text-xs border border-gray-11 text-green-11 font-bold hover:text-green-10 transition-all duration-200 flex items-center gap-1.5"
									style={{
										background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(0, 0, 0, 0.8))',
										borderRadius: '25px'
									}}
								>
									<Icons.Star16 className="w-3 h-3" />
									Most $
								</Button>
								<Button
									variant="ghost"
									size="1"
									className="px-3 py-1 h-7 text-xs border border-gray-11 text-orange-11 font-bold hover:text-orange-10 transition-all duration-200 flex items-center gap-1.5"
									style={{
										background: 'linear-gradient(135deg, rgba(255, 140, 0, 0.15), rgba(0, 0, 0, 0.8))',
										borderRadius: '25px'
									}}
								>
									<Icons.ArrowUp16 className="w-3 h-3" />
									Jumping Up
								</Button>
							</div>

							{/* Prize section */}
							<div className="space-y-2">
								<Text size="1" className="text-gray-11">
									Grand prize
								</Text>

								<div className="space-y-2">
									<Text size="2" weight="medium" className="text-white leading-tight">
										{competition.grandPrize}
									</Text>
									<div className="flex items-center gap-2">
										<div className="w-2 h-2 rounded-full bg-blue-9"></div>
										<Text size="1" className="text-gray-11">
											{competition.smallerPrizes}
										</Text>
									</div>
								</div>
							</div>

							<Separator className="w-full my-3" />

							{/* Win condition */}
							<div className="space-y-1.5">
								<Text size="1" className="text-gray-11">
									Win condition
								</Text>
								<Text size="2" weight="medium" className="text-white block">
									{competition.winCondition}
								</Text>
							</div>

							{/* Join button */}
							<Button
								color="blue"
								variant="classic"
								className="w-full mt-3"
								size="2"
								style={{
									boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
								}}
							>
								Join Competition
							</Button>

							{/* Timer */}
							<div className="space-y-3 mt-4">
								<Text size="1" className="text-gray-11">
									Starts in
								</Text>

								<div className="flex items-center justify-center gap-1.5">
									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition.days}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Days
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition.hours}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Hours
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition.minutes}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Mins
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition.seconds}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Secs
										</Text>
									</div>
								</div>
							</div>
						</div>
					</Card>

					{/* Competition Card 2 */}
					<Card size="1" variant="classic" className="w-80 relative overflow-hidden">
						<div className="relative space-y-3">
							{/* Title */}
							<div className="mb-3">
								<Text size="3" weight="bold" className="text-white leading-tight">
									{competition2.title}
								</Text>
							</div>

							{/* Filter buttons */}
							<div className="flex gap-2 mb-4">
								<Button
									variant="ghost"
									size="1"
									className="px-3 py-1 h-7 text-xs border border-gray-11 text-green-11 font-bold hover:text-green-10 transition-all duration-200 flex items-center gap-1.5"
									style={{
										background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(0, 0, 0, 0.8))',
										borderRadius: '25px'
									}}
								>
									<Icons.Star16 className="w-3 h-3" />
									Most $
								</Button>
								<Button
									variant="ghost"
									size="1"
									className="px-3 py-1 h-7 text-xs border border-gray-11 text-orange-11 font-bold hover:text-orange-10 transition-all duration-200 flex items-center gap-1.5"
									style={{
										background: 'linear-gradient(135deg, rgba(255, 140, 0, 0.15), rgba(0, 0, 0, 0.8))',
										borderRadius: '25px'
									}}
								>
									<Icons.ArrowUp16 className="w-3 h-3" />
									Jumping Up
								</Button>
							</div>

							{/* Prize section */}
							<div className="space-y-2">
								<Text size="1" className="text-gray-11">
									Grand prize
								</Text>

								<div className="space-y-2">
									<Text size="2" weight="medium" className="text-white leading-tight">
										{competition2.grandPrize}
									</Text>
									<div className="flex items-center gap-2">
										<div className="w-2 h-2 rounded-full bg-blue-9"></div>
										<Text size="1" className="text-gray-11">
											{competition2.smallerPrizes}
										</Text>
									</div>
								</div>
							</div>

							<Separator className="w-full my-3" />

							{/* Win condition */}
							<div className="space-y-1.5">
								<Text size="1" className="text-gray-11">
									Win condition
								</Text>
								<Text size="2" weight="medium" className="text-white block">
									{competition2.winCondition}
								</Text>
							</div>

							{/* Join button */}
							<Button
								color="blue"
								variant="classic"
								className="w-full mt-3"
								size="2"
								style={{
									boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
								}}
							>
								Join Competition
							</Button>

							{/* Timer */}
							<div className="space-y-3 mt-4">
								<Text size="1" className="text-gray-11">
									Starts in
								</Text>

								<div className="flex items-center justify-center gap-1.5">
									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition2.days}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Days
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition2.hours}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Hours
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition2.minutes}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Mins
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-3">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition2.seconds}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Secs
										</Text>
									</div>
								</div>
							</div>
						</div>
					</Card>

					{/* Competition Card 3 */}
					<Card size="1" variant="classic" className="w-80 relative overflow-hidden border border-gray-6">
						<div className="relative space-y-3">
							{/* Title */}
							<div className="mb-3">
								<Text size="3" weight="bold" className="text-white leading-tight">
									{competition3.title}
								</Text>
							</div>

							{/* Filter buttons */}
							<div className="flex gap-2 mb-4">
								<Button
									variant="ghost"
									size="1"
									className="px-3 py-1 h-7 text-xs border border-gray-11 text-green-11 font-bold hover:text-green-10 transition-all duration-200 flex items-center gap-1.5"
									style={{
										background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(0, 0, 0, 0.8))',
										borderRadius: '25px'
									}}
								>
									<Icons.Star16 className="w-3 h-3" />
									Most $
								</Button>
								<Button
									variant="ghost"
									size="1"
									className="px-3 py-1 h-7 text-xs border border-gray-11 text-orange-11 font-bold hover:text-orange-10 transition-all duration-200 flex items-center gap-1.5"
									style={{
										background: 'linear-gradient(135deg, rgba(255, 140, 0, 0.15), rgba(0, 0, 0, 0.8))',
										borderRadius: '25px'
									}}
								>
									<Icons.ArrowUp16 className="w-3 h-3" />
									Jumping Up
								</Button>
							</div>

							{/* Prize section */}
							<div className="space-y-2">
								<Text size="1" className="text-gray-11">
									Grand prize
								</Text>

								<div className="space-y-2">
									<Text size="2" weight="medium" className="text-white leading-tight">
										{competition3.grandPrize}
									</Text>
									<div className="flex items-center gap-2">
										<div className="w-2 h-2 rounded-full bg-blue-9"></div>
										<Text size="1" className="text-gray-11">
											{competition3.smallerPrizes}
										</Text>
									</div>
								</div>
							</div>

							<Separator className="w-full my-3" />

							{/* Win condition */}
							<div className="space-y-1.5">
								<Text size="1" className="text-gray-11">
									Win condition
								</Text>
								<Text size="2" weight="medium" className="text-white block">
									{competition3.winCondition}
								</Text>
							</div>

							{/* Join button */}
							<Button
								color="blue"
								variant="classic"
								className="w-full mt-3"
								size="2"
								style={{
									boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
								}}
							>
								Join Competition
							</Button>

							{/* Timer */}
							<div className="space-y-3 mt-4">
								<Text size="1" className="text-gray-11">
									Starts in
								</Text>

								<div className="flex items-center justify-center gap-1.5">
									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition3.days}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Days
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-2">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition3.hours}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Hours
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-2">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition3.minutes}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Mins
										</Text>
									</div>

									<Text size="2" className="text-gray-11 font-mono -mt-2">:</Text>

									<div className="flex flex-col items-center gap-1.5">
										<div className="w-8 h-12 bg-gray-2 border border-gray-6 rounded-md flex items-center justify-center shadow-sm">
											<Text size="2" weight="bold" className="text-white font-mono">
												{competition3.seconds}
											</Text>
										</div>
										<Text size="1" className="text-gray-10 text-xs">
											Secs
										</Text>
									</div>
								</div>
							</div>
						</div>
					</Card>
				</div>
			</div>

			{/* Create Competition Modal */}
			{showCreateModal && (
				<div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
					<div className="flex flex-col justify-center min-h-screen py-16">
						<div className="w-full max-w-2xl mx-auto">
							{/* Main heading */}
							<div className="text-center mb-16">
								<h1
									className="font-medium leading-tight"
									style={{
										backgroundImage: 'linear-gradient(135deg, #6b7280 0%, #ffffff 50%, #9ca3af 100%)',
										WebkitBackgroundClip: 'text',
										WebkitTextFillColor: 'transparent',
										backgroundClip: 'text',
										fontSize: '6rem',
										color: 'transparent'
									}}
								>
									Create your first competition
								</h1>
							</div>

							<Card size="1" variant="classic" className="relative overflow-hidden bg-gray-1/80 backdrop-blur-xl border border-gray-6/50">
								{/* Header */}
								<div className="flex items-center justify-between p-5 border-b border-gray-6/30">
									<Text size="3" weight="medium" className="text-white">
										Competition details
									</Text>
									<Button
										variant="ghost"
										size="1"
										className="text-gray-11 hover:text-white w-6 h-6 p-0 flex items-center justify-center"
										onClick={() => setShowCreateModal(false)}
									>
										<Text size="3">×</Text>
									</Button>
								</div>

								<div className="p-5">
									<div className="grid grid-cols-2 gap-6">
										{/* Left Column */}
										<div className="space-y-4">
											{/* Competition Type */}
											<div className="space-y-2">
												<Text size="2" weight="medium" className="text-white">
													Competition Type
												</Text>
												<DropdownMenu.Root>
													<DropdownMenu.Trigger>
														<Button
															variant="surface"
															size="2"
															className="w-full justify-between text-white bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
														>
															<Text size="2">{competitionType}</Text>
															<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
														</Button>
													</DropdownMenu.Trigger>
													<DropdownMenu.Content size="2" variant="translucent">
														<DropdownMenu.Item onClick={() => setCompetitionType("Money earned")}>
															Money earned
														</DropdownMenu.Item>
														<DropdownMenu.Item onClick={() => setCompetitionType("Most followers")}>
															Most followers
														</DropdownMenu.Item>
														<DropdownMenu.Item onClick={() => setCompetitionType("Best content")}>
															Best content
														</DropdownMenu.Item>
													</DropdownMenu.Content>
												</DropdownMenu.Root>
											</div>

											{/* Start Date */}
											<div className="space-y-2">
												<Text size="2" weight="medium" className="text-white">
													Start date
												</Text>
												<div className="flex gap-2">
													<DropdownMenu.Root>
														<DropdownMenu.Trigger>
															<Button
																variant="surface"
																size="2"
																className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
															>
																<Text size="2">Pick a date</Text>
																<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
															</Button>
														</DropdownMenu.Trigger>
														<DropdownMenu.Content size="2" variant="translucent">
															<DropdownMenu.Item>Today</DropdownMenu.Item>
															<DropdownMenu.Item>Tomorrow</DropdownMenu.Item>
															<DropdownMenu.Item>Next week</DropdownMenu.Item>
														</DropdownMenu.Content>
													</DropdownMenu.Root>
													<DropdownMenu.Root>
														<DropdownMenu.Trigger>
															<Button
																variant="surface"
																size="2"
																className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
															>
																<Text size="2">{startTime}</Text>
																<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
															</Button>
														</DropdownMenu.Trigger>
														<DropdownMenu.Content size="2" variant="translucent">
															<DropdownMenu.Item onClick={() => setStartTime("09:00 AM")}>
																09:00 AM
															</DropdownMenu.Item>
															<DropdownMenu.Item onClick={() => setStartTime("10:00 AM")}>
																10:00 AM
															</DropdownMenu.Item>
															<DropdownMenu.Item onClick={() => setStartTime("11:00 AM")}>
																11:00 AM
															</DropdownMenu.Item>
														</DropdownMenu.Content>
													</DropdownMenu.Root>
												</div>
											</div>

											{/* End Date */}
											<div className="space-y-2">
												<Text size="2" weight="medium" className="text-white">
													End date
												</Text>
												<div className="flex gap-2">
													<DropdownMenu.Root>
														<DropdownMenu.Trigger>
															<Button
																variant="surface"
																size="2"
																className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
															>
																<Text size="2">Pick a date</Text>
																<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
															</Button>
														</DropdownMenu.Trigger>
														<DropdownMenu.Content size="2" variant="translucent">
															<DropdownMenu.Item>Today</DropdownMenu.Item>
															<DropdownMenu.Item>Tomorrow</DropdownMenu.Item>
															<DropdownMenu.Item>Next week</DropdownMenu.Item>
														</DropdownMenu.Content>
													</DropdownMenu.Root>
													<DropdownMenu.Root>
														<DropdownMenu.Trigger>
															<Button
																variant="surface"
																size="2"
																className="flex-1 justify-between text-gray-11 bg-gray-3/50 border-gray-6/50 px-3 py-2 h-9"
															>
																<Text size="2">{endTime}</Text>
																<Icons.ChevronDown12 className="text-gray-11 w-3 h-3" />
															</Button>
														</DropdownMenu.Trigger>
														<DropdownMenu.Content size="2" variant="translucent">
															<DropdownMenu.Item onClick={() => setEndTime("07:00 PM")}>
																07:00 PM
															</DropdownMenu.Item>
															<DropdownMenu.Item onClick={() => setEndTime("08:00 PM")}>
																08:00 PM
															</DropdownMenu.Item>
															<DropdownMenu.Item onClick={() => setEndTime("09:00 PM")}>
																09:00 PM
															</DropdownMenu.Item>
														</DropdownMenu.Content>
													</DropdownMenu.Root>
												</div>
											</div>
										</div>

										{/* Right Column */}
										<div className="space-y-4">
											{/* Prizes Section */}
											<div className="space-y-2">
												<Text size="2" weight="medium" className="text-white">
													Prizes
												</Text>

												{/* 1st Place */}
												<div className="space-y-2 p-3 bg-gray-2/30 rounded-lg border border-gray-6/30">
													<div className="flex items-center justify-between">
														<Text size="2" weight="medium" className="text-white">
															1st place
														</Text>
														<Button variant="ghost" size="1" className="w-5 h-5 p-0 flex items-center justify-center bg-red-9 hover:bg-red-10 rounded">
															<Icons.Trash16 className="w-3 h-3 text-white" />
														</Button>
													</div>

													<div className="flex gap-2">
														<Button
															variant="solid"
															color="gray"
															size="1"
															className="flex-1 bg-gray-6 text-white h-8"
														>
															Cash
														</Button>
														<Button
															variant="ghost"
															color="gray"
															size="1"
															className="flex-1 text-gray-11 h-8"
														>
															Custom
														</Button>
													</div>

													<input
														type="text"
														placeholder="Enter $ amount"
														className="w-full px-3 py-2 bg-gray-3/50 border border-gray-6/50 rounded-md text-white text-sm placeholder-gray-11 focus:outline-none focus:border-blue-9 h-8"
													/>
												</div>

												{/* 2nd Place */}
												<div className="space-y-2 p-3 bg-gray-2/30 rounded-lg border border-gray-6/30">
													<div className="flex items-center justify-between">
														<Text size="2" weight="medium" className="text-white">
															2nd place
														</Text>
														<Button variant="ghost" size="1" className="w-5 h-5 p-0 flex items-center justify-center bg-red-9 hover:bg-red-10 rounded">
															<Icons.Trash16 className="w-3 h-3 text-white" />
														</Button>
													</div>

													<div className="flex gap-2">
														<Button
															variant="solid"
															color="gray"
															size="1"
															className="flex-1 bg-gray-6 text-white h-8"
														>
															Cash
														</Button>
														<Button
															variant="ghost"
															color="gray"
															size="1"
															className="flex-1 text-gray-11 h-8"
														>
															Custom
														</Button>
													</div>

													<input
														type="text"
														placeholder="Enter prize"
														className="w-full px-3 py-2 bg-gray-3/50 border border-gray-6/50 rounded-md text-white text-sm placeholder-gray-11 focus:outline-none focus:border-blue-9 h-8"
													/>
												</div>
											</div>

											{/* Upload Image */}
											<div className="flex flex-col items-center justify-center py-6 border-2 border-dashed border-gray-6/50 rounded-lg bg-gray-2/20">
												<div className="w-8 h-8 bg-gray-4 rounded-lg flex items-center justify-center mb-2">
													<Icons.Upload16 className="text-gray-11 w-4 h-4" />
												</div>
												<Text size="2" weight="medium" className="text-white mb-1">
													Upload image
												</Text>
												<Text size="1" className="text-gray-11 text-center">
													We recommend uploading images<br />with a 1:1 aspect ratio.
												</Text>
											</div>
										</div>
									</div>

									{/* Create Competition Button - Full Width at Bottom */}
									<div className="p-5 pt-0">
										<Button
											variant="solid"
											color="blue"
											size="2"
											className="w-full py-3"
											style={{
												boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'
											}}
											onClick={() => setShowCreateModal(false)}
										>
											Create Competition
										</Button>
									</div>
								</div>
							</Card>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}