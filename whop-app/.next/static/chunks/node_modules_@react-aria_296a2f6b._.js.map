{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "ar-AE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/ar-AE.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} إلى {endDate}\",\n  \"dateSelected\": \"{date} المحدد\",\n  \"finishRangeSelectionPrompt\": \"انقر لإنهاء عملية تحديد نطاق التاريخ\",\n  \"maximumDate\": \"آخر تاريخ متاح\",\n  \"minimumDate\": \"أول تاريخ متاح\",\n  \"next\": \"التالي\",\n  \"previous\": \"السابق\",\n  \"selectedDateDescription\": \"تاريخ محدد: {date}\",\n  \"selectedRangeDescription\": \"المدى الزمني المحدد: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"انقر لبدء عملية تحديد نطاق التاريخ\",\n  \"todayDate\": \"اليوم، {date}\",\n  \"todayDateSelected\": \"اليوم، {date} محدد\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,uBAAK,EAAE,KAAK,OAAO,EAAE;IAChF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,2CAAO,CAAC;IAC/C,8BAA8B,CAAC,8NAAoC,CAAC;IACpE,eAAe,CAAC,sFAAc,CAAC;IAC/B,eAAe,CAAC,sFAAc,CAAC;IAC/B,QAAQ,CAAC,0CAAM,CAAC;IAChB,YAAY,CAAC,0CAAM,CAAC;IACpB,2BAA2B,CAAC,OAAS,CAAC,kEAAY,EAAE,KAAK,IAAI,EAAE;IAC/D,4BAA4B,CAAC,OAAS,CAAC,2HAAqB,EAAE,KAAK,SAAS,EAAE;IAC9E,6BAA6B,CAAC,gNAAkC,CAAC;IACjE,aAAa,CAAC,OAAS,CAAC,2CAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,2CAAO,EAAE,KAAK,IAAI,CAAC,6BAAK,CAAC;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "file": "bg-BG.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/bg-BG.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} до {endDate}\",\n  \"dateSelected\": \"Избрано е {date}\",\n  \"finishRangeSelectionPrompt\": \"Натиснете, за да довършите избора на времеви интервал\",\n  \"maximumDate\": \"Последна налична дата\",\n  \"minimumDate\": \"Първа налична дата\",\n  \"next\": \"Напред\",\n  \"previous\": \"Назад\",\n  \"selectedDateDescription\": \"Избрана дата: {date}\",\n  \"selectedRangeDescription\": \"Избран диапазон: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Натиснете, за да пристъпите към избора на времеви интервал\",\n  \"todayDate\": \"Днес, {date}\",\n  \"todayDateSelected\": \"Днес, {date} са избрани\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,gBAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,CAAC,0DAAU,EAAE,KAAK,IAAI,EAAE;IAClD,8BAA8B,CAAC,mUAAqD,CAAC;IACrF,eAAe,CAAC,uIAAqB,CAAC;IACtC,eAAe,CAAC,kHAAkB,CAAC;IACnC,QAAQ,CAAC,0CAAM,CAAC;IAChB,YAAY,CAAC,mCAAK,CAAC;IACnB,2BAA2B,CAAC,OAAS,CAAC,gFAAc,EAAE,KAAK,IAAI,EAAE;IACjE,4BAA4B,CAAC,OAAS,CAAC,qGAAiB,EAAE,KAAK,SAAS,EAAE;IAC1E,6BAA6B,CAAC,gWAA0D,CAAC;IACzF,aAAa,CAAC,OAAS,CAAC,8BAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,8BAAM,EAAE,KAAK,IAAI,CAAC,iEAAW,CAAC;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "file": "cs-CZ.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/cs-CZ.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} až {endDate}\",\n  \"dateSelected\": \"<PERSON>yb<PERSON><PERSON><PERSON> {date}\",\n  \"finishRangeSelectionPrompt\": \"Kliknutím dokončíte výběr rozsahu dat\",\n  \"maximumDate\": \"Poslední dostupné datum\",\n  \"minimumDate\": \"První dostupné datum\",\n  \"next\": \"Dalš<PERSON>\",\n  \"previous\": \"Předchozí\",\n  \"selectedDateDescription\": \"Vybrané datum: {date}\",\n  \"selectedRangeDescription\": \"Vybrané období: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Kliknutím zahájíte výběr rozsahu dat\",\n  \"todayDate\": \"Dnes, {date}\",\n  \"todayDateSelected\": \"Dnes, vybráno {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,UAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,CAAC,WAAQ,EAAE,KAAK,IAAI,EAAE;IAChD,8BAA8B,CAAC,0DAAqC,CAAC;IACrE,eAAe,CAAC,6BAAuB,CAAC;IACxC,eAAe,CAAC,0BAAoB,CAAC;IACrC,QAAQ,CAAC,cAAK,CAAC;IACf,YAAY,CAAC,kBAAS,CAAC;IACvB,2BAA2B,CAAC,OAAS,CAAC,kBAAe,EAAE,KAAK,IAAI,EAAE;IAClE,4BAA4B,CAAC,OAAS,CAAC,sBAAgB,EAAE,KAAK,SAAS,EAAE;IACzE,6BAA6B,CAAC,sDAAoC,CAAC;IACnE,aAAa,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,iBAAc,EAAE,KAAK,IAAI,EAAE;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "file": "da-DK.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/da-DK.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} til {endDate}\",\n  \"dateSelected\": \"{date} valgt\",\n  \"finishRangeSelectionPrompt\": \"Klik for at fuldføre valg af datoområde\",\n  \"maximumDate\": \"<PERSON>ste ledige dato\",\n  \"minimumDate\": \"<PERSON><PERSON>rste ledige dato\",\n  \"next\": \"<PERSON><PERSON>ste\",\n  \"previous\": \"Forrige\",\n  \"selectedDateDescription\": \"Valgt dato: {date}\",\n  \"selectedRangeDescription\": \"Valgt interval: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Klik for at starte valg af datoområde\",\n  \"todayDate\": \"I dag, {date}\",\n  \"todayDateSelected\": \"I dag, {date} valgt\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE;IAChF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC;IAC9C,8BAA8B,CAAC,6CAAuC,CAAC;IACvE,eAAe,CAAC,kBAAkB,CAAC;IACnC,eAAe,CAAC,qBAAkB,CAAC;IACnC,QAAQ,CAAC,QAAK,CAAC;IACf,YAAY,CAAC,OAAO,CAAC;IACrB,2BAA2B,CAAC,OAAS,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;IAC/D,4BAA4B,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,SAAS,EAAE;IACzE,6BAA6B,CAAC,wCAAqC,CAAC;IACpE,aAAa,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "file": "de-DE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/de-DE.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} bis {endDate}\",\n  \"dateSelected\": \"{date} ausgewählt\",\n  \"finishRangeSelectionPrompt\": \"<PERSON><PERSON><PERSON>, um die Auswahl des Datumsbereichs zu beenden\",\n  \"maximumDate\": \"Letztes verfügbares Datum\",\n  \"minimumDate\": \"Erstes verfügbares Datum\",\n  \"next\": \"Weiter\",\n  \"previous\": \"Zurück\",\n  \"selectedDateDescription\": \"Ausgewähltes Datum: {date}\",\n  \"selectedRangeDescription\": \"Ausgewählter Bereich: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"<PERSON><PERSON><PERSON>, um die Auswahl des Datumsbereichs zu beginnen\",\n  \"todayDate\": \"Heute, {date}\",\n  \"todayDateSelected\": \"Heute, {date} ausgewählt\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE;IAChF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAW,CAAC;IACnD,8BAA8B,CAAC,qDAAqD,CAAC;IACrF,eAAe,CAAC,4BAAyB,CAAC;IAC1C,eAAe,CAAC,2BAAwB,CAAC;IACzC,QAAQ,CAAC,MAAM,CAAC;IAChB,YAAY,CAAC,SAAM,CAAC;IACpB,2BAA2B,CAAC,OAAS,CAAC,uBAAoB,EAAE,KAAK,IAAI,EAAE;IACvE,4BAA4B,CAAC,OAAS,CAAC,yBAAsB,EAAE,KAAK,SAAS,EAAE;IAC/E,6BAA6B,CAAC,sDAAsD,CAAC;IACrF,aAAa,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,cAAW,CAAC;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "file": "el-GR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/el-GR.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} έως {endDate}\",\n  \"dateSelected\": \"Επιλέχθηκε {date}\",\n  \"finishRangeSelectionPrompt\": \"Κάντε κλικ για να ολοκληρώσετε την επιλογή εύρους ημερομηνιών\",\n  \"maximumDate\": \"Τελευταία διαθέσιμη ημερομηνία\",\n  \"minimumDate\": \"Πρώτη διαθέσιμη ημερομηνία\",\n  \"next\": \"Επόμενο\",\n  \"previous\": \"Προηγούμενο\",\n  \"selectedDateDescription\": \"Επιλεγμένη ημερομηνία: {date}\",\n  \"selectedRangeDescription\": \"Επιλεγμένο εύρος: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Κάντε κλικ για να ξεκινήσετε την επιλογή εύρους ημερομηνιών\",\n  \"todayDate\": \"Σήμερα, {date}\",\n  \"todayDateSelected\": \"Σήμερα, επιλέχτηκε {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,uBAAK,EAAE,KAAK,OAAO,EAAE;IAChF,gBAAgB,CAAC,OAAS,CAAC,uEAAW,EAAE,KAAK,IAAI,EAAE;IACnD,8BAA8B,CAAC,2XAA6D,CAAC;IAC7F,eAAe,CAAC,sMAA8B,CAAC;IAC/C,eAAe,CAAC,0KAA0B,CAAC;IAC3C,QAAQ,CAAC,iDAAO,CAAC;IACjB,YAAY,CAAC,6EAAW,CAAC;IACzB,2BAA2B,CAAC,OAAS,CAAC,+IAAuB,EAAE,KAAK,IAAI,EAAE;IAC1E,4BAA4B,CAAC,OAAS,CAAC,4GAAkB,EAAE,KAAK,SAAS,EAAE;IAC3E,6BAA6B,CAAC,6WAA2D,CAAC;IAC1F,aAAa,CAAC,OAAS,CAAC,4CAAQ,EAAE,KAAK,IAAI,EAAE;IAC7C,qBAAqB,CAAC,OAAS,CAAC,mHAAmB,EAAE,KAAK,IAAI,EAAE;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "file": "en-US.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/en-US.json"], "sourcesContent": ["{\n  \"previous\": \"Previous\",\n  \"next\": \"Next\",\n  \"selectedDateDescription\": \"Selected Date: {date}\",\n  \"selectedRangeDescription\": \"Selected Range: {dateRange}\",\n  \"todayDate\": \"Today, {date}\",\n  \"todayDateSelected\": \"Today, {date} selected\",\n  \"dateSelected\": \"{date} selected\",\n  \"startRangeSelectionPrompt\": \"Click to start selecting date range\",\n  \"finishRangeSelectionPrompt\": \"Click to finish selecting date range\",\n  \"minimumDate\": \"First available date\",\n  \"maximumDate\": \"Last available date\",\n  \"dateRange\": \"{startDate} to {endDate}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,YAAY,CAAC,QAAQ,CAAC;IACxC,QAAQ,CAAC,IAAI,CAAC;IACd,2BAA2B,CAAC,OAAS,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;IAClE,4BAA4B,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,SAAS,EAAE;IACzE,aAAa,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC;IAC7D,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;IACjD,6BAA6B,CAAC,mCAAmC,CAAC;IAClE,8BAA8B,CAAC,oCAAoC,CAAC;IACpE,eAAe,CAAC,oBAAoB,CAAC;IACrC,eAAe,CAAC,mBAAmB,CAAC;IACpC,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "file": "es-ES.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/es-ES.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} a {endDate}\",\n  \"dateSelected\": \"{date} seleccionado\",\n  \"finishRangeSelectionPrompt\": \"Haga clic para terminar de seleccionar rango de fechas\",\n  \"maximumDate\": \"Última fecha disponible\",\n  \"minimumDate\": \"Primera fecha disponible\",\n  \"next\": \"Siguiente\",\n  \"previous\": \"Anterior\",\n  \"selectedDateDescription\": \"Fecha seleccionada: {date}\",\n  \"selectedRangeDescription\": \"Intervalo seleccionado: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Haga clic para comenzar a seleccionar un rango de fechas\",\n  \"todayDate\": \"Hoy, {date}\",\n  \"todayDateSelected\": \"Hoy, {date} seleccionado\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;IACrD,8BAA8B,CAAC,sDAAsD,CAAC;IACtF,eAAe,CAAC,0BAAuB,CAAC;IACxC,eAAe,CAAC,wBAAwB,CAAC;IACzC,QAAQ,CAAC,SAAS,CAAC;IACnB,YAAY,CAAC,QAAQ,CAAC;IACtB,2BAA2B,CAAC,OAAS,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;IACvE,4BAA4B,CAAC,OAAS,CAAC,wBAAwB,EAAE,KAAK,SAAS,EAAE;IACjF,6BAA6B,CAAC,wDAAwD,CAAC;IACvF,aAAa,CAAC,OAAS,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE;IAC1C,qBAAqB,CAAC,OAAS,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "file": "et-EE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/et-EE.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} kuni {endDate}\",\n  \"dateSelected\": \"{date} valitud\",\n  \"finishRangeSelectionPrompt\": \"Klõpsake kuupäevavahemiku valimise lõpetamiseks\",\n  \"maximumDate\": \"<PERSON>iimane saadaolev kuupäev\",\n  \"minimumDate\": \"Esimene saadaolev kuupäev\",\n  \"next\": \"Järgmine\",\n  \"previous\": \"Eelmine\",\n  \"selectedDateDescription\": \"<PERSON>it<PERSON> kuupäev: {date}\",\n  \"selectedRangeDescription\": \"Valitud vahemik: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Klõpsake kuupäevavahemiku valimiseks\",\n  \"todayDate\": \"Täna, {date}\",\n  \"todayDateSelected\": \"Täna, {date} valitud\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,OAAO,EAAE;IACjF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC;IAChD,8BAA8B,CAAC,wDAA+C,CAAC;IAC/E,eAAe,CAAC,4BAAyB,CAAC;IAC1C,eAAe,CAAC,4BAAyB,CAAC;IAC1C,QAAQ,CAAC,WAAQ,CAAC;IAClB,YAAY,CAAC,OAAO,CAAC;IACrB,2BAA2B,CAAC,OAAS,CAAC,oBAAiB,EAAE,KAAK,IAAI,EAAE;IACpE,4BAA4B,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,SAAS,EAAE;IAC1E,6BAA6B,CAAC,0CAAoC,CAAC;IACnE,aAAa,CAAC,OAAS,CAAC,SAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,SAAM,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "file": "fi-FI.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/fi-FI.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} – {endDate}\",\n  \"dateSelected\": \"{date} valittu\",\n  \"finishRangeSelectionPrompt\": \"<PERSON>peta päivämääräalueen valinta napsauttamalla tätä.\",\n  \"maximumDate\": \"Viimeinen varattavissa oleva päivämäärä\",\n  \"minimumDate\": \"Ensimmäinen varattavissa oleva päivämäärä\",\n  \"next\": \"Seuraava\",\n  \"previous\": \"Edellinen\",\n  \"selectedDateDescription\": \"Valittu päivämäärä: {date}\",\n  \"selectedRangeDescription\": \"Valittu aikaväli: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Aloita päivämääräalueen valinta napsauttamalla tätä.\",\n  \"todayDate\": \"Tän<PERSON>än, {date}\",\n  \"todayDateSelected\": \"Tän<PERSON>än, {date} valittu\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,UAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC;IAChD,8BAA8B,CAAC,yEAAoD,CAAC;IACpF,eAAe,CAAC,sDAAuC,CAAC;IACxD,eAAe,CAAC,2DAAyC,CAAC;IAC1D,QAAQ,CAAC,QAAQ,CAAC;IAClB,YAAY,CAAC,SAAS,CAAC;IACvB,2BAA2B,CAAC,OAAS,CAAC,mCAAoB,EAAE,KAAK,IAAI,EAAE;IACvE,4BAA4B,CAAC,OAAS,CAAC,qBAAkB,EAAE,KAAK,SAAS,EAAE;IAC3E,6BAA6B,CAAC,yEAAoD,CAAC;IACnF,aAAa,CAAC,OAAS,CAAC,iBAAQ,EAAE,KAAK,IAAI,EAAE;IAC7C,qBAAqB,CAAC,OAAS,CAAC,iBAAQ,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "file": "fr-FR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/fr-FR.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} à {endDate}\",\n  \"dateSelected\": \"{date} sélectionné\",\n  \"finishRangeSelectionPrompt\": \"Cliquer pour finir de sélectionner la plage de dates\",\n  \"maximumDate\": \"Dernière date disponible\",\n  \"minimumDate\": \"Première date disponible\",\n  \"next\": \"Suivant\",\n  \"previous\": \"Précédent\",\n  \"selectedDateDescription\": \"Date sélectionnée : {date}\",\n  \"selectedRangeDescription\": \"Plage sélectionnée : {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Cliquer pour commencer à sélectionner la plage de dates\",\n  \"todayDate\": \"Aujourd'hui, {date}\",\n  \"todayDateSelected\": \"Aujourd’hui, {date} sélectionné\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,MAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,kBAAY,CAAC;IACpD,8BAA8B,CAAC,uDAAoD,CAAC;IACpF,eAAe,CAAC,2BAAwB,CAAC;IACzC,eAAe,CAAC,2BAAwB,CAAC;IACzC,QAAQ,CAAC,OAAO,CAAC;IACjB,YAAY,CAAC,eAAS,CAAC;IACvB,2BAA2B,CAAC,OAAS,CAAC,6BAAoB,EAAE,KAAK,IAAI,EAAE;IACvE,4BAA4B,CAAC,OAAS,CAAC,8BAAqB,EAAE,KAAK,SAAS,EAAE;IAC9E,6BAA6B,CAAC,6DAAuD,CAAC;IACtF,aAAa,CAAC,OAAS,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;IAClD,qBAAqB,CAAC,OAAS,CAAC,oBAAa,EAAE,KAAK,IAAI,CAAC,kBAAY,CAAC;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "file": "he-IL.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/he-IL.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} עד {endDate}\",\n  \"dateSelected\": \"{date} נבחר\",\n  \"finishRangeSelectionPrompt\": \"חץ כדי לסיים את בחירת טווח התאריכים\",\n  \"maximumDate\": \"תאריך פנוי אחרון\",\n  \"minimumDate\": \"תאריך פנוי ראשון\",\n  \"next\": \"הבא\",\n  \"previous\": \"הקודם\",\n  \"selectedDateDescription\": \"תאריך נבחר: {date}\",\n  \"selectedRangeDescription\": \"טווח נבחר: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"לחץ כדי להתחיל בבחירת טווח התאריכים\",\n  \"todayDate\": \"היום, {date}\",\n  \"todayDateSelected\": \"היום, {date} נבחר\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,gBAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,6BAAK,CAAC;IAC7C,8BAA8B,CAAC,iNAAmC,CAAC;IACnE,eAAe,CAAC,oGAAgB,CAAC;IACjC,eAAe,CAAC,oGAAgB,CAAC;IACjC,QAAQ,CAAC,qBAAG,CAAC;IACb,YAAY,CAAC,mCAAK,CAAC;IACnB,2BAA2B,CAAC,OAAS,CAAC,kEAAY,EAAE,KAAK,IAAI,EAAE;IAC/D,4BAA4B,CAAC,OAAS,CAAC,2DAAW,EAAE,KAAK,SAAS,EAAE;IACpE,6BAA6B,CAAC,uNAAmC,CAAC;IAClE,aAAa,CAAC,OAAS,CAAC,8BAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,8BAAM,EAAE,KAAK,IAAI,CAAC,6BAAK,CAAC;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "file": "hr-HR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/hr-HR.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} do {endDate}\",\n  \"dateSelected\": \"{date} odabran\",\n  \"finishRangeSelectionPrompt\": \"Kliknite da dovršite raspon odabranih datuma\",\n  \"maximumDate\": \"Posljednji raspoloživi datum\",\n  \"minimumDate\": \"Prvi raspoloživi datum\",\n  \"next\": \"Sljedeći\",\n  \"previous\": \"Prethodni\",\n  \"selectedDateDescription\": \"Odabrani datum: {date}\",\n  \"selectedRangeDescription\": \"Odabrani raspon: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Kliknite da započnete raspon odabranih datuma\",\n  \"todayDate\": \"Danas, {date}\",\n  \"todayDateSelected\": \"Danas, odabran {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC;IAChD,8BAA8B,CAAC,kDAA4C,CAAC;IAC5E,eAAe,CAAC,kCAA4B,CAAC;IAC7C,eAAe,CAAC,4BAAsB,CAAC;IACvC,QAAQ,CAAC,cAAQ,CAAC;IAClB,YAAY,CAAC,SAAS,CAAC;IACvB,2BAA2B,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;IACnE,4BAA4B,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,SAAS,EAAE;IAC1E,6BAA6B,CAAC,mDAA6C,CAAC;IAC5E,aAAa,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "file": "hu-HU.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/hu-HU.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate}–{endDate}\",\n  \"dateSelected\": \"{date} kiválasztva\",\n  \"finishRangeSelectionPrompt\": \"Kattintson a dátumtartomány kijelölésének befejezéséhez\",\n  \"maximumDate\": \"Utolsó elérhető dátum\",\n  \"minimumDate\": \"Az első elérhető dátum\",\n  \"next\": \"Következő\",\n  \"previous\": \"Előző\",\n  \"selectedDateDescription\": \"Kijelölt dátum: {date}\",\n  \"selectedRangeDescription\": \"Kijelölt tartomány: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Kattintson a dátumtartomány kijelölésének indításához\",\n  \"todayDate\": \"Ma, {date}\",\n  \"todayDateSelected\": \"Ma, {date} kijelölve\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,QAAC,EAAE,KAAK,OAAO,EAAE;IAC5E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,eAAY,CAAC;IACpD,8BAA8B,CAAC,4EAAuD,CAAC;IACvF,eAAe,CAAC,oCAAqB,CAAC;IACtC,eAAe,CAAC,wCAAsB,CAAC;IACvC,QAAQ,CAAC,kBAAS,CAAC;IACnB,YAAY,CAAC,iBAAK,CAAC;IACnB,2BAA2B,CAAC,OAAS,CAAC,sBAAgB,EAAE,KAAK,IAAI,EAAE;IACnE,4BAA4B,CAAC,OAAS,CAAC,0BAAoB,EAAE,KAAK,SAAS,EAAE;IAC7E,6BAA6B,CAAC,6EAAqD,CAAC;IACpF,aAAa,CAAC,OAAS,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;IACzC,qBAAqB,CAAC,OAAS,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,aAAU,CAAC;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "file": "it-IT.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/it-IT.json"], "sourcesContent": ["{\n  \"dateRange\": \"Da {startDate} a {endDate}\",\n  \"dateSelected\": \"{date} selezionata\",\n  \"finishRangeSelectionPrompt\": \"Fai clic per completare la selezione dell’intervallo di date\",\n  \"maximumDate\": \"Ultima data disponibile\",\n  \"minimumDate\": \"Prima data disponibile\",\n  \"next\": \"Successivo\",\n  \"previous\": \"Precedente\",\n  \"selectedDateDescription\": \"Data selezionata: {date}\",\n  \"selectedRangeDescription\": \"Intervallo selezionato: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Fai clic per selezionare l’intervallo di date\",\n  \"todayDate\": \"Oggi, {date}\",\n  \"todayDateSelected\": \"Oggi, {date} selezionata\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE;IACjF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;IACpD,8BAA8B,CAAC,mEAA4D,CAAC;IAC5F,eAAe,CAAC,uBAAuB,CAAC;IACxC,eAAe,CAAC,sBAAsB,CAAC;IACvC,QAAQ,CAAC,UAAU,CAAC;IACpB,YAAY,CAAC,UAAU,CAAC;IACxB,2BAA2B,CAAC,OAAS,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;IACrE,4BAA4B,CAAC,OAAS,CAAC,wBAAwB,EAAE,KAAK,SAAS,EAAE;IACjF,6BAA6B,CAAC,oDAA6C,CAAC;IAC5E,aAAa,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "file": "ja-JP.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/ja-JP.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} から {endDate}\",\n  \"dateSelected\": \"{date} を選択\",\n  \"finishRangeSelectionPrompt\": \"クリックして日付範囲の選択を終了\",\n  \"maximumDate\": \"最終利用可能日\",\n  \"minimumDate\": \"最初の利用可能日\",\n  \"next\": \"次へ\",\n  \"previous\": \"前へ\",\n  \"selectedDateDescription\": \"選択した日付 : {date}\",\n  \"selectedRangeDescription\": \"選択範囲 : {dateRange}\",\n  \"startRangeSelectionPrompt\": \"クリックして日付範囲の選択を開始\",\n  \"todayDate\": \"本日、{date}\",\n  \"todayDateSelected\": \"本日、{date} を選択\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,kBAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,yBAAI,CAAC;IAC5C,8BAA8B,CAAC,gIAAgB,CAAC;IAChD,eAAe,CAAC,wDAAO,CAAC;IACxB,eAAe,CAAC,gEAAQ,CAAC;IACzB,QAAQ,CAAC,gBAAE,CAAC;IACZ,YAAY,CAAC,gBAAE,CAAC;IAChB,2BAA2B,CAAC,OAAS,CAAC,mDAAS,EAAE,KAAK,IAAI,EAAE;IAC5D,4BAA4B,CAAC,OAAS,CAAC,mCAAO,EAAE,KAAK,SAAS,EAAE;IAChE,6BAA6B,CAAC,gIAAgB,CAAC;IAC/C,aAAa,CAAC,OAAS,CAAC,wBAAG,EAAE,KAAK,IAAI,EAAE;IACxC,qBAAqB,CAAC,OAAS,CAAC,wBAAG,EAAE,KAAK,IAAI,CAAC,yBAAI,CAAC;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "file": "ko-KR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/ko-KR.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} ~ {endDate}\",\n  \"dateSelected\": \"{date} 선택됨\",\n  \"finishRangeSelectionPrompt\": \"날짜 범위 선택을 완료하려면 클릭하십시오.\",\n  \"maximumDate\": \"마지막으로 사용 가능한 일자\",\n  \"minimumDate\": \"처음으로 사용 가능한 일자\",\n  \"next\": \"다음\",\n  \"previous\": \"이전\",\n  \"selectedDateDescription\": \"선택 일자: {date}\",\n  \"selectedRangeDescription\": \"선택 범위: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"날짜 범위 선택을 시작하려면 클릭하십시오.\",\n  \"todayDate\": \"오늘, {date}\",\n  \"todayDateSelected\": \"오늘, {date} 선택됨\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,yBAAI,CAAC;IAC5C,8BAA8B,CAAC,qJAAuB,CAAC;IACvD,eAAe,CAAC,mGAAe,CAAC;IAChC,eAAe,CAAC,2FAAc,CAAC;IAC/B,QAAQ,CAAC,gBAAE,CAAC;IACZ,YAAY,CAAC,gBAAE,CAAC;IAChB,2BAA2B,CAAC,OAAS,CAAC,mCAAO,EAAE,KAAK,IAAI,EAAE;IAC1D,4BAA4B,CAAC,OAAS,CAAC,mCAAO,EAAE,KAAK,SAAS,EAAE;IAChE,6BAA6B,CAAC,qJAAuB,CAAC;IACtD,aAAa,CAAC,OAAS,CAAC,kBAAI,EAAE,KAAK,IAAI,EAAE;IACzC,qBAAqB,CAAC,OAAS,CAAC,kBAAI,EAAE,KAAK,IAAI,CAAC,yBAAI,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "file": "lt-LT.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/lt-LT.json"], "sourcesContent": ["{\n  \"dateRange\": \"Nuo {startDate} iki {endDate}\",\n  \"dateSelected\": \"Pasirinkta {date}\",\n  \"finishRangeSelectionPrompt\": \"<PERSON>pustelėkite, kad baigtumėte pasirinkti datų intervalą\",\n  \"maximumDate\": \"Paskutinė galima data\",\n  \"minimumDate\": \"Pirmoji galima data\",\n  \"next\": \"Paskesnis\",\n  \"previous\": \"Ankstesnis\",\n  \"selectedDateDescription\": \"Pasirinkta data: {date}\",\n  \"selectedRangeDescription\": \"Pasirinktas intervalas: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Spustelėkite, kad pradėtumėte pasirinkti datų intervalą\",\n  \"todayDate\": \"Šiandien, {date}\",\n  \"todayDateSelected\": \"Šiandien, pasirinkta {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE;IACpF,gBAAgB,CAAC,OAAS,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;IACnD,8BAA8B,CAAC,8EAAsD,CAAC;IACtF,eAAe,CAAC,2BAAqB,CAAC;IACtC,eAAe,CAAC,mBAAmB,CAAC;IACpC,QAAQ,CAAC,SAAS,CAAC;IACnB,YAAY,CAAC,UAAU,CAAC;IACxB,2BAA2B,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;IACpE,4BAA4B,CAAC,OAAS,CAAC,wBAAwB,EAAE,KAAK,SAAS,EAAE;IACjF,6BAA6B,CAAC,qFAAuD,CAAC;IACtF,aAAa,CAAC,OAAS,CAAC,gBAAU,EAAE,KAAK,IAAI,EAAE;IAC/C,qBAAqB,CAAC,OAAS,CAAC,2BAAqB,EAAE,KAAK,IAAI,EAAE;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "file": "lv-LV.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/lv-LV.json"], "sourcesContent": ["{\n  \"dateRange\": \"No {startDate} līdz {endDate}\",\n  \"dateSelected\": \"Atlasīts: {date}\",\n  \"finishRangeSelectionPrompt\": \"Noklikšķiniet, lai pabeigtu datumu diapazona atlasi\",\n  \"maximumDate\": \"Pēdējais pieejamais datums\",\n  \"minimumDate\": \"Pirmais pieejamais datums\",\n  \"next\": \"Tālāk\",\n  \"previous\": \"Atpakaļ\",\n  \"selectedDateDescription\": \"Atlasītais datums: {date}\",\n  \"selectedRangeDescription\": \"Atlasītais diapazons: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Noklikšķiniet, lai sāktu datumu diapazona atlasi\",\n  \"todayDate\": \"Š<PERSON><PERSON>, {date}\",\n  \"todayDateSelected\": \"Atlasīta šodiena, {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,YAAM,EAAE,KAAK,OAAO,EAAE;IACpF,gBAAgB,CAAC,OAAS,CAAC,gBAAU,EAAE,KAAK,IAAI,EAAE;IAClD,8BAA8B,CAAC,+DAAmD,CAAC;IACnF,eAAe,CAAC,sCAA0B,CAAC;IAC3C,eAAe,CAAC,yBAAyB,CAAC;IAC1C,QAAQ,CAAC,iBAAK,CAAC;IACf,YAAY,CAAC,aAAO,CAAC;IACrB,2BAA2B,CAAC,OAAS,CAAC,yBAAmB,EAAE,KAAK,IAAI,EAAE;IACtE,4BAA4B,CAAC,OAAS,CAAC,4BAAsB,EAAE,KAAK,SAAS,EAAE;IAC/E,6BAA6B,CAAC,kEAAgD,CAAC;IAC/E,aAAa,CAAC,OAAS,CAAC,cAAQ,EAAE,KAAK,IAAI,EAAE;IAC7C,qBAAqB,CAAC,OAAS,CAAC,8BAAkB,EAAE,KAAK,IAAI,EAAE;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "file": "nb-NO.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/nb-NO.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} til {endDate}\",\n  \"dateSelected\": \"{date} valgt\",\n  \"finishRangeSelectionPrompt\": \"Klikk for å fullføre valg av datoområde\",\n  \"maximumDate\": \"Siste tilgjengelige dato\",\n  \"minimumDate\": \"Første tilgjengelige dato\",\n  \"next\": \"Neste\",\n  \"previous\": \"Forrige\",\n  \"selectedDateDescription\": \"Valgt dato: {date}\",\n  \"selectedRangeDescription\": \"Valgt område: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Klikk for å starte valg av datoområde\",\n  \"todayDate\": \"I dag, {date}\",\n  \"todayDateSelected\": \"I dag, {date} valgt\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE;IAChF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC;IAC9C,8BAA8B,CAAC,gDAAuC,CAAC;IACvE,eAAe,CAAC,wBAAwB,CAAC;IACzC,eAAe,CAAC,4BAAyB,CAAC;IAC1C,QAAQ,CAAC,KAAK,CAAC;IACf,YAAY,CAAC,OAAO,CAAC;IACrB,2BAA2B,CAAC,OAAS,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;IAC/D,4BAA4B,CAAC,OAAS,CAAC,iBAAc,EAAE,KAAK,SAAS,EAAE;IACvE,6BAA6B,CAAC,2CAAqC,CAAC;IACpE,aAAa,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "file": "nl-NL.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/nl-NL.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} tot {endDate}\",\n  \"dateSelected\": \"{date} geselecteerd\",\n  \"finishRangeSelectionPrompt\": \"Klik om de selectie van het datumbereik te voltooien\",\n  \"maximumDate\": \"Laatste beschikbare datum\",\n  \"minimumDate\": \"Eerste beschikbare datum\",\n  \"next\": \"Volgende\",\n  \"previous\": \"Vorige\",\n  \"selectedDateDescription\": \"Geselecteerde datum: {date}\",\n  \"selectedRangeDescription\": \"Geselecteerd bereik: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Klik om het datumbereik te selecteren\",\n  \"todayDate\": \"Vandaag, {date}\",\n  \"todayDateSelected\": \"Vandaag, {date} geselecteerd\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,KAAK,EAAE,KAAK,OAAO,EAAE;IAChF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;IACrD,8BAA8B,CAAC,oDAAoD,CAAC;IACpF,eAAe,CAAC,yBAAyB,CAAC;IAC1C,eAAe,CAAC,wBAAwB,CAAC;IACzC,QAAQ,CAAC,QAAQ,CAAC;IAClB,YAAY,CAAC,MAAM,CAAC;IACpB,2BAA2B,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;IACxE,4BAA4B,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,SAAS,EAAE;IAC9E,6BAA6B,CAAC,qCAAqC,CAAC;IACpE,aAAa,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;IAC9C,qBAAqB,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "file": "pl-PL.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/pl-PL.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} do {endDate}\",\n  \"dateSelected\": \"<PERSON><PERSON><PERSON><PERSON> {date}\",\n  \"finishRangeSelectionPrompt\": \"<PERSON><PERSON><PERSON><PERSON>, aby zako<PERSON><PERSON><PERSON><PERSON> wybór zakresu dat\",\n  \"maximumDate\": \"Ostatnia dostępna data\",\n  \"minimumDate\": \"Pierwsza dostępna data\",\n  \"next\": \"Dalej\",\n  \"previous\": \"Wstecz\",\n  \"selectedDateDescription\": \"Wybrana data: {date}\",\n  \"selectedRangeDescription\": \"Wybrany zakres: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"<PERSON><PERSON><PERSON><PERSON>, aby rozpo<PERSON>ć wybór zakresu dat\",\n  \"todayDate\": \"<PERSON><PERSON>sia<PERSON>, {date}\",\n  \"todayDateSelected\": \"<PERSON><PERSON>sia<PERSON> wybrano {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;IAChD,8BAA8B,CAAC,uDAAwC,CAAC;IACxE,eAAe,CAAC,4BAAsB,CAAC;IACvC,eAAe,CAAC,4BAAsB,CAAC;IACvC,QAAQ,CAAC,KAAK,CAAC;IACf,YAAY,CAAC,MAAM,CAAC;IACpB,2BAA2B,CAAC,OAAS,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;IACjE,4BAA4B,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,SAAS,EAAE;IACzE,6BAA6B,CAAC,uDAAwC,CAAC;IACvE,aAAa,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;IAC9C,qBAAqB,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "file": "pt-BR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/pt-BR.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} a {endDate}\",\n  \"dateSelected\": \"{date} selecionado\",\n  \"finishRangeSelectionPrompt\": \"Clique para concluir a seleção do intervalo de datas\",\n  \"maximumDate\": \"Última data disponível\",\n  \"minimumDate\": \"Primeira data disponível\",\n  \"next\": \"Próximo\",\n  \"previous\": \"Anterior\",\n  \"selectedDateDescription\": \"Data selecionada: {date}\",\n  \"selectedRangeDescription\": \"Intervalo selecionado: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Clique para iniciar a seleção do intervalo de datas\",\n  \"todayDate\": \"Hoje, {date}\",\n  \"todayDateSelected\": \"Ho<PERSON>, {date} selecionado\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;IACpD,8BAA8B,CAAC,0DAAoD,CAAC;IACpF,eAAe,CAAC,4BAAsB,CAAC;IACvC,eAAe,CAAC,2BAAwB,CAAC;IACzC,QAAQ,CAAC,UAAO,CAAC;IACjB,YAAY,CAAC,QAAQ,CAAC;IACtB,2BAA2B,CAAC,OAAS,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;IACrE,4BAA4B,CAAC,OAAS,CAAC,uBAAuB,EAAE,KAAK,SAAS,EAAE;IAChF,6BAA6B,CAAC,yDAAmD,CAAC;IAClF,aAAa,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "file": "pt-PT.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/pt-PT.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} a {endDate}\",\n  \"dateSelected\": \"{date} selecionado\",\n  \"finishRangeSelectionPrompt\": \"Clique para terminar de selecionar o intervalo de datas\",\n  \"maximumDate\": \"Última data disponível\",\n  \"minimumDate\": \"Primeira data disponível\",\n  \"next\": \"Próximo\",\n  \"previous\": \"Anterior\",\n  \"selectedDateDescription\": \"Data selecionada: {date}\",\n  \"selectedRangeDescription\": \"Intervalo selecionado: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Clique para começar a selecionar o intervalo de datas\",\n  \"todayDate\": \"Hoje, {date}\",\n  \"todayDateSelected\": \"Ho<PERSON>, {date} selecionado\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;IACpD,8BAA8B,CAAC,uDAAuD,CAAC;IACvF,eAAe,CAAC,4BAAsB,CAAC;IACvC,eAAe,CAAC,2BAAwB,CAAC;IACzC,QAAQ,CAAC,UAAO,CAAC;IACjB,YAAY,CAAC,QAAQ,CAAC;IACtB,2BAA2B,CAAC,OAAS,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;IACrE,4BAA4B,CAAC,OAAS,CAAC,uBAAuB,EAAE,KAAK,SAAS,EAAE;IAChF,6BAA6B,CAAC,wDAAqD,CAAC;IACpF,aAAa,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "file": "ro-RO.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/ro-RO.json"], "sourcesContent": ["{\n  \"dateRange\": \"De la {startDate} pân<PERSON> la {endDate}\",\n  \"dateSelected\": \"{date} selectată\",\n  \"finishRangeSelectionPrompt\": \"Apăsaţi pentru a finaliza selecţia razei pentru dată\",\n  \"maximumDate\": \"Ultima dată disponibilă\",\n  \"minimumDate\": \"Prima dată disponibilă\",\n  \"next\": \"Următorul\",\n  \"previous\": \"Înainte\",\n  \"selectedDateDescription\": \"Dată selectată: {date}\",\n  \"selectedRangeDescription\": \"Interval selectat: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Apăsaţi pentru a începe selecţia razei pentru dată\",\n  \"todayDate\": \"Ast<PERSON><PERSON>, {date}\",\n  \"todayDateSelected\": \"<PERSON><PERSON>, {date} selectată\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,kBAAS,EAAE,KAAK,OAAO,EAAE;IAC1F,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,gBAAU,CAAC;IAClD,8BAA8B,CAAC,4EAAoD,CAAC;IACpF,eAAe,CAAC,mCAAuB,CAAC;IACxC,eAAe,CAAC,kCAAsB,CAAC;IACvC,QAAQ,CAAC,eAAS,CAAC;IACnB,YAAY,CAAC,UAAO,CAAC;IACrB,2BAA2B,CAAC,OAAS,CAAC,4BAAgB,EAAE,KAAK,IAAI,EAAE;IACnE,4BAA4B,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,SAAS,EAAE;IAC5E,6BAA6B,CAAC,6EAAkD,CAAC;IACjF,aAAa,CAAC,OAAS,CAAC,cAAQ,EAAE,KAAK,IAAI,EAAE;IAC7C,qBAAqB,CAAC,OAAS,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,gBAAU,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "file": "ru-RU.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/ru-RU.json"], "sourcesContent": ["{\n  \"dateRange\": \"С {startDate} по {endDate}\",\n  \"dateSelected\": \"Выбрано {date}\",\n  \"finishRangeSelectionPrompt\": \"Щелкните, чтобы завершить выбор диапазона дат\",\n  \"maximumDate\": \"Последняя доступная дата\",\n  \"minimumDate\": \"Первая доступная дата\",\n  \"next\": \"Далее\",\n  \"previous\": \"Назад\",\n  \"selectedDateDescription\": \"Выбранная дата: {date}\",\n  \"selectedRangeDescription\": \"Выбранный диапазон: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Щелкните, чтобы начать выбор диапазона дат\",\n  \"todayDate\": \"Сегодня, {date}\",\n  \"todayDateSelected\": \"Сегодня, выбрано {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,CAAC,QAAE,EAAE,KAAK,SAAS,CAAC,gBAAI,EAAE,KAAK,OAAO,EAAE;IACjF,gBAAgB,CAAC,OAAS,CAAC,kDAAQ,EAAE,KAAK,IAAI,EAAE;IAChD,8BAA8B,CAAC,uRAA6C,CAAC;IAC7E,eAAe,CAAC,4JAAwB,CAAC;IACzC,eAAe,CAAC,uIAAqB,CAAC;IACtC,QAAQ,CAAC,mCAAK,CAAC;IACf,YAAY,CAAC,mCAAK,CAAC;IACnB,2BAA2B,CAAC,OAAS,CAAC,8FAAgB,EAAE,KAAK,IAAI,EAAE;IACnE,4BAA4B,CAAC,OAAS,CAAC,0HAAoB,EAAE,KAAK,SAAS,EAAE;IAC7E,6BAA6B,CAAC,kQAA0C,CAAC;IACzE,aAAa,CAAC,OAAS,CAAC,mDAAS,EAAE,KAAK,IAAI,EAAE;IAC9C,qBAAqB,CAAC,OAAS,CAAC,qGAAiB,EAAE,KAAK,IAAI,EAAE;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "file": "sk-SK.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/sk-SK.json"], "sourcesContent": ["{\n  \"dateRange\": \"Od {startDate} do {endDate}\",\n  \"dateSelected\": \"Vybratý dátum {date}\",\n  \"finishRangeSelectionPrompt\": \"Kliknutím dokončíte výber rozsahu dátumov\",\n  \"maximumDate\": \"Posledný dostupný dátum\",\n  \"minimumDate\": \"Prvý dostupný dátum\",\n  \"next\": \"Nasledujúce\",\n  \"previous\": \"Predchádzajúce\",\n  \"selectedDateDescription\": \"Vybratý dátum: {date}\",\n  \"selectedRangeDescription\": \"Vybratý rozsah: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Kliknutím spustíte výber rozsahu dátumov\",\n  \"todayDate\": \"Dnes {date}\",\n  \"todayDateSelected\": \"Vybratý dnešný dátum {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;IAClF,gBAAgB,CAAC,OAAS,CAAC,oBAAc,EAAE,KAAK,IAAI,EAAE;IACtD,8BAA8B,CAAC,2DAAyC,CAAC;IACzE,eAAe,CAAC,gCAAuB,CAAC;IACxC,eAAe,CAAC,4BAAmB,CAAC;IACpC,QAAQ,CAAC,cAAW,CAAC;IACrB,YAAY,CAAC,oBAAc,CAAC;IAC5B,2BAA2B,CAAC,OAAS,CAAC,qBAAe,EAAE,KAAK,IAAI,EAAE;IAClE,4BAA4B,CAAC,OAAS,CAAC,mBAAgB,EAAE,KAAK,SAAS,EAAE;IACzE,6BAA6B,CAAC,oDAAwC,CAAC;IACvE,aAAa,CAAC,OAAS,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE;IAC1C,qBAAqB,CAAC,OAAS,CAAC,oCAAqB,EAAE,KAAK,IAAI,EAAE;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "file": "sl-SI.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/sl-SI.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} do {endDate}\",\n  \"dateSelected\": \"{date} izbrano\",\n  \"finishRangeSelectionPrompt\": \"Kliknite za dokončanje izbire datumskega obsega\",\n  \"maximumDate\": \"Zadnji razpoložljivi datum\",\n  \"minimumDate\": \"Prvi razpoložljivi datum\",\n  \"next\": \"Naprej\",\n  \"previous\": \"Nazaj\",\n  \"selectedDateDescription\": \"Izbrani datum: {date}\",\n  \"selectedRangeDescription\": \"Izbrano območje: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Kliknite za začetek izbire datumskega obsega\",\n  \"todayDate\": \"<PERSON><PERSON>, {date}\",\n  \"todayDateSelected\": \"<PERSON><PERSON>, {date} izbrano\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC;IAChD,8BAA8B,CAAC,qDAA+C,CAAC;IAC/E,eAAe,CAAC,gCAA0B,CAAC;IAC3C,eAAe,CAAC,8BAAwB,CAAC;IACzC,QAAQ,CAAC,MAAM,CAAC;IAChB,YAAY,CAAC,KAAK,CAAC;IACnB,2BAA2B,CAAC,OAAS,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;IAClE,4BAA4B,CAAC,OAAS,CAAC,uBAAiB,EAAE,KAAK,SAAS,EAAE;IAC1E,6BAA6B,CAAC,kDAA4C,CAAC;IAC3E,aAAa,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "file": "sr-SP.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/sr-SP.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} do {endDate}\",\n  \"dateSelected\": \"{date} izabran\",\n  \"finishRangeSelectionPrompt\": \"Kliknite da dovršite opseg izabranih datuma\",\n  \"maximumDate\": \"Zadnji raspoloživi datum\",\n  \"minimumDate\": \"Prvi raspoloživi datum\",\n  \"next\": \"Sledeći\",\n  \"previous\": \"Prethodni\",\n  \"selectedDateDescription\": \"Izabrani datum: {date}\",\n  \"selectedRangeDescription\": \"Izabrani period: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Kliknite da započnete opseg izabranih datuma\",\n  \"todayDate\": \"<PERSON><PERSON>, {date}\",\n  \"todayDateSelected\": \"<PERSON>s, izabran {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;IAC/E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC;IAChD,8BAA8B,CAAC,iDAA2C,CAAC;IAC3E,eAAe,CAAC,8BAAwB,CAAC;IACzC,eAAe,CAAC,4BAAsB,CAAC;IACvC,QAAQ,CAAC,aAAO,CAAC;IACjB,YAAY,CAAC,SAAS,CAAC;IACvB,2BAA2B,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;IACnE,4BAA4B,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,SAAS,EAAE;IAC1E,6BAA6B,CAAC,kDAA4C,CAAC;IAC3E,aAAa,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "file": "sv-SE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/sv-SE.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} till {endDate}\",\n  \"dateSelected\": \"{date} har valts\",\n  \"finishRangeSelectionPrompt\": \"<PERSON><PERSON><PERSON> för att avsluta val av datumintervall\",\n  \"maximumDate\": \"Sista tillgängliga datum\",\n  \"minimumDate\": \"Första tillgängliga datum\",\n  \"next\": \"Nästa\",\n  \"previous\": \"Föregående\",\n  \"selectedDateDescription\": \"Valt datum: {date}\",\n  \"selectedRangeDescription\": \"Valt intervall: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"<PERSON><PERSON>a för att välja datumintervall\",\n  \"todayDate\": \"Idag, {date}\",\n  \"todayDateSelected\": \"Idag, {date} har valts\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,OAAO,EAAE;IACjF,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;IAClD,8BAA8B,CAAC,+CAA4C,CAAC;IAC5E,eAAe,CAAC,2BAAwB,CAAC;IACzC,eAAe,CAAC,+BAAyB,CAAC;IAC1C,QAAQ,CAAC,QAAK,CAAC;IACf,YAAY,CAAC,gBAAU,CAAC;IACxB,2BAA2B,CAAC,OAAS,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;IAC/D,4BAA4B,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,SAAS,EAAE;IACzE,6BAA6B,CAAC,yCAAmC,CAAC;IAClE,aAAa,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;IAC3C,qBAAqB,CAAC,OAAS,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "file": "tr-TR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/tr-TR.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} - {endDate}\",\n  \"dateSelected\": \"{date} seçildi\",\n  \"finishRangeSelectionPrompt\": \"<PERSON><PERSON><PERSON> aralığı seçimini tamamlamak için tıklayın\",\n  \"maximumDate\": \"Son müsait tarih\",\n  \"minimumDate\": \"İlk müsait tarih\",\n  \"next\": \"Sonraki\",\n  \"previous\": \"Önceki\",\n  \"selectedDateDescription\": \"Seçilen Tarih: {date}\",\n  \"selectedRangeDescription\": \"Seçilen Aralık: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"<PERSON><PERSON>h aralığı seçimini başlatmak için tıklayın\",\n  \"todayDate\": \"<PERSON><PERSON>ü<PERSON>, {date}\",\n  \"todayDateSelected\": \"<PERSON><PERSON>ü<PERSON>, {date} seçildi\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,WAAQ,CAAC;IAChD,8BAA8B,CAAC,mFAA+C,CAAC;IAC/E,eAAe,CAAC,mBAAgB,CAAC;IACjC,eAAe,CAAC,yBAAgB,CAAC;IACjC,QAAQ,CAAC,OAAO,CAAC;IACjB,YAAY,CAAC,SAAM,CAAC;IACpB,2BAA2B,CAAC,OAAS,CAAC,kBAAe,EAAE,KAAK,IAAI,EAAE;IAClE,4BAA4B,CAAC,OAAS,CAAC,yBAAgB,EAAE,KAAK,SAAS,EAAE;IACzE,6BAA6B,CAAC,wFAA8C,CAAC;IAC7E,aAAa,CAAC,OAAS,CAAC,UAAO,EAAE,KAAK,IAAI,EAAE;IAC5C,qBAAqB,CAAC,OAAS,CAAC,UAAO,EAAE,KAAK,IAAI,CAAC,WAAQ,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "file": "uk-UA.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/uk-UA.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} — {endDate}\",\n  \"dateSelected\": \"Вибрано {date}\",\n  \"finishRangeSelectionPrompt\": \"Натисніть, щоб завершити вибір діапазону дат\",\n  \"maximumDate\": \"Остання доступна дата\",\n  \"minimumDate\": \"Перша доступна дата\",\n  \"next\": \"Наступний\",\n  \"previous\": \"Попередній\",\n  \"selectedDateDescription\": \"Вибрана дата: {date}\",\n  \"selectedRangeDescription\": \"Вибраний діапазон: {dateRange}\",\n  \"startRangeSelectionPrompt\": \"Натисніть, щоб почати вибір діапазону дат\",\n  \"todayDate\": \"Сьогодн<PERSON>, {date}\",\n  \"todayDateSelected\": \"Сьогодні, вибрано {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,UAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,CAAC,kDAAQ,EAAE,KAAK,IAAI,EAAE;IAChD,8BAA8B,CAAC,gRAA4C,CAAC;IAC5E,eAAe,CAAC,uIAAqB,CAAC;IACtC,eAAe,CAAC,yHAAmB,CAAC;IACpC,QAAQ,CAAC,+DAAS,CAAC;IACnB,YAAY,CAAC,sEAAU,CAAC;IACxB,2BAA2B,CAAC,OAAS,CAAC,gFAAc,EAAE,KAAK,IAAI,EAAE;IACjE,4BAA4B,CAAC,OAAS,CAAC,mHAAmB,EAAE,KAAK,SAAS,EAAE;IAC5E,6BAA6B,CAAC,2PAAyC,CAAC;IACxE,aAAa,CAAC,OAAS,CAAC,0DAAU,EAAE,KAAK,IAAI,EAAE;IAC/C,qBAAqB,CAAC,OAAS,CAAC,4GAAkB,EAAE,KAAK,IAAI,EAAE;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "file": "zh-CN.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/zh-CN.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} 至 {endDate}\",\n  \"dateSelected\": \"已选择 {date}\",\n  \"finishRangeSelectionPrompt\": \"单击以完成选择日期范围\",\n  \"maximumDate\": \"最后一个可用日期\",\n  \"minimumDate\": \"第一个可用日期\",\n  \"next\": \"下一页\",\n  \"previous\": \"上一页\",\n  \"selectedDateDescription\": \"选定的日期：{date}\",\n  \"selectedRangeDescription\": \"选定的范围：{dateRange}\",\n  \"startRangeSelectionPrompt\": \"单击以开始选择日期范围\",\n  \"todayDate\": \"今天，即 {date}\",\n  \"todayDateSelected\": \"已选择今天，即 {date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,UAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,CAAC,yBAAI,EAAE,KAAK,IAAI,EAAE;IAC5C,8BAA8B,CAAC,wFAAW,CAAC;IAC3C,eAAe,CAAC,gEAAQ,CAAC;IACzB,eAAe,CAAC,wDAAO,CAAC;IACxB,QAAQ,CAAC,wBAAG,CAAC;IACb,YAAY,CAAC,wBAAG,CAAC;IACjB,2BAA2B,CAAC,OAAS,CAAC,gDAAM,EAAE,KAAK,IAAI,EAAE;IACzD,4BAA4B,CAAC,OAAS,CAAC,gDAAM,EAAE,KAAK,SAAS,EAAE;IAC/D,6BAA6B,CAAC,wFAAW,CAAC;IAC1C,aAAa,CAAC,OAAS,CAAC,iCAAK,EAAE,KAAK,IAAI,EAAE;IAC1C,qBAAqB,CAAC,OAAS,CAAC,yDAAQ,EAAE,KAAK,IAAI,EAAE;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "file": "zh-TW.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/intl/zh-TW.json"], "sourcesContent": ["{\n  \"dateRange\": \"{startDate} 至 {endDate}\",\n  \"dateSelected\": \"已選取 {date}\",\n  \"finishRangeSelectionPrompt\": \"按一下以完成選取日期範圍\",\n  \"maximumDate\": \"最後一個可用日期\",\n  \"minimumDate\": \"第一個可用日期\",\n  \"next\": \"下一頁\",\n  \"previous\": \"上一頁\",\n  \"selectedDateDescription\": \"選定的日期：{date}\",\n  \"selectedRangeDescription\": \"選定的範圍：{dateRange}\",\n  \"startRangeSelectionPrompt\": \"按一下以開始選取日期範圍\",\n  \"todayDate\": \"今天，{date}\",\n  \"todayDateSelected\": \"已選取今天，{date}\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,UAAG,EAAE,KAAK,OAAO,EAAE;IAC9E,gBAAgB,CAAC,OAAS,CAAC,yBAAI,EAAE,KAAK,IAAI,EAAE;IAC5C,8BAA8B,CAAC,gGAAY,CAAC;IAC5C,eAAe,CAAC,gEAAQ,CAAC;IACzB,eAAe,CAAC,wDAAO,CAAC;IACxB,QAAQ,CAAC,wBAAG,CAAC;IACb,YAAY,CAAC,wBAAG,CAAC;IACjB,2BAA2B,CAAC,OAAS,CAAC,gDAAM,EAAE,KAAK,IAAI,EAAE;IACzD,4BAA4B,CAAC,OAAS,CAAC,gDAAM,EAAE,KAAK,SAAS,EAAE;IAC/D,6BAA6B,CAAC,gGAAY,CAAC;IAC3C,aAAa,CAAC,OAAS,CAAC,wBAAG,EAAE,KAAK,IAAI,EAAE;IACxC,qBAAqB,CAAC,OAAS,CAAC,gDAAM,EAAE,KAAK,IAAI,EAAE;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "file": "intlStrings.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/src/%2A.js"], "sourcesContent": ["const _temp0 = require(\"../intl/ar-AE.json\");\nconst _temp1 = require(\"../intl/bg-BG.json\");\nconst _temp2 = require(\"../intl/cs-CZ.json\");\nconst _temp3 = require(\"../intl/da-DK.json\");\nconst _temp4 = require(\"../intl/de-DE.json\");\nconst _temp5 = require(\"../intl/el-GR.json\");\nconst _temp6 = require(\"../intl/en-US.json\");\nconst _temp7 = require(\"../intl/es-ES.json\");\nconst _temp8 = require(\"../intl/et-EE.json\");\nconst _temp9 = require(\"../intl/fi-FI.json\");\nconst _temp10 = require(\"../intl/fr-FR.json\");\nconst _temp11 = require(\"../intl/he-IL.json\");\nconst _temp12 = require(\"../intl/hr-HR.json\");\nconst _temp13 = require(\"../intl/hu-HU.json\");\nconst _temp14 = require(\"../intl/it-IT.json\");\nconst _temp15 = require(\"../intl/ja-JP.json\");\nconst _temp16 = require(\"../intl/ko-KR.json\");\nconst _temp17 = require(\"../intl/lt-LT.json\");\nconst _temp18 = require(\"../intl/lv-LV.json\");\nconst _temp19 = require(\"../intl/nb-NO.json\");\nconst _temp20 = require(\"../intl/nl-NL.json\");\nconst _temp21 = require(\"../intl/pl-PL.json\");\nconst _temp22 = require(\"../intl/pt-BR.json\");\nconst _temp23 = require(\"../intl/pt-PT.json\");\nconst _temp24 = require(\"../intl/ro-RO.json\");\nconst _temp25 = require(\"../intl/ru-RU.json\");\nconst _temp26 = require(\"../intl/sk-SK.json\");\nconst _temp27 = require(\"../intl/sl-SI.json\");\nconst _temp28 = require(\"../intl/sr-SP.json\");\nconst _temp29 = require(\"../intl/sv-SE.json\");\nconst _temp30 = require(\"../intl/tr-TR.json\");\nconst _temp31 = require(\"../intl/uk-UA.json\");\nconst _temp32 = require(\"../intl/zh-CN.json\");\nconst _temp33 = require(\"../intl/zh-TW.json\");\nmodule.exports = {\n  \"ar-AE\": _temp0,\n  \"bg-BG\": _temp1,\n  \"cs-CZ\": _temp2,\n  \"da-DK\": _temp3,\n  \"de-DE\": _temp4,\n  \"el-GR\": _temp5,\n  \"en-US\": _temp6,\n  \"es-ES\": _temp7,\n  \"et-EE\": _temp8,\n  \"fi-FI\": _temp9,\n  \"fr-FR\": _temp10,\n  \"he-IL\": _temp11,\n  \"hr-HR\": _temp12,\n  \"hu-HU\": _temp13,\n  \"it-IT\": _temp14,\n  \"ja-JP\": _temp15,\n  \"ko-KR\": _temp16,\n  \"lt-LT\": _temp17,\n  \"lv-LV\": _temp18,\n  \"nb-NO\": _temp19,\n  \"nl-NL\": _temp20,\n  \"pl-PL\": _temp21,\n  \"pt-BR\": _temp22,\n  \"pt-PT\": _temp23,\n  \"ro-RO\": _temp24,\n  \"ru-RU\": _temp25,\n  \"sk-SK\": _temp26,\n  \"sl-SI\": _temp27,\n  \"sr-SP\": _temp28,\n  \"sv-SE\": _temp29,\n  \"tr-TR\": _temp30,\n  \"uk-UA\": _temp31,\n  \"zh-CN\": _temp32,\n  \"zh-TW\": _temp33\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,4BAAiB;IACf,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CalendarDate, DateFormatter, endOfMonth, isSameDay, startOfMonth} from '@internationalized/date';\nimport {CalendarState, RangeCalendarState} from '@react-stately/calendar';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport type {LocalizedStringFormatter} from '@internationalized/string';\nimport {useDateFormatter, useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useMemo} from 'react';\n\ninterface HookData {\n  ariaLabel?: string,\n  ariaLabelledBy?: string,\n  errorMessageId: string,\n  selectedDateDescription: string\n}\n\nexport const hookData: WeakMap<CalendarState | RangeCalendarState, HookData> = new WeakMap<CalendarState | RangeCalendarState, HookData>();\n\nexport function getEraFormat(date: CalendarDate | undefined): 'short' | undefined {\n  return date?.calendar.identifier === 'gregory' && date.era === 'BC' ? 'short' : undefined;\n}\n\nexport function useSelectedDateDescription(state: CalendarState | RangeCalendarState): string {\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/calendar');\n\n  let start: CalendarDate | undefined, end: CalendarDate | undefined;\n  if ('highlightedRange' in state) {\n    ({start, end} = state.highlightedRange || {});\n  } else {\n    start = end = state.value ?? undefined;\n  }\n\n  let dateFormatter = useDateFormatter({\n    weekday: 'long',\n    month: 'long',\n    year: 'numeric',\n    day: 'numeric',\n    era: getEraFormat(start) || getEraFormat(end),\n    timeZone: state.timeZone\n  });\n\n  let anchorDate = 'anchorDate' in state ? state.anchorDate : null;\n  return useMemo(() => {\n    // No message if currently selecting a range, or there is nothing highlighted.\n    if (!anchorDate && start && end) {\n      // Use a single date message if the start and end dates are the same day,\n      // otherwise include both dates.\n      if (isSameDay(start, end)) {\n        let date = dateFormatter.format(start.toDate(state.timeZone));\n        return stringFormatter.format('selectedDateDescription', {date});\n      } else {\n        let dateRange = formatRange(dateFormatter, stringFormatter, start, end, state.timeZone);\n\n        return stringFormatter.format('selectedRangeDescription', {dateRange});\n      }\n    }\n    return '';\n  }, [start, end, anchorDate, state.timeZone, stringFormatter, dateFormatter]);\n}\n\nexport function useVisibleRangeDescription(startDate: CalendarDate, endDate: CalendarDate, timeZone: string, isAria: boolean): string {\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/calendar');\n  let era: any = getEraFormat(startDate) || getEraFormat(endDate);\n  let monthFormatter = useDateFormatter({\n    month: 'long',\n    year: 'numeric',\n    era,\n    calendar: startDate.calendar.identifier,\n    timeZone\n  });\n\n  let dateFormatter = useDateFormatter({\n    month: 'long',\n    year: 'numeric',\n    day: 'numeric',\n    era,\n    calendar: startDate.calendar.identifier,\n    timeZone\n  });\n\n  return useMemo(() => {\n    // Special case for month granularity. Format as a single month if only a\n    // single month is visible, otherwise format as a range of months.\n    if (isSameDay(startDate, startOfMonth(startDate))) {\n      let startMonth = startDate;\n      let endMonth = endDate;\n      if (startDate.calendar.getFormattableMonth) {\n        startMonth = startDate.calendar.getFormattableMonth(startDate);\n      }\n      if (endDate.calendar.getFormattableMonth) {\n        endMonth = endDate.calendar.getFormattableMonth(endDate);\n      }\n\n      if (isSameDay(endDate, endOfMonth(startDate))) {\n        return monthFormatter.format(startMonth.toDate(timeZone));\n      } else if (isSameDay(endDate, endOfMonth(endDate))) {\n        return isAria\n          ? formatRange(monthFormatter, stringFormatter, startMonth, endMonth, timeZone)\n          : monthFormatter.formatRange(startMonth.toDate(timeZone), endMonth.toDate(timeZone));\n      }\n    }\n\n    return isAria\n      ? formatRange(dateFormatter, stringFormatter, startDate, endDate, timeZone)\n      : dateFormatter.formatRange(startDate.toDate(timeZone), endDate.toDate(timeZone));\n  }, [startDate, endDate, monthFormatter, dateFormatter, stringFormatter, timeZone, isAria]);\n}\n\nfunction formatRange(dateFormatter: DateFormatter, stringFormatter: LocalizedStringFormatter, start: CalendarDate, end: CalendarDate, timeZone: string) {\n  let parts = dateFormatter.formatRangeToParts(start.toDate(timeZone), end.toDate(timeZone));\n\n  // Find the separator between the start and end date. This is determined\n  // by finding the last shared literal before the end range.\n  let separatorIndex = -1;\n  for (let i = 0; i < parts.length; i++) {\n    let part = parts[i];\n    if (part.source === 'shared' && part.type === 'literal') {\n      separatorIndex = i;\n    } else if (part.source === 'endRange') {\n      break;\n    }\n  }\n\n  // Now we can combine the parts into start and end strings.\n  let startValue = '';\n  let endValue = '';\n  for (let i = 0; i < parts.length; i++) {\n    if (i < separatorIndex) {\n      startValue += parts[i].value;\n    } else if (i > separatorIndex) {\n      endValue += parts[i].value;\n    }\n  }\n\n  return stringFormatter.format('dateRange', {startDate: startValue, endDate: endValue});\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAiBM,MAAM,4CAAkE,IAAI;AAE5E,SAAS,0CAAa,IAA8B;IACzD,OAAO,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,CAAC,UAAU,MAAK,aAAa,KAAK,GAAG,KAAK,OAAO,UAAU;AAClF;AAEO,SAAS,0CAA2B,KAAyC;IAClF,IAAI,kBAAkB,CAAA,sLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,sKAAA,CAAA,UAAA,CAAW,GAAG;IAEhE,IAAI,OAAiC;QAIrB;IAHhB,IAAI,sBAAsB,OACvB,CAAA,EAAA,OAAC,KAAK,EAAA,KAAE,GAAG,EAAC,GAAG,MAAM,gBAAgB,IAAI,CAAC,CAAA;SAE3C,QAAQ,MAAM,CAAA,eAAA,MAAM,KAAK,MAAA,QAAX,iBAAA,KAAA,IAAA,eAAe;IAG/B,IAAI,gBAAgB,CAAA,2KAAA,mBAAe,EAAE;QACnC,SAAS;QACT,OAAO;QACP,MAAM;QACN,KAAK;QACL,KAAK,0CAAa,UAAU,0CAAa;QACzC,UAAU,MAAM,QAAQ;IAC1B;IAEA,IAAI,aAAa,gBAAgB,QAAQ,MAAM,UAAU,GAAG;IAC5D,OAAO,CAAA,iKAAA,UAAM,EAAE;QACb,8EAA8E;QAC9E,IAAI,CAAC,cAAc,SAAS,KAAA;YAC1B,yEAAyE;YACzE,gCAAgC;YAChC,IAAI,CAAA,sKAAA,YAAQ,EAAE,OAAO,MAAM;gBACzB,IAAI,OAAO,cAAc,MAAM,CAAC,MAAM,MAAM,CAAC,MAAM,QAAQ;gBAC3D,OAAO,gBAAgB,MAAM,CAAC,2BAA2B;0BAAC;gBAAI;YAChE,OAAO;gBACL,IAAI,YAAY,kCAAY,eAAe,iBAAiB,OAAO,KAAK,MAAM,QAAQ;gBAEtF,OAAO,gBAAgB,MAAM,CAAC,4BAA4B;+BAAC;gBAAS;YACtE;;QAEF,OAAO;IACT,GAAG;QAAC;QAAO;QAAK;QAAY,MAAM,QAAQ;QAAE;QAAiB;KAAc;AAC7E;AAEO,SAAS,yCAA2B,SAAuB,EAAE,OAAqB,EAAE,QAAgB,EAAE,MAAe;IAC1H,IAAI,kBAAkB,CAAA,sLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,sKAAA,CAAA,UAAA,CAAW,GAAG;IAChE,IAAI,MAAW,0CAAa,cAAc,0CAAa;IACvD,IAAI,iBAAiB,CAAA,2KAAA,mBAAe,EAAE;QACpC,OAAO;QACP,MAAM;aACN;QACA,UAAU,UAAU,QAAQ,CAAC,UAAU;kBACvC;IACF;IAEA,IAAI,gBAAgB,CAAA,2KAAA,mBAAe,EAAE;QACnC,OAAO;QACP,MAAM;QACN,KAAK;aACL;QACA,UAAU,UAAU,QAAQ,CAAC,UAAU;kBACvC;IACF;IAEA,OAAO,CAAA,iKAAA,UAAM,EAAE;QACb,yEAAyE;QACzE,kEAAkE;QAClE,IAAI,CAAA,sKAAA,YAAQ,EAAE,WAAW,CAAA,sKAAA,eAAW,EAAE,aAAa;YACjD,IAAI,aAAa;YACjB,IAAI,WAAW;YACf,IAAI,UAAU,QAAQ,CAAC,mBAAmB,EACxC,aAAa,UAAU,QAAQ,CAAC,mBAAmB,CAAC;YAEtD,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,EACtC,WAAW,QAAQ,QAAQ,CAAC,mBAAmB,CAAC;YAGlD,IAAI,CAAA,sKAAA,YAAQ,EAAE,SAAS,CAAA,sKAAA,aAAS,EAAE,aAChC,OAAO,eAAe,MAAM,CAAC,WAAW,MAAM,CAAC;iBAC1C,IAAI,CAAA,sKAAA,YAAQ,EAAE,SAAS,CAAA,sKAAA,aAAS,EAAE,WACvC,OAAO,SACH,kCAAY,gBAAgB,iBAAiB,YAAY,UAAU,YACnE,eAAe,WAAW,CAAC,WAAW,MAAM,CAAC,WAAW,SAAS,MAAM,CAAC;QAEhF;QAEA,OAAO,SACH,kCAAY,eAAe,iBAAiB,WAAW,SAAS,YAChE,cAAc,WAAW,CAAC,UAAU,MAAM,CAAC,WAAW,QAAQ,MAAM,CAAC;IAC3E,GAAG;QAAC;QAAW;QAAS;QAAgB;QAAe;QAAiB;QAAU;KAAO;AAC3F;AAEA,SAAS,kCAAY,aAA4B,EAAE,eAAyC,EAAE,KAAmB,EAAE,GAAiB,EAAE,QAAgB;IACpJ,IAAI,QAAQ,cAAc,kBAAkB,CAAC,MAAM,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC;IAEhF,wEAAwE;IACxE,2DAA2D;IAC3D,IAAI,iBAAiB,CAAA;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,OAAO,KAAK,CAAC,EAAE;QACnB,IAAI,KAAK,MAAM,KAAK,YAAY,KAAK,IAAI,KAAK,WAC5C,iBAAiB;aACZ,IAAI,KAAK,MAAM,KAAK,YACzB;IAEJ;IAEA,2DAA2D;IAC3D,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,IAAI,gBACN,cAAc,KAAK,CAAC,EAAE,CAAC,KAAK;aACvB,IAAI,IAAI,gBACb,YAAY,KAAK,CAAC,EAAE,CAAC,KAAK;IAE9B;IAEA,OAAO,gBAAgB,MAAM,CAAC,aAAa;QAAC,WAAW;QAAY,SAAS;IAAQ;AACtF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "file": "useCalendarBase.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/src/useCalendarBase.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {announce} from '@react-aria/live-announcer';\nimport {AriaButtonProps} from '@react-types/button';\nimport {AriaLabelingProps, DOMAttributes, DOMProps} from '@react-types/shared';\nimport {CalendarPropsBase} from '@react-types/calendar';\nimport {CalendarState, RangeCalendarState} from '@react-stately/calendar';\nimport {filterDOMProps, mergeProps, useLabels, useSlotId, useUpdateEffect} from '@react-aria/utils';\nimport {hookData, useSelectedDateDescription, useVisibleRangeDescription} from './utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useState} from 'react';\n\nexport interface CalendarAria {\n  /** Props for the calendar grouping element. */\n  calendarProps: DOMAttributes,\n  /** Props for the next button. */\n  nextButtonProps: AriaButtonProps,\n  /** Props for the previous button. */\n  prevButtonProps: AriaButtonProps,\n  /** Props for the error message element, if any. */\n  errorMessageProps: DOMAttributes,\n  /** A description of the visible date range, for use in the calendar title. */\n  title: string\n}\n\nexport function useCalendarBase(props: CalendarPropsBase & DOMProps & AriaLabelingProps, state: CalendarState | RangeCalendarState): CalendarAria {\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/calendar');\n  let domProps = filterDOMProps(props);\n\n  let title = useVisibleRangeDescription(state.visibleRange.start, state.visibleRange.end, state.timeZone, false);\n  let visibleRangeDescription = useVisibleRangeDescription(state.visibleRange.start, state.visibleRange.end, state.timeZone, true);\n\n  // Announce when the visible date range changes\n  useUpdateEffect(() => {\n    // only when pressing the Previous or Next button\n    if (!state.isFocused) {\n      announce(visibleRangeDescription);\n    }\n  }, [visibleRangeDescription]);\n\n  // Announce when the selected value changes\n  let selectedDateDescription = useSelectedDateDescription(state);\n  useUpdateEffect(() => {\n    if (selectedDateDescription) {\n      announce(selectedDateDescription, 'polite', 4000);\n    }\n    // handle an update to the caption that describes the currently selected range, to announce the new value\n  }, [selectedDateDescription]);\n\n  let errorMessageId = useSlotId([Boolean(props.errorMessage), props.isInvalid, props.validationState]);\n\n  // Pass the label to the child grid elements.\n  hookData.set(state, {\n    ariaLabel: props['aria-label'],\n    ariaLabelledBy: props['aria-labelledby'],\n    errorMessageId,\n    selectedDateDescription\n  });\n\n  // If the next or previous buttons become disabled while they are focused, move focus to the calendar body.\n  let [nextFocused, setNextFocused] = useState(false);\n  let nextDisabled = props.isDisabled || state.isNextVisibleRangeInvalid();\n  if (nextDisabled && nextFocused) {\n    setNextFocused(false);\n    state.setFocused(true);\n  }\n\n  let [previousFocused, setPreviousFocused] = useState(false);\n  let previousDisabled = props.isDisabled || state.isPreviousVisibleRangeInvalid();\n  if (previousDisabled && previousFocused) {\n    setPreviousFocused(false);\n    state.setFocused(true);\n  }\n\n  let labelProps = useLabels({\n    id: props['id'],\n    'aria-label': [props['aria-label'], visibleRangeDescription].filter(Boolean).join(', '),\n    'aria-labelledby': props['aria-labelledby']\n  });\n\n  return {\n    calendarProps: mergeProps(domProps, labelProps, {\n      role: 'application',\n      'aria-details': props['aria-details'] || undefined,\n      'aria-describedby': props['aria-describedby'] || undefined\n    }),\n    nextButtonProps: {\n      onPress: () => state.focusNextPage(),\n      'aria-label': stringFormatter.format('next'),\n      isDisabled: nextDisabled,\n      onFocusChange: setNextFocused\n    },\n    prevButtonProps: {\n      onPress: () => state.focusPreviousPage(),\n      'aria-label': stringFormatter.format('previous'),\n      isDisabled: previousDisabled,\n      onFocusChange: setPreviousFocused\n    },\n    errorMessageProps: {\n      id: errorMessageId\n    },\n    title\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA2BM,SAAS,0CAAgB,KAAuD,EAAE,KAAyC;IAChI,IAAI,kBAAkB,CAAA,sLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,sKAAA,CAAA,UAAA,CAAW,GAAG;IAChE,IAAI,WAAW,CAAA,0KAAA,iBAAa,EAAE;IAE9B,IAAI,QAAQ,CAAA,oKAAA,6BAAyB,EAAE,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM,YAAY,CAAC,GAAG,EAAE,MAAM,QAAQ,EAAE;IACzG,IAAI,0BAA0B,CAAA,oKAAA,6BAAyB,EAAE,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM,YAAY,CAAC,GAAG,EAAE,MAAM,QAAQ,EAAE;IAE3H,+CAA+C;IAC/C,CAAA,2KAAA,kBAAc,EAAE;QACd,iDAAiD;QACjD,IAAI,CAAC,MAAM,SAAS,EAClB,CAAA,qLAAA,WAAO,EAAE;IAEb,GAAG;QAAC;KAAwB;IAE5B,2CAA2C;IAC3C,IAAI,0BAA0B,CAAA,oKAAA,6BAAyB,EAAE;IACzD,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,yBACF,CAAA,qLAAA,WAAO,EAAE,yBAAyB,UAAU;IAE9C,yGAAyG;IAC3G,GAAG;QAAC;KAAwB;IAE5B,IAAI,iBAAiB,CAAA,iKAAA,YAAQ,EAAE;QAAC,QAAQ,MAAM,YAAY;QAAG,MAAM,SAAS;QAAE,MAAM,eAAe;KAAC;IAEpG,6CAA6C;IAC7C,CAAA,oKAAA,WAAO,EAAE,GAAG,CAAC,OAAO;QAClB,WAAW,KAAK,CAAC,aAAa;QAC9B,gBAAgB,KAAK,CAAC,kBAAkB;wBACxC;iCACA;IACF;IAEA,2GAA2G;IAC3G,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,iKAAA,WAAO,EAAE;IAC7C,IAAI,eAAe,MAAM,UAAU,IAAI,MAAM,yBAAyB;IACtE,IAAI,gBAAgB,aAAa;QAC/B,eAAe;QACf,MAAM,UAAU,CAAC;IACnB;IAEA,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,iKAAA,WAAO,EAAE;IACrD,IAAI,mBAAmB,MAAM,UAAU,IAAI,MAAM,6BAA6B;IAC9E,IAAI,oBAAoB,iBAAiB;QACvC,mBAAmB;QACnB,MAAM,UAAU,CAAC;IACnB;IAEA,IAAI,aAAa,CAAA,qKAAA,YAAQ,EAAE;QACzB,IAAI,KAAK,CAAC,KAAK;QACf,cAAc;YAAC,KAAK,CAAC,aAAa;YAAE;SAAwB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QAClF,mBAAmB,KAAK,CAAC,kBAAkB;IAC7C;IAEA,OAAO;QACL,eAAe,CAAA,sKAAA,aAAS,EAAE,UAAU,YAAY;YAC9C,MAAM;YACN,gBAAgB,KAAK,CAAC,eAAe,IAAI;YACzC,oBAAoB,KAAK,CAAC,mBAAmB,IAAI;QACnD;QACA,iBAAiB;YACf,SAAS,IAAM,MAAM,aAAa;YAClC,cAAc,gBAAgB,MAAM,CAAC;YACrC,YAAY;YACZ,eAAe;QACjB;QACA,iBAAiB;YACf,SAAS,IAAM,MAAM,iBAAiB;YACtC,cAAc,gBAAgB,MAAM,CAAC;YACrC,YAAY;YACZ,eAAe;QACjB;QACA,mBAAmB;YACjB,IAAI;QACN;eACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "file": "useCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/src/useCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaCalendarProps, DateValue} from '@react-types/calendar';\nimport {CalendarAria, useCalendarBase} from './useCalendarBase';\nimport {CalendarState} from '@react-stately/calendar';\n\n/**\n * Provides the behavior and accessibility implementation for a calendar component.\n * A calendar displays one or more date grids and allows users to select a single date.\n */\nexport function useCalendar<T extends DateValue>(props: AriaCalendarProps<T>, state: CalendarState): CalendarAria {\n  return useCalendarBase(props, state);\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAUM,SAAS,0CAAiC,KAA2B,EAAE,KAAoB;IAChG,OAAO,CAAA,8KAAA,kBAAc,EAAE,OAAO;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "file": "useCalendarCell.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/src/useCalendarCell.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CalendarDate, isEqualDay, isSameDay, isToday} from '@internationalized/date';\nimport {CalendarState, RangeCalendarState} from '@react-stately/calendar';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {focusWithoutScrolling, getScrollParent, mergeProps, scrollIntoViewport, useDeepMemo, useDescription} from '@react-aria/utils';\nimport {getEraFormat, hookData} from './utils';\nimport {getInteractionModality, usePress} from '@react-aria/interactions';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {useDateFormatter, useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useEffect, useMemo, useRef} from 'react';\n\nexport interface AriaCalendarCellProps {\n  /** The date that this cell represents. */\n  date: CalendarDate,\n  /**\n   * Whether the cell is disabled. By default, this is determined by the\n   * Calendar's `minValue`, `maxValue`, and `isDisabled` props.\n   */\n  isDisabled?: boolean,\n\n  /**\n   * Whether the cell is outside of the current month.\n   */\n  isOutsideMonth?: boolean\n}\n\nexport interface CalendarCellAria {\n  /** Props for the grid cell element (e.g. `<td>`). */\n  cellProps: DOMAttributes,\n  /** Props for the button element within the cell. */\n  buttonProps: DOMAttributes,\n  /** Whether the cell is currently being pressed. */\n  isPressed: boolean,\n  /** Whether the cell is selected. */\n  isSelected: boolean,\n  /** Whether the cell is focused. */\n  isFocused: boolean,\n  /**\n   * Whether the cell is disabled, according to the calendar's `minValue`, `maxValue`, and `isDisabled` props.\n   * Disabled dates are not focusable, and cannot be selected by the user. They are typically\n   * displayed with a dimmed appearance.\n   */\n  isDisabled: boolean,\n  /**\n   * Whether the cell is unavailable, according to the calendar's `isDateUnavailable` prop. Unavailable dates remain\n   * focusable, but cannot be selected by the user. They should be displayed with a visual affordance to indicate they\n   * are unavailable, such as a different color or a strikethrough.\n   *\n   * Note that because they are focusable, unavailable dates must meet a 4.5:1 color contrast ratio,\n   * [as defined by WCAG](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html).\n   */\n  isUnavailable: boolean,\n  /**\n   * Whether the cell is outside the visible range of the calendar.\n   * For example, dates before the first day of a month in the same week.\n   */\n  isOutsideVisibleRange: boolean,\n  /** Whether the cell is part of an invalid selection. */\n  isInvalid: boolean,\n  /** The day number formatted according to the current locale. */\n  formattedDate: string\n}\n\n/**\n * Provides the behavior and accessibility implementation for a calendar cell component.\n * A calendar cell displays a date cell within a calendar grid which can be selected by the user.\n */\nexport function useCalendarCell(props: AriaCalendarCellProps, state: CalendarState | RangeCalendarState, ref: RefObject<HTMLElement | null>): CalendarCellAria {\n  let {date, isDisabled} = props;\n  let {errorMessageId, selectedDateDescription} = hookData.get(state)!;\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/calendar');\n  let dateFormatter = useDateFormatter({\n    weekday: 'long',\n    day: 'numeric',\n    month: 'long',\n    year: 'numeric',\n    era: getEraFormat(date),\n    timeZone: state.timeZone\n  });\n  let isSelected = state.isSelected(date);\n  let isFocused = state.isCellFocused(date) && !props.isOutsideMonth;\n  isDisabled = isDisabled || state.isCellDisabled(date);\n  let isUnavailable = state.isCellUnavailable(date);\n  let isSelectable = !isDisabled && !isUnavailable;\n  let isInvalid = state.isValueInvalid && Boolean(\n    'highlightedRange' in state\n      ? !state.anchorDate && state.highlightedRange && date.compare(state.highlightedRange.start) >= 0 && date.compare(state.highlightedRange.end) <= 0\n      : state.value && isSameDay(state.value, date)\n  );\n\n  if (isInvalid) {\n    isSelected = true;\n  }\n\n  // For performance, reuse the same date object as before if the new date prop is the same.\n  // This allows subsequent useMemo results to be reused.\n  date = useDeepMemo<CalendarDate>(date, isEqualDay);\n  let nativeDate = useMemo(() => date.toDate(state.timeZone), [date, state.timeZone]);\n\n  // aria-label should be localize Day of week, Month, Day and Year without Time.\n  let isDateToday = isToday(date, state.timeZone);\n  let label = useMemo(() => {\n    let label = '';\n\n    // If this is a range calendar, add a description of the full selected range\n    // to the first and last selected date.\n    if (\n      'highlightedRange' in state &&\n      state.value &&\n      !state.anchorDate &&\n      (isSameDay(date, state.value.start) || isSameDay(date, state.value.end))\n    ) {\n      label = selectedDateDescription + ', ';\n    }\n\n    label += dateFormatter.format(nativeDate);\n    if (isDateToday) {\n      // If date is today, set appropriate string depending on selected state:\n      label = stringFormatter.format(isSelected ? 'todayDateSelected' : 'todayDate', {\n        date: label\n      });\n    } else if (isSelected) {\n      // If date is selected but not today:\n      label = stringFormatter.format('dateSelected', {\n        date: label\n      });\n    }\n\n    if (state.minValue && isSameDay(date, state.minValue)) {\n      label += ', ' + stringFormatter.format('minimumDate');\n    } else if (state.maxValue && isSameDay(date, state.maxValue)) {\n      label += ', ' + stringFormatter.format('maximumDate');\n    }\n\n    return label;\n  }, [dateFormatter, nativeDate, stringFormatter, isSelected, isDateToday, date, state, selectedDateDescription]);\n\n  // When a cell is focused and this is a range calendar, add a prompt to help\n  // screenreader users know that they are in a range selection mode.\n  let rangeSelectionPrompt = '';\n  if ('anchorDate' in state && isFocused && !state.isReadOnly && isSelectable) {\n    // If selection has started add \"click to finish selecting range\"\n    if (state.anchorDate) {\n      rangeSelectionPrompt = stringFormatter.format('finishRangeSelectionPrompt');\n    // Otherwise, add \"click to start selecting range\" prompt\n    } else {\n      rangeSelectionPrompt = stringFormatter.format('startRangeSelectionPrompt');\n    }\n  }\n\n  let descriptionProps = useDescription(rangeSelectionPrompt);\n\n  let isAnchorPressed = useRef(false);\n  let isRangeBoundaryPressed = useRef(false);\n  let touchDragTimerRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);\n  let {pressProps, isPressed} = usePress({\n    // When dragging to select a range, we don't want dragging over the original anchor\n    // again to trigger onPressStart. Cancel presses immediately when the pointer exits.\n    shouldCancelOnPointerExit: 'anchorDate' in state && !!state.anchorDate,\n    preventFocusOnPress: true,\n    isDisabled: !isSelectable || state.isReadOnly,\n    onPressStart(e) {\n      if (state.isReadOnly) {\n        state.setFocusedDate(date);\n        return;\n      }\n\n      if ('highlightedRange' in state && !state.anchorDate && (e.pointerType === 'mouse' || e.pointerType === 'touch')) {\n        // Allow dragging the start or end date of a range to modify it\n        // rather than starting a new selection.\n        // Don't allow dragging when invalid, or weird jumping behavior may occur as date ranges\n        // are constrained to available dates. The user will need to select a new range in this case.\n        if (state.highlightedRange && !isInvalid) {\n          if (isSameDay(date, state.highlightedRange.start)) {\n            state.setAnchorDate(state.highlightedRange.end);\n            state.setFocusedDate(date);\n            state.setDragging(true);\n            isRangeBoundaryPressed.current = true;\n            return;\n          } else if (isSameDay(date, state.highlightedRange.end)) {\n            state.setAnchorDate(state.highlightedRange.start);\n            state.setFocusedDate(date);\n            state.setDragging(true);\n            isRangeBoundaryPressed.current = true;\n            return;\n          }\n        }\n\n        let startDragging = () => {\n          state.setDragging(true);\n          touchDragTimerRef.current = undefined;\n\n          state.selectDate(date);\n          state.setFocusedDate(date);\n          isAnchorPressed.current = true;\n        };\n\n        // Start selection on mouse/touch down so users can drag to select a range.\n        // On touch, delay dragging to determine if the user really meant to scroll.\n        if (e.pointerType === 'touch') {\n          touchDragTimerRef.current = setTimeout(startDragging, 200);\n        } else {\n          startDragging();\n        }\n      }\n    },\n    onPressEnd() {\n      isRangeBoundaryPressed.current = false;\n      isAnchorPressed.current = false;\n      clearTimeout(touchDragTimerRef.current);\n      touchDragTimerRef.current = undefined;\n    },\n    onPress() {\n      // For non-range selection, always select on press up.\n      if (!('anchorDate' in state) && !state.isReadOnly) {\n        state.selectDate(date);\n        state.setFocusedDate(date);\n      }\n    },\n    onPressUp(e) {\n      if (state.isReadOnly) {\n        return;\n      }\n\n      // If the user tapped quickly, the date won't be selected yet and the\n      // timer will still be in progress. In this case, select the date on touch up.\n      // Timer is cleared in onPressEnd.\n      if ('anchorDate' in state && touchDragTimerRef.current) {\n        state.selectDate(date);\n        state.setFocusedDate(date);\n      }\n\n      if ('anchorDate' in state) {\n        if (isRangeBoundaryPressed.current) {\n          // When clicking on the start or end date of an already selected range,\n          // start a new selection on press up to also allow dragging the date to\n          // change the existing range.\n          state.setAnchorDate(date);\n        } else if (state.anchorDate && !isAnchorPressed.current) {\n          // When releasing a drag or pressing the end date of a range, select it.\n          state.selectDate(date);\n          state.setFocusedDate(date);\n        } else if (e.pointerType === 'keyboard' && !state.anchorDate) {\n          // For range selection, auto-advance the focused date by one if using keyboard.\n          // This gives an indication that you're selecting a range rather than a single date.\n          // For mouse, this is unnecessary because users will see the indication on hover. For screen readers,\n          // there will be an announcement to \"click to finish selecting range\" (above).\n          state.selectDate(date);\n          let nextDay = date.add({days: 1});\n          if (state.isInvalid(nextDay)) {\n            nextDay = date.subtract({days: 1});\n          }\n          if (!state.isInvalid(nextDay)) {\n            state.setFocusedDate(nextDay);\n          }\n        } else if (e.pointerType === 'virtual') {\n          // For screen readers, just select the date on click.\n          state.selectDate(date);\n          state.setFocusedDate(date);\n        }\n      }\n    }\n  });\n\n  let tabIndex: number | undefined = undefined;\n  if (!isDisabled) {\n    tabIndex = isSameDay(date, state.focusedDate) ? 0 : -1;\n  }\n\n  // Focus the button in the DOM when the state updates.\n  useEffect(() => {\n    if (isFocused && ref.current) {\n      focusWithoutScrolling(ref.current);\n\n      // Scroll into view if navigating with a keyboard, otherwise\n      // try not to shift the view under the user's mouse/finger.\n      // If in a overlay, scrollIntoViewport will only cause scrolling\n      // up to the overlay scroll body to prevent overlay shifting.\n      // Also only scroll into view if the cell actually got focused.\n      // There are some cases where the cell might be disabled or inside,\n      // an inert container and we don't want to scroll then.\n      if (getInteractionModality() !== 'pointer' && document.activeElement === ref.current) {\n        scrollIntoViewport(ref.current, {containingElement: getScrollParent(ref.current)});\n      }\n    }\n  }, [isFocused, ref]);\n\n  let cellDateFormatter = useDateFormatter({\n    day: 'numeric',\n    timeZone: state.timeZone,\n    calendar: date.calendar.identifier\n  });\n\n  let formattedDate = useMemo(() => cellDateFormatter.formatToParts(nativeDate).find(part => part.type === 'day')!.value, [cellDateFormatter, nativeDate]);\n\n  return {\n    cellProps: {\n      role: 'gridcell',\n      'aria-disabled': !isSelectable || undefined,\n      'aria-selected': isSelected || undefined,\n      'aria-invalid': isInvalid || undefined\n    },\n    buttonProps: mergeProps(pressProps, {\n      onFocus() {\n        if (!isDisabled) {\n          state.setFocusedDate(date);\n        }\n      },\n      tabIndex,\n      role: 'button',\n      'aria-disabled': !isSelectable || undefined,\n      'aria-label': label,\n      'aria-invalid': isInvalid || undefined,\n      'aria-describedby': [\n        isInvalid ? errorMessageId : undefined,\n        descriptionProps['aria-describedby']\n      ].filter(Boolean).join(' ') || undefined,\n      onPointerEnter(e) {\n        // Highlight the date on hover or drag over a date when selecting a range.\n        if ('highlightDate' in state && (e.pointerType !== 'touch' || state.isDragging) && isSelectable) {\n          state.highlightDate(date);\n        }\n      },\n      onPointerDown(e) {\n        // This is necessary on touch devices to allow dragging\n        // outside the original pressed element.\n        // (JSDOM does not support this)\n        if ('releasePointerCapture' in e.target) {\n          e.target.releasePointerCapture(e.pointerId);\n        }\n      },\n      onContextMenu(e) {\n        // Prevent context menu on long press.\n        e.preventDefault();\n      }\n    }),\n    isPressed,\n    isFocused,\n    isSelected,\n    isDisabled,\n    isUnavailable,\n    isOutsideVisibleRange: date.compare(state.visibleRange.start) < 0 || date.compare(state.visibleRange.end) > 0,\n    isInvalid,\n    formattedDate\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAqEM,SAAS,0CAAgB,KAA4B,EAAE,KAAyC,EAAE,GAAkC;IACzI,IAAI,EAAA,MAAC,IAAI,EAAA,YAAE,UAAU,EAAC,GAAG;IACzB,IAAI,EAAA,gBAAC,cAAc,EAAA,yBAAE,uBAAuB,EAAC,GAAG,CAAA,oKAAA,WAAO,EAAE,GAAG,CAAC;IAC7D,IAAI,kBAAkB,CAAA,sLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,sKAAA,CAAA,UAAA,CAAW,GAAG;IAChE,IAAI,gBAAgB,CAAA,2KAAA,mBAAe,EAAE;QACnC,SAAS;QACT,KAAK;QACL,OAAO;QACP,MAAM;QACN,KAAK,CAAA,oKAAA,eAAW,EAAE;QAClB,UAAU,MAAM,QAAQ;IAC1B;IACA,IAAI,aAAa,MAAM,UAAU,CAAC;IAClC,IAAI,YAAY,MAAM,aAAa,CAAC,SAAS,CAAC,MAAM,cAAc;IAClE,aAAa,cAAc,MAAM,cAAc,CAAC;IAChD,IAAI,gBAAgB,MAAM,iBAAiB,CAAC;IAC5C,IAAI,eAAe,CAAC,cAAc,CAAC;IACnC,IAAI,YAAY,MAAM,cAAc,IAAI,QACtC,sBAAsB,QAClB,CAAC,MAAM,UAAU,IAAI,MAAM,gBAAgB,IAAI,KAAK,OAAO,CAAC,MAAM,gBAAgB,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,CAAC,MAAM,gBAAgB,CAAC,GAAG,KAAK,IAC9I,MAAM,KAAK,IAAI,CAAA,sKAAA,YAAQ,EAAE,MAAM,KAAK,EAAE;IAG5C,IAAI,WACF,aAAa;IAGf,0FAA0F;IAC1F,uDAAuD;IACvD,OAAO,CAAA,uKAAA,cAAU,EAAgB,MAAM,CAAA,sKAAA,aAAS;IAChD,IAAI,aAAa,CAAA,iKAAA,UAAM,EAAE,IAAM,KAAK,MAAM,CAAC,MAAM,QAAQ,GAAG;QAAC;QAAM,MAAM,QAAQ;KAAC;IAElF,+EAA+E;IAC/E,IAAI,cAAc,CAAA,sKAAA,UAAM,EAAE,MAAM,MAAM,QAAQ;IAC9C,IAAI,QAAQ,CAAA,iKAAA,UAAM,EAAE;QAClB,IAAI,QAAQ;QAEZ,4EAA4E;QAC5E,uCAAuC;QACvC,IACE,sBAAsB,SACtB,MAAM,KAAK,IACX,CAAC,MAAM,UAAU,IAChB,CAAA,CAAA,sKAAA,YAAQ,EAAE,MAAM,MAAM,KAAK,CAAC,KAAK,KAAK,CAAA,sKAAA,YAAQ,EAAE,MAAM,MAAM,KAAK,CAAC,GAAG,CAAA,GAEtE,QAAQ,0BAA0B;QAGpC,SAAS,cAAc,MAAM,CAAC;QAC9B,IAAI,aAEF,AADA,QACQ,gBAAgB,MAAM,CAAC,aAAa,sBAAsB,MADM,OACO;YAC7E,MAAM;QACR;aACK,IAAI,YAET,AADA,QACQ,gBAAgB,MAAM,CAAC,MADM,UACU;YAC7C,MAAM;QACR;QAGF,IAAI,MAAM,QAAQ,IAAI,CAAA,sKAAA,YAAQ,EAAE,MAAM,MAAM,QAAQ,GAClD,SAAS,OAAO,gBAAgB,MAAM,CAAC;aAClC,IAAI,MAAM,QAAQ,IAAI,CAAA,sKAAA,YAAQ,EAAE,MAAM,MAAM,QAAQ,GACzD,SAAS,OAAO,gBAAgB,MAAM,CAAC;QAGzC,OAAO;IACT,GAAG;QAAC;QAAe;QAAY;QAAiB;QAAY;QAAa;QAAM;QAAO;KAAwB;IAE9G,4EAA4E;IAC5E,mEAAmE;IACnE,IAAI,uBAAuB;IAC3B,IAAI,gBAAgB,SAAS,aAAa,CAAC,MAAM,UAAU,IAAI,cAAA;QAC7D,iEAAiE;QACjE,IAAI,MAAM,UAAU,EAClB,uBAAuB,gBAAgB,MAAM,CAAC;aAG9C,uBAAuB,gBAAgB,MAAM,CAAC;;IAIlD,IAAI,mBAAmB,CAAA,0KAAA,iBAAa,EAAE;IAEtC,IAAI,kBAAkB,CAAA,iKAAA,SAAK,EAAE;IAC7B,IAAI,yBAAyB,CAAA,iKAAA,SAAK,EAAE;IACpC,IAAI,oBAAoB,CAAA,iKAAA,SAAK,EAA6C;IAC1E,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;QACrC,mFAAmF;QACnF,oFAAoF;QACpF,2BAA2B,gBAAgB,SAAS,CAAC,CAAC,MAAM,UAAU;QACtE,qBAAqB;QACrB,YAAY,CAAC,gBAAgB,MAAM,UAAU;QAC7C,cAAa,CAAC;YACZ,IAAI,MAAM,UAAU,EAAE;gBACpB,MAAM,cAAc,CAAC;gBACrB;YACF;YAEA,IAAI,sBAAsB,SAAS,CAAC,MAAM,UAAU,IAAK,CAAA,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,OAAM,GAAI;gBAChH,+DAA+D;gBAC/D,wCAAwC;gBACxC,wFAAwF;gBACxF,6FAA6F;gBAC7F,IAAI,MAAM,gBAAgB,IAAI,CAAC,WAAW;oBACxC,IAAI,CAAA,sKAAA,YAAQ,EAAE,MAAM,MAAM,gBAAgB,CAAC,KAAK,GAAG;wBACjD,MAAM,aAAa,CAAC,MAAM,gBAAgB,CAAC,GAAG;wBAC9C,MAAM,cAAc,CAAC;wBACrB,MAAM,WAAW,CAAC;wBAClB,uBAAuB,OAAO,GAAG;wBACjC;oBACF,OAAO,IAAI,CAAA,sKAAA,YAAQ,EAAE,MAAM,MAAM,gBAAgB,CAAC,GAAG,GAAG;wBACtD,MAAM,aAAa,CAAC,MAAM,gBAAgB,CAAC,KAAK;wBAChD,MAAM,cAAc,CAAC;wBACrB,MAAM,WAAW,CAAC;wBAClB,uBAAuB,OAAO,GAAG;wBACjC;oBACF;gBACF;gBAEA,IAAI,gBAAgB;oBAClB,MAAM,WAAW,CAAC;oBAClB,kBAAkB,OAAO,GAAG;oBAE5B,MAAM,UAAU,CAAC;oBACjB,MAAM,cAAc,CAAC;oBACrB,gBAAgB,OAAO,GAAG;gBAC5B;gBAEA,2EAA2E;gBAC3E,4EAA4E;gBAC5E,IAAI,EAAE,WAAW,KAAK,SACpB,kBAAkB,OAAO,GAAG,WAAW,eAAe;qBAEtD;YAEJ;QACF;QACA;YACE,uBAAuB,OAAO,GAAG;YACjC,gBAAgB,OAAO,GAAG;YAC1B,aAAa,kBAAkB,OAAO;YACtC,kBAAkB,OAAO,GAAG;QAC9B;QACA;YACE,sDAAsD;YACtD,IAAI,CAAE,CAAA,gBAAgB,KAAI,KAAM,CAAC,MAAM,UAAU,EAAE;gBACjD,MAAM,UAAU,CAAC;gBACjB,MAAM,cAAc,CAAC;YACvB;QACF;QACA,WAAU,CAAC;YACT,IAAI,MAAM,UAAU,EAClB;YAGF,qEAAqE;YACrE,8EAA8E;YAC9E,kCAAkC;YAClC,IAAI,gBAAgB,SAAS,kBAAkB,OAAO,EAAE;gBACtD,MAAM,UAAU,CAAC;gBACjB,MAAM,cAAc,CAAC;YACvB;YAEA,IAAI,gBAAgB,OAAO;gBACzB,IAAI,uBAAuB,OAAO,EAChC,AACA,uEADuE,AACA;gBACvE,6BAA6B;gBAC7B,MAAM,aAAa,CAAC;qBACf,IAAI,MAAM,UAAU,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBACvD,wEAAwE;oBACxE,MAAM,UAAU,CAAC;oBACjB,MAAM,cAAc,CAAC;gBACvB,OAAO,IAAI,EAAE,WAAW,KAAK,cAAc,CAAC,MAAM,UAAU,EAAE;oBAC5D,+EAA+E;oBAC/E,oFAAoF;oBACpF,qGAAqG;oBACrG,8EAA8E;oBAC9E,MAAM,UAAU,CAAC;oBACjB,IAAI,UAAU,KAAK,GAAG,CAAC;wBAAC,MAAM;oBAAC;oBAC/B,IAAI,MAAM,SAAS,CAAC,UAClB,UAAU,KAAK,QAAQ,CAAC;wBAAC,MAAM;oBAAC;oBAElC,IAAI,CAAC,MAAM,SAAS,CAAC,UACnB,MAAM,cAAc,CAAC;gBAEzB,OAAO,IAAI,EAAE,WAAW,KAAK,WAAW;oBACtC,qDAAqD;oBACrD,MAAM,UAAU,CAAC;oBACjB,MAAM,cAAc,CAAC;gBACvB;YACF;QACF;IACF;IAEA,IAAI,WAA+B;IACnC,IAAI,CAAC,YACH,WAAW,CAAA,sKAAA,YAAQ,EAAE,MAAM,MAAM,WAAW,IAAI,IAAI,CAAA;IAGtD,sDAAsD;IACtD,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,aAAa,IAAI,OAAO,EAAE;YAC5B,CAAA,iLAAA,wBAAoB,EAAE,IAAI,OAAO;YAEjC,4DAA4D;YAC5D,2DAA2D;YAC3D,gEAAgE;YAChE,6DAA6D;YAC7D,+DAA+D;YAC/D,mEAAmE;YACnE,uDAAuD;YACvD,IAAI,CAAA,kLAAA,yBAAqB,QAAQ,aAAa,SAAS,aAAa,KAAK,IAAI,OAAO,EAClF,CAAA,0KAAA,qBAAiB,EAAE,IAAI,OAAO,EAAE;gBAAC,mBAAmB,CAAA,2KAAA,kBAAc,EAAE,IAAI,OAAO;YAAC;QAEpF;IACF,GAAG;QAAC;QAAW;KAAI;IAEnB,IAAI,oBAAoB,CAAA,2KAAA,mBAAe,EAAE;QACvC,KAAK;QACL,UAAU,MAAM,QAAQ;QACxB,UAAU,KAAK,QAAQ,CAAC,UAAU;IACpC;IAEA,IAAI,gBAAgB,CAAA,iKAAA,UAAM,EAAE,IAAM,kBAAkB,aAAa,CAAC,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAQ,KAAK,EAAE;QAAC;QAAmB;KAAW;IAEvJ,OAAO;QACL,WAAW;YACT,MAAM;YACN,iBAAiB,CAAC,gBAAgB;YAClC,iBAAiB,cAAc;YAC/B,gBAAgB,aAAa;QAC/B;QACA,aAAa,CAAA,sKAAA,aAAS,EAAE,YAAY;YAClC;gBACE,IAAI,CAAC,YACH,MAAM,cAAc,CAAC;YAEzB;sBACA;YACA,MAAM;YACN,iBAAiB,CAAC,gBAAgB;YAClC,cAAc;YACd,gBAAgB,aAAa;YAC7B,oBAAoB;gBAClB,YAAY,iBAAiB;gBAC7B,gBAAgB,CAAC,mBAAmB;aACrC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;YAC/B,gBAAe,CAAC;gBACd,0EAA0E;gBAC1E,IAAI,mBAAmB,SAAU,CAAA,EAAE,WAAW,KAAK,WAAW,MAAM,UAAS,KAAM,cACjF,MAAM,aAAa,CAAC;YAExB;YACA,eAAc,CAAC;gBACb,uDAAuD;gBACvD,wCAAwC;gBACxC,gCAAgC;gBAChC,IAAI,2BAA2B,EAAE,MAAM,EACrC,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE,SAAS;YAE9C;YACA,eAAc,CAAC;gBACb,sCAAsC;gBACtC,EAAE,cAAc;YAClB;QACF;mBACA;mBACA;oBACA;oBACA;uBACA;QACA,uBAAuB,KAAK,OAAO,CAAC,MAAM,YAAY,CAAC,KAAK,IAAI,KAAK,KAAK,OAAO,CAAC,MAAM,YAAY,CAAC,GAAG,IAAI;mBAC5G;uBACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1574, "column": 0}, "map": {"version": 3, "file": "useCalendarGrid.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/src/useCalendarGrid.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CalendarDate, getWeeksInMonth, startOfWeek, today} from '@internationalized/date';\nimport {CalendarState, RangeCalendarState} from '@react-stately/calendar';\nimport {DOMAttributes} from '@react-types/shared';\nimport {hookData, useVisibleRangeDescription} from './utils';\nimport {KeyboardEvent, useMemo} from 'react';\nimport {mergeProps, useLabels} from '@react-aria/utils';\nimport {useDateFormatter, useLocale} from '@react-aria/i18n';\n\nexport interface AriaCalendarGridProps {\n  /**\n   * The first date displayed in the calendar grid.\n   * Defaults to the first visible date in the calendar.\n   * Override this to display multiple date grids in a calendar.\n   */\n  startDate?: CalendarDate,\n  /**\n   * The last date displayed in the calendar grid.\n   * Defaults to the last visible date in the calendar.\n   * Override this to display multiple date grids in a calendar.\n   */\n  endDate?: CalendarDate,\n  /**\n   * The style of weekday names to display in the calendar grid header,\n   * e.g. single letter, abbreviation, or full day name.\n   * @default \"narrow\"\n   */\n  weekdayStyle?: 'narrow' | 'short' | 'long',\n  /**\n   * The day that starts the week.\n   */\n  firstDayOfWeek?: 'sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat'\n}\n\nexport interface CalendarGridAria {\n  /** Props for the date grid element (e.g. `<table>`). */\n  gridProps: DOMAttributes,\n  /** Props for the grid header element (e.g. `<thead>`). */\n  headerProps: DOMAttributes,\n  /** A list of week day abbreviations formatted for the current locale, typically used in column headers. */\n  weekDays: string[],\n  /** The number of weeks in the month. */\n  weeksInMonth: number\n}\n\n/**\n * Provides the behavior and accessibility implementation for a calendar grid component.\n * A calendar grid displays a single grid of days within a calendar or range calendar which\n * can be keyboard navigated and selected by the user.\n */\nexport function useCalendarGrid(props: AriaCalendarGridProps, state: CalendarState | RangeCalendarState): CalendarGridAria {\n  let {\n    startDate = state.visibleRange.start,\n    endDate = state.visibleRange.end,\n    firstDayOfWeek\n  } = props;\n\n  let {direction} = useLocale();\n\n  let onKeyDown = (e: KeyboardEvent) => {\n    switch (e.key) {\n      case 'Enter':\n      case ' ':\n        e.preventDefault();\n        state.selectFocusedDate();\n        break;\n      case 'PageUp':\n        e.preventDefault();\n        e.stopPropagation();\n        state.focusPreviousSection(e.shiftKey);\n        break;\n      case 'PageDown':\n        e.preventDefault();\n        e.stopPropagation();\n        state.focusNextSection(e.shiftKey);\n        break;\n      case 'End':\n        e.preventDefault();\n        e.stopPropagation();\n        state.focusSectionEnd();\n        break;\n      case 'Home':\n        e.preventDefault();\n        e.stopPropagation();\n        state.focusSectionStart();\n        break;\n      case 'ArrowLeft':\n        e.preventDefault();\n        e.stopPropagation();\n        if (direction === 'rtl') {\n          state.focusNextDay();\n        } else {\n          state.focusPreviousDay();\n        }\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        e.stopPropagation();\n        state.focusPreviousRow();\n        break;\n      case 'ArrowRight':\n        e.preventDefault();\n        e.stopPropagation();\n        if (direction === 'rtl') {\n          state.focusPreviousDay();\n        } else {\n          state.focusNextDay();\n        }\n        break;\n      case 'ArrowDown':\n        e.preventDefault();\n        e.stopPropagation();\n        state.focusNextRow();\n        break;\n      case 'Escape':\n        // Cancel the selection.\n        if ('setAnchorDate' in state) {\n          e.preventDefault();\n          state.setAnchorDate(null);\n        }\n        break;\n    }\n  };\n\n  let visibleRangeDescription = useVisibleRangeDescription(startDate, endDate, state.timeZone, true);\n\n  let {ariaLabel, ariaLabelledBy} = hookData.get(state)!;\n  let labelProps = useLabels({\n    'aria-label': [ariaLabel, visibleRangeDescription].filter(Boolean).join(', '),\n    'aria-labelledby': ariaLabelledBy\n  });\n\n  let dayFormatter = useDateFormatter({weekday: props.weekdayStyle || 'narrow', timeZone: state.timeZone});\n  let {locale} = useLocale();\n  let weekDays = useMemo(() => {\n    let weekStart = startOfWeek(today(state.timeZone), locale, firstDayOfWeek);\n    return [...new Array(7).keys()].map((index) => {\n      let date = weekStart.add({days: index});\n      let dateDay = date.toDate(state.timeZone);\n      return dayFormatter.format(dateDay);\n    });\n  }, [locale, state.timeZone, dayFormatter, firstDayOfWeek]);\n  let weeksInMonth = getWeeksInMonth(startDate, locale, firstDayOfWeek);\n\n  return {\n    gridProps: mergeProps(labelProps, {\n      role: 'grid',\n      'aria-readonly': state.isReadOnly || undefined,\n      'aria-disabled': state.isDisabled || undefined,\n      'aria-multiselectable': ('highlightedRange' in state) || undefined,\n      onKeyDown,\n      onFocus: () => state.setFocused(true),\n      onBlur: () => state.setFocused(false)\n    }),\n    headerProps: {\n      // Column headers are hidden to screen readers to make navigating with a touch screen reader easier.\n      // The day names are already included in the label of each cell, so there's no need to announce them twice.\n      'aria-hidden': true\n    },\n    weekDays,\n    weeksInMonth\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAmDM,SAAS,0CAAgB,KAA4B,EAAE,KAAyC;IACrG,IAAI,EAAA,WACF,YAAY,MAAM,YAAY,CAAC,KAAK,EAAA,SACpC,UAAU,MAAM,YAAY,CAAC,GAAG,EAAA,gBAChC,cAAc,EACf,GAAG;IAEJ,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,kKAAA,YAAQ;IAE1B,IAAI,YAAY,CAAC;QACf,OAAQ,EAAE,GAAG;YACX,KAAK;YACL,KAAK;gBACH,EAAE,cAAc;gBAChB,MAAM,iBAAiB;gBACvB;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,MAAM,oBAAoB,CAAC,EAAE,QAAQ;gBACrC;YACF,KAAK;g<PERSON><PERSON>,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,MAAM,gBAAgB,CAAC,EAAE,QAAQ;gBACjC;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,MAAM,eAAe;gBACrB;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,MAAM,iBAAiB;gBACvB;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,IAAI,cAAc,OAChB,MAAM,YAAY;qBAElB,MAAM,gBAAgB;gBAExB;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,MAAM,gBAAgB;gBACtB;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,IAAI,cAAc,OAChB,MAAM,gBAAgB;qBAEtB,MAAM,YAAY;gBAEpB;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,MAAM,YAAY;gBAClB;YACF,KAAK;gBACH,wBAAwB;gBACxB,IAAI,mBAAmB,OAAO;oBAC5B,EAAE,cAAc;oBAChB,MAAM,aAAa,CAAC;gBACtB;gBACA;QACJ;IACF;IAEA,IAAI,0BAA0B,CAAA,oKAAA,6BAAyB,EAAE,WAAW,SAAS,MAAM,QAAQ,EAAE;IAE7F,IAAI,EAAA,WAAC,SAAS,EAAA,gBAAE,cAAc,EAAC,GAAG,CAAA,oKAAA,WAAO,EAAE,GAAG,CAAC;IAC/C,IAAI,aAAa,CAAA,qKAAA,YAAQ,EAAE;QACzB,cAAc;YAAC;YAAW;SAAwB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACxE,mBAAmB;IACrB;IAEA,IAAI,eAAe,CAAA,2KAAA,mBAAe,EAAE;QAAC,SAAS,MAAM,YAAY,IAAI;QAAU,UAAU,MAAM,QAAQ;IAAA;IACtG,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,kKAAA,YAAQ;IACvB,IAAI,WAAW,CAAA,iKAAA,UAAM,EAAE;QACrB,IAAI,YAAY,CAAA,sKAAA,cAAU,EAAE,CAAA,sKAAA,QAAI,EAAE,MAAM,QAAQ,GAAG,QAAQ;QAC3D,OAAO;eAAI,IAAI,MAAM,GAAG,IAAI;SAAG,CAAC,GAAG,CAAC,CAAC;YACnC,IAAI,OAAO,UAAU,GAAG,CAAC;gBAAC,MAAM;YAAK;YACrC,IAAI,UAAU,KAAK,MAAM,CAAC,MAAM,QAAQ;YACxC,OAAO,aAAa,MAAM,CAAC;QAC7B;IACF,GAAG;QAAC;QAAQ,MAAM,QAAQ;QAAE;QAAc;KAAe;IACzD,IAAI,eAAe,CAAA,sKAAA,kBAAc,EAAE,WAAW,QAAQ;IAEtD,OAAO;QACL,WAAW,CAAA,sKAAA,aAAS,EAAE,YAAY;YAChC,MAAM;YACN,iBAAiB,MAAM,UAAU,IAAI;YACrC,iBAAiB,MAAM,UAAU,IAAI;YACrC,wBAAyB,sBAAsB,SAAU;uBACzD;YACA,SAAS,IAAM,MAAM,UAAU,CAAC;YAChC,QAAQ,IAAM,MAAM,UAAU,CAAC;QACjC;QACA,aAAa;YACX,oGAAoG;YACpG,2GAA2G;YAC3G,eAAe;QACjB;kBACA;sBACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1719, "column": 0}, "map": {"version": 3, "file": "useRangeCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/calendar/dist/packages/%40react-aria/calendar/src/useRangeCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaRangeCalendarProps, DateValue} from '@react-types/calendar';\nimport {CalendarAria, useCalendarBase} from './useCalendarBase';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {RangeCalendarState} from '@react-stately/calendar';\nimport {useEvent} from '@react-aria/utils';\nimport {useRef} from 'react';\n\n/**\n * Provides the behavior and accessibility implementation for a range calendar component.\n * A range calendar displays one or more date grids and allows users to select a contiguous range of dates.\n */\nexport function useRangeCalendar<T extends DateValue>(props: AriaRangeCalendarProps<T>, state: RangeCalendarState, ref: RefObject<FocusableElement | null>): CalendarAria {\n  let res = useCalendarBase(props, state);\n\n  // We need to ignore virtual pointer events from VoiceOver due to these bugs.\n  // https://bugs.webkit.org/show_bug.cgi?id=222627\n  // https://bugs.webkit.org/show_bug.cgi?id=223202\n  // usePress also does this and waits for the following click event before firing.\n  // We need to match that here otherwise this will fire before the press event in\n  // useCalendarCell, causing range selection to not work properly.\n  let isVirtualClick = useRef(false);\n  let windowRef = useRef(typeof window !== 'undefined' ? window : null);\n  useEvent(windowRef, 'pointerdown', e => {\n    isVirtualClick.current = e.width === 0 && e.height === 0;\n  });\n\n  // Stop range selection when pressing or releasing a pointer outside the calendar body,\n  // except when pressing the next or previous buttons to switch months.\n  let endDragging = (e: PointerEvent) => {\n    if (isVirtualClick.current) {\n      isVirtualClick.current = false;\n      return;\n    }\n\n    state.setDragging(false);\n    if (!state.anchorDate) {\n      return;\n    }\n\n    let target = e.target as Element;\n    if (\n      ref.current &&\n      ref.current.contains(document.activeElement) &&\n      (!ref.current.contains(target) || !target.closest('button, [role=\"button\"]'))\n    ) {\n      state.selectFocusedDate();\n    }\n  };\n\n  useEvent(windowRef, 'pointerup', endDragging);\n\n  // Also stop range selection on blur, e.g. tabbing away from the calendar.\n  res.calendarProps.onBlur = e => {\n    if (!ref.current) {\n      return;\n    }\n    if ((!e.relatedTarget || !ref.current.contains(e.relatedTarget)) && state.anchorDate) {\n      state.selectFocusedDate();\n    }\n  };\n\n  // Prevent touch scrolling while dragging\n  useEvent(ref, 'touchmove', e => {\n    if (state.isDragging) {\n      e.preventDefault();\n    }\n  }, {passive: false, capture: true});\n\n  return res;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS,0CAAsC,KAAgC,EAAE,KAAyB,EAAE,GAAuC;IACxJ,IAAI,MAAM,CAAA,8KAAA,kBAAc,EAAE,OAAO;IAEjC,6EAA6E;IAC7E,iDAAiD;IACjD,iDAAiD;IACjD,iFAAiF;IACjF,gFAAgF;IAChF,iEAAiE;IACjE,IAAI,iBAAiB,CAAA,iKAAA,SAAK,EAAE;IAC5B,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE,OAAO,WAAW,cAAc,SAAS;IAChE,CAAA,oKAAA,WAAO,EAAE,WAAW,eAAe,CAAA;QACjC,eAAe,OAAO,GAAG,EAAE,KAAK,KAAK,KAAK,EAAE,MAAM,KAAK;IACzD;IAEA,uFAAuF;IACvF,sEAAsE;IACtE,IAAI,cAAc,CAAC;QACjB,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,GAAG;YACzB;QACF;QAEA,MAAM,WAAW,CAAC;QAClB,IAAI,CAAC,MAAM,UAAU,EACnB;QAGF,IAAI,SAAS,EAAE,MAAM;QACrB,IACE,IAAI,OAAO,IACX,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,aAAa,KAC1C,CAAA,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,OAAO,CAAC,0BAAyB,GAE3E,MAAM,iBAAiB;IAE3B;IAEA,CAAA,oKAAA,WAAO,EAAE,WAAW,aAAa;IAEjC,0EAA0E;IAC1E,IAAI,aAAa,CAAC,MAAM,GAAG,CAAA;QACzB,IAAI,CAAC,IAAI,OAAO,EACd;QAEF,IAAK,CAAA,CAAC,EAAE,aAAa,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,aAAa,CAAA,KAAM,MAAM,UAAU,EAClF,MAAM,iBAAiB;IAE3B;IAEA,yCAAyC;IACzC,CAAA,oKAAA,WAAO,EAAE,KAAK,aAAa,CAAA;QACzB,IAAI,MAAM,UAAU,EAClB,EAAE,cAAc;IAEpB,GAAG;QAAC,SAAS;QAAO,SAAS;IAAI;IAEjC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1786, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// https://en.wikipedia.org/wiki/Right-to-left\nconst RTL_SCRIPTS = new Set(['Arab', 'Syrc', 'Samr', 'Mand', 'Thaa', 'Mend', 'Nkoo', 'Adlm', 'Rohg', 'Hebr']);\nconst RTL_LANGS = new Set(['ae', 'ar', 'arc', 'bcc', 'bqi', 'ckb', 'dv', 'fa', 'glk', 'he', 'ku', 'mzn', 'nqo', 'pnb', 'ps', 'sd', 'ug', 'ur', 'yi']);\n\n/**\n * Determines if a locale is read right to left using [Intl.Locale]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale}.\n */\nexport function isRTL(localeString: string): boolean {\n  // If the Intl.Locale API is available, use it to get the locale's text direction.\n  if (Intl.Locale) {\n    let locale = new Intl.Locale(localeString).maximize();\n\n    // Use the text info object to get the direction if possible.\n    // @ts-ignore - this was implemented as a property by some browsers before it was standardized as a function.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/getTextInfo\n    let textInfo = typeof locale.getTextInfo === 'function' ? locale.getTextInfo() : locale.textInfo;\n    if (textInfo) {\n      return textInfo.direction === 'rtl';\n    }\n\n    // Fallback: guess using the script.\n    // This is more accurate than guessing by language, since languages can be written in multiple scripts.\n    if (locale.script) {\n      return RTL_SCRIPTS.has(locale.script);\n    }\n  }\n\n  // If not, just guess by the language (first part of the locale)\n  let lang = localeString.split('-')[0];\n  return RTL_LANGS.has(lang);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED,8CAA8C;;;;AAC9C,MAAM,oCAAc,IAAI,IAAI;IAAC;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;CAAO;AAC5G,MAAM,kCAAY,IAAI,IAAI;IAAC;IAAM;IAAM;IAAO;IAAO;IAAO;IAAO;IAAM;IAAM;IAAO;IAAM;IAAM;IAAO;IAAO;IAAO;IAAM;IAAM;IAAM;IAAM;CAAK;AAK7I,SAAS,0CAAM,YAAoB;IACxC,kFAAkF;IAClF,IAAI,KAAK,MAAM,EAAE;QACf,IAAI,SAAS,IAAI,KAAK,MAAM,CAAC,cAAc,QAAQ;QAEnD,6DAA6D;QAC7D,6GAA6G;QAC7G,2GAA2G;QAC3G,IAAI,WAAW,OAAO,OAAO,WAAW,KAAK,aAAa,OAAO,WAAW,KAAK,OAAO,QAAQ;QAChG,IAAI,UACF,OAAO,SAAS,SAAS,KAAK;QAGhC,oCAAoC;QACpC,uGAAuG;QACvG,IAAI,OAAO,MAAM,EACf,OAAO,kCAAY,GAAG,CAAC,OAAO,MAAM;IAExC;IAEA,gEAAgE;IAChE,IAAI,OAAO,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE;IACrC,OAAO,gCAAU,GAAG,CAAC;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1858, "column": 0}, "map": {"version": 3, "file": "useDefaultLocale.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/useDefaultLocale.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Direction} from '@react-types/shared';\nimport {isRTL} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport interface Locale {\n  /** The [BCP47](https://www.ietf.org/rfc/bcp/bcp47.txt) language code for the locale. */\n  locale: string,\n  /** The writing direction for the locale. */\n  direction: Direction\n}\n\n// Locale passed from server by PackageLocalizationProvider.\nconst localeSymbol = Symbol.for('react-aria.i18n.locale');\n\n/**\n * Gets the locale setting of the browser.\n */\nexport function getDefaultLocale(): Locale {\n  let locale = typeof window !== 'undefined' && window[localeSymbol]\n    // @ts-ignore\n    || (typeof navigator !== 'undefined' && (navigator.language || navigator.userLanguage))\n    || 'en-US';\n\n  try {\n    Intl.DateTimeFormat.supportedLocalesOf([locale]);\n  } catch {\n    locale = 'en-US';\n  }\n  return {\n    locale,\n    direction: isRTL(locale) ? 'rtl' : 'ltr'\n  };\n}\n\nlet currentLocale = getDefaultLocale();\nlet listeners = new Set<(locale: Locale) => void>();\n\nfunction updateLocale() {\n  currentLocale = getDefaultLocale();\n  for (let listener of listeners) {\n    listener(currentLocale);\n  }\n}\n\n/**\n * Returns the current browser/system language, and updates when it changes.\n */\nexport function useDefaultLocale(): Locale {\n  let isSSR = useIsSSR();\n  let [defaultLocale, setDefaultLocale] = useState(currentLocale);\n\n  useEffect(() => {\n    if (listeners.size === 0) {\n      window.addEventListener('languagechange', updateLocale);\n    }\n\n    listeners.add(setDefaultLocale);\n\n    return () => {\n      listeners.delete(setDefaultLocale);\n      if (listeners.size === 0) {\n        window.removeEventListener('languagechange', updateLocale);\n      }\n    };\n  }, []);\n\n  // We cannot determine the browser's language on the server, so default to\n  // en-US. This will be updated after hydration on the client to the correct value.\n  if (isSSR) {\n    return {\n      locale: 'en-US',\n      direction: 'ltr'\n    };\n  }\n\n  return defaultLocale;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAcD,4DAA4D;AAC5D,MAAM,qCAAe,OAAO,GAAG,CAAC;AAKzB,SAAS;IACd,IAAI,SAAS,OAAO,WAAW,eAAe,MAAM,CAAC,mCAAa,IAE5D,OAAO,cAAc,eAAgB,CAAA,UAAU,QAAQ,IAAI,UAAU,YAAW,KACjF;IAEL,IAAI;QACF,KAAK,cAAc,CAAC,kBAAkB,CAAC;YAAC;SAAO;IACjD,EAAE,OAAM;QACN,SAAS;IACX;IACA,OAAO;gBACL;QACA,WAAW,CAAA,gKAAA,QAAI,EAAE,UAAU,QAAQ;IACrC;AACF;AAEA,IAAI,sCAAgB;AACpB,IAAI,kCAAY,IAAI;AAEpB,SAAS;IACP,sCAAgB;IAChB,KAAK,IAAI,YAAY,gCACnB,SAAS;AAEb;AAKO,SAAS;IACd,IAAI,QAAQ,CAAA,qKAAA,WAAO;IACnB,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAEjD,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,gCAAU,IAAI,KAAK,GACrB,OAAO,gBAAgB,CAAC,kBAAkB;QAG5C,gCAAU,GAAG,CAAC;QAEd,OAAO;YACL,gCAAU,MAAM,CAAC;YACjB,IAAI,gCAAU,IAAI,KAAK,GACrB,OAAO,mBAAmB,CAAC,kBAAkB;QAEjD;IACF,GAAG,EAAE;IAEL,0EAA0E;IAC1E,kFAAkF;IAClF,IAAI,OACF,OAAO;QACL,QAAQ;QACR,WAAW;IACb;IAGF,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1927, "column": 0}, "map": {"version": 3, "file": "context.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/context.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isRTL} from './utils';\nimport {Locale, useDefaultLocale} from './useDefaultLocale';\nimport React, {JSX, ReactNode, useContext} from 'react';\n\nexport interface I18nProviderProps {\n  /** Contents that should have the locale applied. */\n  children: ReactNode,\n  /** The locale to apply to the children. */\n  locale?: string\n}\n\nconst I18nContext = React.createContext<Locale | null>(null);\n\ninterface I18nProviderWithLocaleProps extends I18nProviderProps {\n  locale: string\n}\n\n/**\n * Internal component that handles the case when locale is provided.\n */\nfunction I18nProviderWithLocale(props: I18nProviderWithLocaleProps): JSX.Element {\n  let {locale, children} = props;  \n  let value: Locale = React.useMemo(() => ({\n    locale,\n    direction: isRTL(locale) ? 'rtl' : 'ltr'\n  }), [locale]);\n\n  return (\n    <I18nContext.Provider value={value}>\n      {children}\n    </I18nContext.Provider>\n  );\n}\n\ninterface I18nProviderWithDefaultLocaleProps {\n  children: ReactNode\n}\n\n/**\n * Internal component that handles the case when no locale is provided.\n */\nfunction I18nProviderWithDefaultLocale(props: I18nProviderWithDefaultLocaleProps): JSX.Element {\n  let {children} = props;\n  let defaultLocale = useDefaultLocale();\n\n  return (\n    <I18nContext.Provider value={defaultLocale}>\n      {children}\n    </I18nContext.Provider>\n  );\n}\n\n/**\n * Provides the locale for the application to all child components.\n */\nexport function I18nProvider(props: I18nProviderProps): JSX.Element {\n  let {locale, children} = props;\n\n  // Conditionally render different components to avoid calling useDefaultLocale.\n  // This is necessary because useDefaultLocale triggers a re-render.\n  if (locale) {\n    return <I18nProviderWithLocale locale={locale} children={children} />;\n  }\n\n  return <I18nProviderWithDefaultLocale children={children} />;\n}\n\n/**\n * Returns the current locale and layout direction.\n */\nexport function useLocale(): Locale {\n  let defaultLocale = useDefaultLocale();\n  let context = useContext(I18nContext);\n  return context || defaultLocale;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAaD,MAAM,oCAAA,WAAA,GAAc,CAAA,iKAAA,UAAI,EAAE,aAAa,CAAgB;AAMvD;;CAEC,GACD,SAAS,6CAAuB,KAAkC;IAChE,IAAI,EAAA,QAAC,MAAM,EAAA,UAAE,QAAQ,EAAC,GAAG;IACzB,IAAI,QAAgB,CAAA,iKAAA,UAAI,EAAE,OAAO;uEAAC,IAAO,CAAA;wBACvC;gBACA,WAAW,CAAA,gKAAA,QAAI,EAAE,UAAU,QAAQ;YACrC,CAAA;sEAAI;QAAC;KAAO;IAEZ,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,kCAAY,QAAQ,EAAA;QAAC,OAAO;OAC1B;AAGP;AAMA;;CAEC,GACD,SAAS,oDAA8B,KAAyC;IAC9E,IAAI,EAAA,UAAC,QAAQ,EAAC,GAAG;IACjB,IAAI,gBAAgB,CAAA,2KAAA,mBAAe;IAEnC,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,kCAAY,QAAQ,EAAA;QAAC,OAAO;OAC1B;AAGP;AAKO,SAAS,0CAAa,KAAwB;IACnD,IAAI,EAAA,QAAC,MAAM,EAAA,UAAE,QAAQ,EAAC,GAAG;IAEzB,+EAA+E;IAC/E,mEAAmE;IACnE,IAAI,QACF,OAAA,WAAA,GAAO,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,8CAAA;QAAuB,QAAQ;QAAQ,UAAU;;IAG3D,OAAA,WAAA,GAAO,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,qDAAA;QAA8B,UAAU;;AAClD;AAKO,SAAS;IACd,IAAI,gBAAgB,CAAA,2KAAA,mBAAe;IACnC,IAAI,UAAU,CAAA,iKAAA,aAAS,EAAE;IACzB,OAAO,WAAW;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1998, "column": 0}, "map": {"version": 3, "file": "useLocalizedStringFormatter.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/useLocalizedStringFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {LocalizedString, LocalizedStringDictionary, LocalizedStringFormatter, LocalizedStrings} from '@internationalized/string';\nimport {useLocale} from './context';\nimport {useMemo} from 'react';\n\nconst cache = new WeakMap();\nfunction getCachedDictionary<K extends string, T extends LocalizedString>(strings: LocalizedStrings<K, T>): LocalizedStringDictionary<K, T> {\n  let dictionary = cache.get(strings);\n  if (!dictionary) {\n    dictionary = new LocalizedStringDictionary(strings);\n    cache.set(strings, dictionary);\n  }\n\n  return dictionary;\n}\n\n/**\n * Returns a cached LocalizedStringDictionary for the given strings.\n */\nexport function useLocalizedStringDictionary<K extends string = string, T extends LocalizedString = string>(strings: LocalizedStrings<K, T>, packageName?: string): LocalizedStringDictionary<K, T> {\n  return (packageName && LocalizedStringDictionary.getGlobalDictionaryForPackage(packageName)) || getCachedDictionary(strings);\n}\n\n/**\n * Provides localized string formatting for the current locale. Supports interpolating variables,\n * selecting the correct pluralization, and formatting numbers. Automatically updates when the locale changes.\n * @param strings - A mapping of languages to localized strings by key.\n */\nexport function useLocalizedStringFormatter<K extends string = string, T extends LocalizedString = string>(strings: LocalizedStrings<K, T>, packageName?: string): LocalizedStringFormatter<K, T> {\n  let {locale} = useLocale();\n  let dictionary = useLocalizedStringDictionary(strings, packageName);\n  return useMemo(() => new LocalizedStringFormatter(locale, dictionary), [locale, dictionary]);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAMD,MAAM,8BAAQ,IAAI;AAClB,SAAS,0CAAiE,OAA+B;IACvG,IAAI,aAAa,4BAAM,GAAG,CAAC;IAC3B,IAAI,CAAC,YAAY;QACf,aAAa,IAAI,CAAA,0LAAA,4BAAwB,EAAE;QAC3C,4BAAM,GAAG,CAAC,SAAS;IACrB;IAEA,OAAO;AACT;AAKO,SAAS,0CAA4F,OAA+B,EAAE,WAAoB;IAC/J,OAAQ,eAAe,CAAA,0LAAA,4BAAwB,EAAE,6BAA6B,CAAC,gBAAiB,0CAAoB;AACtH;AAOO,SAAS,0CAA2F,OAA+B,EAAE,WAAoB;IAC9J,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,kKAAA,YAAQ;IACvB,IAAI,aAAa,0CAA6B,SAAS;IACvD,OAAO,CAAA,iKAAA,UAAM,EAAE,IAAM,IAAI,CAAA,yLAAA,2BAAuB,EAAE,QAAQ,aAAa;QAAC;QAAQ;KAAW;AAC7F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "file": "useDateFormatter.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/useDateFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DateFormatter} from '@internationalized/date';\nimport {useDeepMemo} from '@react-aria/utils';\nimport {useLocale} from './context';\nimport {useMemo} from 'react';\n\nexport interface DateFormatterOptions extends Intl.DateTimeFormatOptions {\n  calendar?: string\n}\n\n/**\n * Provides localized date formatting for the current locale. Automatically updates when the locale changes,\n * and handles caching of the date formatter for performance.\n * @param options - Formatting options.\n */\nexport function useDateFormatter(options?: DateFormatterOptions): DateFormatter {\n  // Reuse last options object if it is shallowly equal, which allows the useMemo result to also be reused.\n  options = useDeepMemo(options ?? {}, isEqual);\n  let {locale} = useLocale();\n  return useMemo(() => new DateFormatter(locale, options), [locale, options]);\n}\n\nfunction isEqual(a: DateFormatterOptions, b: DateFormatterOptions) {\n  if (a === b) {\n    return true;\n  }\n\n  let aKeys = Object.keys(a);\n  let bKeys = Object.keys(b);\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n\n  for (let key of aKeys) {\n    if (b[key] !== a[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAgBM,SAAS,0CAAiB,OAA8B;IAC7D,yGAAyG;IACzG,UAAU,CAAA,uKAAA,cAAU,EAAE,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW,CAAC,GAAG;IACrC,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,kKAAA,YAAQ;IACvB,OAAO,CAAA,iKAAA,UAAM,EAAE,IAAM,IAAI,CAAA,4KAAA,gBAAY,EAAE,QAAQ,UAAU;QAAC;QAAQ;KAAQ;AAC5E;AAEA,SAAS,8BAAQ,CAAuB,EAAE,CAAuB;IAC/D,IAAI,MAAM,GACR,OAAO;IAGT,IAAI,QAAQ,OAAO,IAAI,CAAC;IACxB,IAAI,QAAQ,OAAO,IAAI,CAAC;IACxB,IAAI,MAAM,MAAM,KAAK,MAAM,MAAM,EAC/B,OAAO;IAGT,KAAK,IAAI,OAAO,MAAO;QACrB,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EACnB,OAAO;IAEX;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2095, "column": 0}, "map": {"version": 3, "file": "useCollator.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/useCollator.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useLocale} from './context';\n\nlet cache = new Map<string, Intl.Collator>();\n\n/**\n * Provides localized string collation for the current locale. Automatically updates when the locale changes,\n * and handles caching of the collator for performance.\n * @param options - Collator options.\n */\nexport function useCollator(options?: Intl.CollatorOptions): Intl.Collator {\n  let {locale} = useLocale();\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (cache.has(cacheKey)) {\n    return cache.get(cacheKey)!;\n  }\n\n  let formatter = new Intl.Collator(locale, options);\n  cache.set(cacheKey, formatter);\n  return formatter;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAID,IAAI,8BAAQ,IAAI;AAOT,SAAS,0CAAY,OAA8B;IACxD,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,kKAAA,YAAQ;IAEvB,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAA,IAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,4BAAM,GAAG,CAAC,WACZ,OAAO,4BAAM,GAAG,CAAC;IAGnB,IAAI,YAAY,IAAI,KAAK,QAAQ,CAAC,QAAQ;IAC1C,4BAAM,GAAG,CAAC,UAAU;IACpB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "file": "useFilter.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/useFilter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useMemo} from 'react';\nimport {useCollator} from './useCollator';\n\nexport interface Filter {\n  /** Returns whether a string starts with a given substring. */\n  startsWith(string: string, substring: string): boolean,\n  /** Returns whether a string ends with a given substring. */\n  endsWith(string: string, substring: string): boolean,\n  /** Returns whether a string contains a given substring. */\n  contains(string: string, substring: string): boolean\n}\n\n/**\n * Provides localized string search functionality that is useful for filtering or matching items\n * in a list. Options can be provided to adjust the sensitivity to case, diacritics, and other parameters.\n */\nexport function useFilter(options?: Intl.CollatorOptions): Filter {\n  let collator = useCollator({\n    usage: 'search',\n    ...options\n  });\n\n  // TODO(later): these methods don't currently support the ignorePunctuation option.\n  let startsWith = useCallback((string, substring) => {\n    if (substring.length === 0) {\n      return true;\n    }\n\n    // Normalize both strings so we can slice safely\n    // TODO: take into account the ignorePunctuation option as well...\n    string = string.normalize('NFC');\n    substring = substring.normalize('NFC');\n    return collator.compare(string.slice(0, substring.length), substring) === 0;\n  }, [collator]);\n\n  let endsWith = useCallback((string, substring) => {\n    if (substring.length === 0) {\n      return true;\n    }\n\n    string = string.normalize('NFC');\n    substring = substring.normalize('NFC');\n    return collator.compare(string.slice(-substring.length), substring) === 0;\n  }, [collator]);\n\n  let contains = useCallback((string, substring) => {\n    if (substring.length === 0) {\n      return true;\n    }\n\n    string = string.normalize('NFC');\n    substring = substring.normalize('NFC');\n\n    let scan = 0;\n    let sliceLen = substring.length;\n    for (; scan + sliceLen <= string.length; scan++) {\n      let slice = string.slice(scan, scan + sliceLen);\n      if (collator.compare(substring, slice) === 0) {\n        return true;\n      }\n    }\n\n    return false;\n  }, [collator]);\n\n  return useMemo(() => ({\n    startsWith,\n    endsWith,\n    contains\n  }), [startsWith, endsWith, contains]);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAkBM,SAAS,yCAAU,OAA8B;IACtD,IAAI,WAAW,CAAA,sKAAA,cAAU,EAAE;QACzB,OAAO;QACP,GAAG,OAAO;IACZ;IAEA,mFAAmF;IACnF,IAAI,aAAa,CAAA,iKAAA,cAAU,EAAE,CAAC,QAAQ;QACpC,IAAI,UAAU,MAAM,KAAK,GACvB,OAAO;QAGT,gDAAgD;QAChD,kEAAkE;QAClE,SAAS,OAAO,SAAS,CAAC;QAC1B,YAAY,UAAU,SAAS,CAAC;QAChC,OAAO,SAAS,OAAO,CAAC,OAAO,KAAK,CAAC,GAAG,UAAU,MAAM,GAAG,eAAe;IAC5E,GAAG;QAAC;KAAS;IAEb,IAAI,WAAW,CAAA,iKAAA,cAAU,EAAE,CAAC,QAAQ;QAClC,IAAI,UAAU,MAAM,KAAK,GACvB,OAAO;QAGT,SAAS,OAAO,SAAS,CAAC;QAC1B,YAAY,UAAU,SAAS,CAAC;QAChC,OAAO,SAAS,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC,UAAU,MAAM,GAAG,eAAe;IAC1E,GAAG;QAAC;KAAS;IAEb,IAAI,WAAW,CAAA,iKAAA,cAAU,EAAE,CAAC,QAAQ;QAClC,IAAI,UAAU,MAAM,KAAK,GACvB,OAAO;QAGT,SAAS,OAAO,SAAS,CAAC;QAC1B,YAAY,UAAU,SAAS,CAAC;QAEhC,IAAI,OAAO;QACX,IAAI,WAAW,UAAU,MAAM;QAC/B,MAAO,OAAO,YAAY,OAAO,MAAM,EAAE,OAAQ;YAC/C,IAAI,QAAQ,OAAO,KAAK,CAAC,MAAM,OAAO;YACtC,IAAI,SAAS,OAAO,CAAC,WAAW,WAAW,GACzC,OAAO;QAEX;QAEA,OAAO;IACT,GAAG;QAAC;KAAS;IAEb,OAAO,CAAA,iKAAA,UAAM,EAAE,IAAO,CAAA;wBACpB;sBACA;sBACA;QACF,CAAA,GAAI;QAAC;QAAY;QAAU;KAAS;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "file": "SSRProvider.module.js.map", "sourceRoot": "../../../../", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/ssr/dist/packages/%40react-aria/ssr/src/SSRProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {JSX, ReactNode, useContext, useLayoutEffect, useMemo, useRef, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\nconst IsSSRContext = React.createContext(false);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n// This is only used in React < 18.\nfunction LegacySSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let counter = useCounter(cur === defaultContext);\n  let [isSSR, setIsSSR] = useState(true);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${counter}`,\n    current: 0\n  }), [cur, counter]);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined') {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return (\n    <SSRContext.Provider value={value}>\n      <IsSSRContext.Provider value={isSSR}>\n        {props.children}\n      </IsSSRContext.Provider>\n    </SSRContext.Provider>\n  );\n}\n\nlet warnedAboutSSRProvider = false;\n\n/**\n * When using SSR with React Aria in React 16 or 17, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  if (typeof React['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      warnedAboutSSRProvider = true;\n    }\n    return <>{props.children}</>;\n  }\n  return <LegacySSRProvider {...props} />;\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nlet componentIds = new WeakMap();\n\nfunction useCounter(isDisabled = false) {\n  let ctx = useContext(SSRContext);\n  let ref = useRef<number | null>(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current;\n    if (currentOwner) {\n      let prevComponentValue = componentIds.get(currentOwner);\n      if (prevComponentValue == null) {\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });\n      } else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        componentIds.delete(currentOwner);\n      }\n    }\n\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\n\nfunction useLegacySSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM && process.env.NODE_ENV !== 'production') {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  let counter = useCounter(!!defaultId);\n  let prefix = ctx === defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${ctx.prefix}`;\n  return defaultId || `${prefix}-${counter}`;\n}\n\nfunction useModernSSRSafeId(defaultId?: string): string {\n  let id = React.useId();\n  let [didSSR] = useState(useIsSSR());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${defaultContext.prefix}`;\n  return defaultId || `${prefix}-${id}`;\n}\n\n// Use React.useId in React 18 if available, otherwise fall back to our old implementation.\n/** @private */\nexport const useSSRSafeId: typeof useModernSSRSafeId | typeof useLegacySSRSafeId = typeof React['useId'] === 'function' ? useModernSSRSafeId : useLegacySSRSafeId;\n\nfunction getSnapshot() {\n  return false;\n}\n\nfunction getServerSnapshot() {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction subscribe(onStoreChange: () => void): () => void {\n  // noop\n  return () => {};\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof React['useSyncExternalStore'] === 'function') {\n    return React['useSyncExternalStore'](subscribe, getSnapshot, getServerSnapshot);\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(IsSSRContext);\n}\n"], "names": [], "mappings": ";;;;;AAsFQ,QAAQ,GAAG,CAAC,QAAQ;;;AAtF5B;;;;;;;;;;CAUC,GAED,0FAA0F;AAC1F,2DAA2D;AAC3D,wDAAwD;AAcxD,iFAAiF;AACjF,kFAAkF;AAClF,+EAA+E;AAC/E,+EAA+E;AAC/E,2DAA2D;AAC3D,MAAM,uCAAkC;IACtC,QAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAC1C,SAAS;AACX;AAEA,MAAM,mCAAA,WAAA,GAAa,CAAA,iKAAA,UAAI,EAAE,aAAa,CAAkB;AACxD,MAAM,qCAAA,WAAA,GAAe,CAAA,iKAAA,UAAI,EAAE,aAAa,CAAC;AAOzC,mCAAmC;AACnC,SAAS,wCAAkB,KAAuB;IAChD,IAAI,MAAM,CAAA,iKAAA,aAAS,EAAE;IACrB,IAAI,UAAU,iCAAW,QAAQ;IACjC,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,yKAAO,EAAE;IACjC,IAAI,QAAyB,CAAA,iKAAA,UAAM,EAAE,IAAO,CAAA;YAC1C,iFAAiF;YACjF,oCAAoC;YACpC,QAAQ,QAAQ,uCAAiB,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,SAAS;YAChE,SAAS;QACX,CAAA,GAAI;QAAC;QAAK;KAAQ;IAElB,qEAAqE;IACrE,yEAAyE;IACzE,IAAI,OAAO,aAAa,aACtB,AACA,sDAAsD,iBADiB;IAEvE,sDAAsD;IACtD,CAAA,iKAAA,kBAAc,EAAE;QACd,SAAS;IACX,GAAG,EAAE;IAGP,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,iCAAW,QAAQ,EAAA;QAAC,OAAO;qBAC1B,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,mCAAa,QAAQ,EAAA;QAAC,OAAO;OAC3B,MAAM,QAAQ;AAIvB;AAEA,IAAI,+CAAyB;AAMtB,SAAS,0CAAY,KAAuB;IACjD,IAAI,OAAO,CAAA,iKAAA,UAAI,CAAC,CAAC,QAAQ,KAAK,YAAY;QACxC,wDAA6B,UAAU,QAAQ,GAAG,CAAC,QAAQ,gCAAK,gBAAgB,CAAC,8CAAwB;YACvG,QAAQ,IAAI,CAAC;YACb,+CAAyB;QAC3B;QACA,OAAA,WAAA,GAAO,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAA,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,QAAA,EAAA,MAAG,MAAM,QAAQ;IAC1B;IACA,OAAA,WAAA,GAAO,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,yCAAsB;AAChC;AAEA,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAG/B,IAAI,qCAAe,IAAI;AAEvB,SAAS,iCAAW,aAAa,KAAK;IACpC,IAAI,MAAM,CAAA,iKAAA,aAAS,EAAE;IACrB,IAAI,MAAM,CAAA,iKAAA,SAAK,EAAiB;IAChC,gDAAgD;IAChD,IAAI,IAAI,OAAO,KAAK,QAAQ,CAAC,YAAY;YAWpB,6EAAA;QAVnB,0GAA0G;QAC1G,wGAAwG;QACxG,uGAAuG;QACvG,+GAA+G;QAC/G,gHAAgH;QAChH,uHAAuH;QACvH,2GAA2G;QAC3G,yGAAyG;QACzG,gFAAgF;QAChF,aAAa;QACb,IAAI,eAAA,CAAe,4DAAA,CAAA,iKAAA,UAAI,EAAE,kDAAkD,MAAA,QAAxD,8DAAA,KAAA,IAAA,KAAA,IAAA,CAAA,8EAAA,0DAA0D,iBAAiB,MAAA,QAA3E,gFAAA,KAAA,IAAA,KAAA,IAAA,4EAA6E,OAAO;QACvG,IAAI,cAAc;YAChB,IAAI,qBAAqB,mCAAa,GAAG,CAAC;YAC1C,IAAI,sBAAsB,MACxB,AACA,mCAAa,GAAG,CAAC,cAAc,mCADyD;gBAEtF,IAAI,IAAI,OAAO;gBACf,OAAO,aAAa,aAAa;YACnC;iBACK,IAAI,aAAa,aAAa,KAAK,mBAAmB,KAAK,EAAE;gBAClE,+DAA+D;gBAC/D,8DAA8D;gBAC9D,sCAAsC;gBACtC,IAAI,OAAO,GAAG,mBAAmB,EAAE;gBACnC,mCAAa,MAAM,CAAC;YACtB;QACF;QAEA,gDAAgD;QAChD,IAAI,OAAO,GAAG,EAAE,IAAI,OAAO;IAC7B;IAEA,gDAAgD;IAChD,OAAO,IAAI,OAAO;AACpB;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,MAAM,CAAA,GAAA,2KAAS,EAAE;IAErB,4EAA4E;IAC5E,yDAAyD;IACzD,IAAI,QAAQ,wCAAkB,CAAC,mCAAa,QAAQ,GAAG,CAAC,QAAQ,gCAAK,cACnE,QAAQ,IAAI,CAAC;IAGf,IAAI,UAAU,iCAAW,CAAC,CAAC;IAC3B,IAAI,SAAS,QAAQ,wCAAkB,QAAQ,GAAG,CAAC,QAAQ,KAAK,IAAwB,CAAC,IAAhB,MAA0B,EAAE,IAAI,MAAM,EAAE;IACjH,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,SAAS;AAC5C;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,KAAK,CAAA,GAAA,wKAAI,EAAE,KAAK;IACpB,IAAI,CAAC,OAAO,GAAG,CAAA,iKAAA,WAAO,EAAE;IACxB,IAAI,SAAS,UAAU,QAAQ,GAAG,CAAC,QAAQ,gCAAK,SAAS,eAAe,CAAC,UAAU,EAAE,qCAAe,MAAM,EAAE;IAC5G,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,IAAI;AACvC;AAIO,MAAM,4CAAsE,OAAO,CAAA,iKAAA,UAAI,CAAC,CAAC,QAAQ,KAAK,aAAa,2CAAqB;AAE/I,SAAS;IACP,OAAO;AACT;AAEA,SAAS;IACP,OAAO;AACT;AAEA,6DAA6D;AAC7D,SAAS,gCAAU,aAAyB;IAC1C,OAAO;IACP,OAAO,KAAO;AAChB;AAOO,SAAS;IACd,iGAAiG;IACjG,IAAI,OAAO,CAAA,iKAAA,UAAI,CAAC,CAAC,uBAAuB,KAAK,YAC3C,OAAO,CAAA,iKAAA,UAAI,CAAC,CAAC,uBAAuB,CAAC,iCAAW,mCAAa;IAG/D,sDAAsD;IACtD,OAAO,CAAA,iKAAA,aAAS,EAAE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2350, "column": 0}, "map": {"version": 3, "file": "useDeepMemo.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useDeepMemo.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/* eslint-disable rulesdir/pure-render */\n\nimport {useRef} from 'react';\n\nexport function useDeepMemo<T>(value: T, isEqual: (a: T, b: T) => boolean): T {\n  // Using a ref during render is ok here because it's only an optimization – both values are equivalent.\n  // If a render is thrown away, it'll still work the same no matter if the next render is the same or not.\n  let lastValue = useRef<T | null>(null);\n  if (value && lastValue.current && isEqual(value, lastValue.current)) {\n    value = lastValue.current;\n  }\n\n  lastValue.current = value;\n  return value;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAED,uCAAuC,GAIhC,SAAS,0CAAe,KAAQ,EAAE,OAAgC;IACvE,uGAAuG;IACvG,yGAAyG;IACzG,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAY;IACjC,IAAI,SAAS,UAAU,OAAO,IAAI,QAAQ,OAAO,UAAU,OAAO,GAChE,QAAQ,UAAU,OAAO;IAG3B,UAAU,OAAO,GAAG;IACpB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2381, "column": 0}, "map": {"version": 3, "file": "filterDOMProps.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/filterDOMProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps, GlobalDOMAttributes, LinkDOMProps} from '@react-types/shared';\n\nconst DOMPropNames = new Set([\n  'id'\n]);\n\nconst labelablePropNames = new Set([\n  'aria-label',\n  'aria-labelledby',\n  'aria-describedby',\n  'aria-details'\n]);\n\n// See LinkDOMProps in dom.d.ts.\nconst linkPropNames = new Set([\n  'href',\n  'hrefLang',\n  'target',\n  'rel',\n  'download',\n  'ping',\n  'referrerPolicy'\n]);\n\nconst globalAttrs = new Set([\n  'dir',\n  'lang',\n  'hidden',\n  'inert',\n  'translate'\n]);\n\nconst globalEvents = new Set([\n  'onClick',\n  'onAuxClick',\n  'onContextMenu',\n  'onDoubleClick',\n  'onMouseDown',\n  'onMouseEnter',\n  'onMouseLeave',\n  'onMouseMove',\n  'onMouseOut',\n  'onMouseOver',\n  'onMouseUp',\n  'onTouchCancel',\n  'onTouchEnd',\n  'onTouchMove',\n  'onTouchStart',\n  'onPointerDown',\n  'onPointerMove',\n  'onPointerUp',\n  'onPointerCancel',\n  'onPointerEnter',\n  'onPointerLeave',\n  'onPointerOver',\n  'onPointerOut',\n  'onGotPointerCapture',\n  'onLostPointerCapture',\n  'onScroll',\n  'onWheel',\n  'onAnimationStart',\n  'onAnimationEnd',\n  'onAnimationIteration',\n  'onTransitionCancel',\n  'onTransitionEnd',\n  'onTransitionRun',\n  'onTransitionStart'\n]);\n\ninterface Options {\n  /**\n   * If labelling associated aria properties should be included in the filter.\n   */\n  labelable?: boolean,\n  /** Whether the element is a link and should include DOM props for <a> elements. */\n  isLink?: boolean,\n  /** Whether to include global DOM attributes. */\n  global?: boolean,\n  /** Whether to include DOM events. */\n  events?: boolean,\n  /**\n   * A Set of other property names that should be included in the filter.\n   */\n  propNames?: Set<string>\n}\n\nconst propRe = /^(data-.*)$/;\n\n/**\n * Filters out all props that aren't valid DOM props or defined via override prop obj.\n * @param props - The component props to be filtered.\n * @param opts - Props to override.\n */\nexport function filterDOMProps(props: DOMProps & AriaLabelingProps & LinkDOMProps & GlobalDOMAttributes, opts: Options = {}): DOMProps & AriaLabelingProps & GlobalDOMAttributes {\n  let {labelable, isLink, global, events = global, propNames} = opts;\n  let filteredProps = {};\n\n  for (const prop in props) {\n    if (\n      Object.prototype.hasOwnProperty.call(props, prop) && (\n        DOMPropNames.has(prop) ||\n        (labelable && labelablePropNames.has(prop)) ||\n        (isLink && linkPropNames.has(prop)) ||\n        (global && globalAttrs.has(prop)) ||\n        (events && globalEvents.has(prop) || (prop.endsWith('Capture') && globalEvents.has(prop.slice(0, -7)))) ||\n        propNames?.has(prop) ||\n        propRe.test(prop)\n      )\n    ) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n\n  return filteredProps;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAID,MAAM,qCAAe,IAAI,IAAI;IAC3B;CACD;AAED,MAAM,2CAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;CACD;AAED,gCAAgC;AAChC,MAAM,sCAAgB,IAAI,IAAI;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,oCAAc,IAAI,IAAI;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qCAAe,IAAI,IAAI;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAmBD,MAAM,+BAAS;AAOR,SAAS,0CAAe,KAAwE,EAAE,OAAgB,CAAC,CAAC;IACzH,IAAI,EAAA,WAAC,SAAS,EAAA,QAAE,MAAM,EAAA,QAAE,MAAM,EAAA,QAAE,SAAS,MAAA,EAAA,WAAQ,SAAS,EAAC,GAAG;IAC9D,IAAI,gBAAgB,CAAC;IAErB,IAAK,MAAM,QAAQ,MACjB,IACE,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,SAC1C,CAAA,mCAAa,GAAG,CAAC,SAChB,aAAa,yCAAmB,GAAG,CAAC,SACpC,UAAU,oCAAc,GAAG,CAAC,SAC5B,UAAU,kCAAY,GAAG,CAAC,SAC1B,UAAU,mCAAa,GAAG,CAAC,SAAU,KAAK,QAAQ,CAAC,cAAc,mCAAa,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAA,OAAA,CACjG,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,GAAG,CAAC,KAAA,KACf,6BAAO,IAAI,CAAC,KAAI,GAGlB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;IAIrC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2471, "column": 0}, "map": {"version": 3, "file": "useUpdateEffect.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useUpdateEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {EffectCallback, useEffect, useRef} from 'react';\n\n// Like useEffect, but only called for updates after the initial render.\nexport function useUpdateEffect(effect: EffectCallback, dependencies: any[]): void {\n  const isInitialMount = useRef(true);\n  const lastDeps = useRef<any[] | null>(null);\n\n  useEffect(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n\n  useEffect(() => {\n    let prevDeps = lastDeps.current;\n    if (isInitialMount.current) {\n      isInitialMount.current = false;\n    } else if (!prevDeps || dependencies.some((dep, i) => !Object.is(dep, prevDeps[i]))) {\n      effect();\n    }\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAKM,SAAS,0CAAgB,MAAsB,EAAE,YAAmB;IACzE,MAAM,iBAAiB,CAAA,iKAAA,SAAK,EAAE;IAC9B,MAAM,WAAW,CAAA,iKAAA,SAAK,EAAgB;IAEtC,CAAA,iKAAA,YAAQ,EAAE;QACR,eAAe,OAAO,GAAG;QACzB,OAAO;YACL,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG,EAAE;IAEL,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,WAAW,SAAS,OAAO;QAC/B,IAAI,eAAe,OAAO,EACxB,eAAe,OAAO,GAAG;aACpB,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,CAAC,KAAK,IAAM,CAAC,OAAO,EAAE,CAAC,KAAK,QAAQ,CAAC,EAAE,IAC/E;QAEF,SAAS,OAAO,GAAG;IACnB,uDAAuD;IACzD,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2511, "column": 0}, "map": {"version": 3, "file": "useLayoutEffect.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useLayoutEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React from 'react';\n\n// During SSR, React emits a warning when calling useLayoutEffect.\n// Since neither useLayoutEffect nor useEffect run on the server,\n// we can suppress this by replace it with a noop on the server.\nexport const useLayoutEffect: typeof React.useLayoutEffect = typeof document !== 'undefined'\n  ? React.useLayoutEffect\n  : () => {};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAOM,MAAM,4CAAgD,OAAO,aAAa,cAC7E,CAAA,iKAAA,UAAI,EAAE,eAAe,GACrB,KAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2535, "column": 0}, "map": {"version": 3, "file": "useEffectEvent.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useEffectEvent.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React, {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\n// Use the earliest effect type possible. useInsertionEffect runs during the mutation phase,\n// before all layout effects, but is available only in React 18 and later.\nconst useEarlyEffect = React['useInsertionEffect'] ?? useLayoutEffect;\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useEarlyEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,OAOsB;AAFvB,4FAA4F;AAC5F,0EAA0E;AAC1E,MAAM,uCAAiB,CAAA,kDAAA,CAAA,iKAAA,UAAI,CAAC,CAAC,qBAAqB,MAAA,QAA3B,oDAAA,KAAA,IAAA,kDAA+B,CAAA,2KAAA,kBAAc;AAE7D,SAAS,0CAAmC,EAAM;IACvD,MAAM,MAAM,CAAA,iKAAA,SAAK,EAAwB;IACzC,qCAAe;QACb,IAAI,OAAO,GAAG;IAChB,GAAG;QAAC;KAAG;IACP,aAAa;IACb,OAAO,CAAA,iKAAA,cAAU,EAAK,CAAC,GAAG;QACxB,MAAM,IAAI,IAAI,OAAO;QACrB,OAAO,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,KAAO;IAChB,GAAG,EAAE;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2577, "column": 0}, "map": {"version": 3, "file": "useValueEffect.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useValueEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Dispatch, MutableRefObject, useRef, useState} from 'react';\nimport {useEffectEvent, useLayoutEffect} from './';\n\ntype SetValueAction<S> = (prev: S) => Generator<any, void, unknown>;\n\n// This hook works like `useState`, but when setting the value, you pass a generator function\n// that can yield multiple values. Each yielded value updates the state and waits for the next\n// layout effect, then continues the generator. This allows sequential updates to state to be\n// written linearly.\nexport function useValueEffect<S>(defaultValue: S | (() => S)): [S, Dispatch<SetValueAction<S>>] {\n  let [value, setValue] = useState(defaultValue);\n  let effect: MutableRefObject<Generator<S> | null> = useRef<Generator<S> | null>(null);\n\n  // Store the function in a ref so we can always access the current version\n  // which has the proper `value` in scope.\n  let nextRef = useEffectEvent(() => {\n    if (!effect.current) {\n      return;\n    }\n    // Run the generator to the next yield.\n    let newValue = effect.current.next();\n\n    // If the generator is done, reset the effect.\n    if (newValue.done) {\n      effect.current = null;\n      return;\n    }\n\n    // If the value is the same as the current value,\n    // then continue to the next yield. Otherwise,\n    // set the value in state and wait for the next layout effect.\n    if (value === newValue.value) {\n      nextRef();\n    } else {\n      setValue(newValue.value);\n    }\n  });\n\n  useLayoutEffect(() => {\n    // If there is an effect currently running, continue to the next yield.\n    if (effect.current) {\n      nextRef();\n    }\n  });\n\n  let queue = useEffectEvent(fn => {\n    effect.current = fn(value);\n    nextRef();\n  });\n\n  return [value, queue];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAWM,SAAS,0CAAkB,YAA2B;IAC3D,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,iKAAA,WAAO,EAAE;IACjC,IAAI,SAAgD,CAAA,iKAAA,SAAK,EAAuB;IAEhF,0EAA0E;IAC1E,yCAAyC;IACzC,IAAI,UAAU,CAAA,0KAAA,iBAAa,EAAE;QAC3B,IAAI,CAAC,OAAO,OAAO,EACjB;QAEF,uCAAuC;QACvC,IAAI,WAAW,OAAO,OAAO,CAAC,IAAI;QAElC,8CAA8C;QAC9C,IAAI,SAAS,IAAI,EAAE;YACjB,OAAO,OAAO,GAAG;YACjB;QACF;QAEA,iDAAiD;QACjD,8CAA8C;QAC9C,8DAA8D;QAC9D,IAAI,UAAU,SAAS,KAAK,EAC1B;aAEA,SAAS,SAAS,KAAK;IAE3B;IAEA,CAAA,2KAAA,kBAAc,EAAE;QACd,uEAAuE;QACvE,IAAI,OAAO,OAAO,EAChB;IAEJ;IAEA,IAAI,QAAQ,CAAA,0KAAA,iBAAa,EAAE,CAAA;QACzB,OAAO,OAAO,GAAG,GAAG;QACpB;IACF;IAEA,OAAO;QAAC;QAAO;KAAM;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2637, "column": 0}, "map": {"version": 3, "file": "useId.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useId.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useSSRSafeId} from '@react-aria/ssr';\nimport {useValueEffect} from './';\n\n// copied from SSRProvider.tsx to reduce exports, if needed again, consider sharing\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nexport let idsUpdaterMap: Map<string, { current: string | null }[]> = new Map();\n// This allows us to clean up the idsUpdaterMap when the id is no longer used.\n// Map is a strong reference, so unused ids wouldn't be cleaned up otherwise.\n// This can happen in suspended components where mount/unmount is not called.\nlet registry;\nif (typeof FinalizationRegistry !== 'undefined') {\n  registry = new FinalizationRegistry<string>((heldValue) => {\n    idsUpdaterMap.delete(heldValue);\n  });\n}\n\n/**\n * If a default is not provided, generate an id.\n * @param defaultId - Default component id.\n */\nexport function useId(defaultId?: string): string {\n  let [value, setValue] = useState(defaultId);\n  let nextId = useRef(null);\n\n  let res = useSSRSafeId(value);\n  let cleanupRef = useRef(null);\n\n  if (registry) {\n    registry.register(cleanupRef, res);\n  }\n\n  if (canUseDOM) {\n    const cacheIdRef = idsUpdaterMap.get(res);\n    if (cacheIdRef && !cacheIdRef.includes(nextId)) {\n      cacheIdRef.push(nextId);\n    } else {\n      idsUpdaterMap.set(res, [nextId]);\n    }\n  }\n\n  useLayoutEffect(() => {\n    let r = res;\n    return () => {\n      // In Suspense, the cleanup function may be not called\n      // when it is though, also remove it from the finalization registry.\n      if (registry) {\n        registry.unregister(cleanupRef);\n      }\n      idsUpdaterMap.delete(r);\n    };\n  }, [res]);\n\n  // This cannot cause an infinite loop because the ref is always cleaned up.\n  // eslint-disable-next-line\n  useEffect(() => {\n    let newId = nextId.current;\n    if (newId) { setValue(newId); }\n\n    return () => {\n      if (newId) { nextId.current = null; }\n    };\n  });\n\n  return res;\n}\n\n/**\n * Merges two ids.\n * Different ids will trigger a side-effect and re-render components hooked up with `useId`.\n */\nexport function mergeIds(idA: string, idB: string): string {\n  if (idA === idB) {\n    return idA;\n  }\n\n  let setIdsA = idsUpdaterMap.get(idA);\n  if (setIdsA) {\n    setIdsA.forEach(ref => (ref.current = idB));\n    return idB;\n  }\n\n  let setIdsB = idsUpdaterMap.get(idB);\n  if (setIdsB) {\n    setIdsB.forEach((ref) => (ref.current = idA));\n    return idA;\n  }\n\n  return idB;\n}\n\n/**\n * Used to generate an id, and after render, check if that id is rendered so we know\n * if we can use it in places such as labelledby.\n * @param depArray - When to recalculate if the id is in the DOM.\n */\nexport function useSlotId(depArray: ReadonlyArray<any> = []): string {\n  let id = useId();\n  let [resolvedId, setResolvedId] = useValueEffect(id);\n  let updateId = useCallback(() => {\n    setResolvedId(function *() {\n      yield id;\n\n      yield document.getElementById(id) ? id : undefined;\n    });\n  }, [id, setResolvedId]);\n\n  useLayoutEffect(updateId, [id, updateId, ...depArray]);\n\n  return resolvedId;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAOD,mFAAmF;AACnF,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAGxB,IAAI,4CAA2D,IAAI;AAC1E,8EAA8E;AAC9E,6EAA6E;AAC7E,6EAA6E;AAC7E,IAAI;AACJ,IAAI,OAAO,yBAAyB,aAClC,iCAAW,IAAI,qBAA6B,CAAC;IAC3C,0CAAc,MAAM,CAAC;AACvB;AAOK,SAAS,0CAAM,SAAkB;IACtC,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,iKAAA,WAAO,EAAE;IACjC,IAAI,SAAS,CAAA,iKAAA,SAAK,EAAE;IAEpB,IAAI,MAAM,CAAA,qKAAA,eAAW,EAAE;IACvB,IAAI,aAAa,CAAA,iKAAA,SAAK,EAAE;IAExB,IAAI,gCACF,+BAAS,QAAQ,CAAC,YAAY;IAGhC,IAAI,iCAAW;QACb,MAAM,aAAa,0CAAc,GAAG,CAAC;QACrC,IAAI,cAAc,CAAC,WAAW,QAAQ,CAAC,SACrC,WAAW,IAAI,CAAC;aAEhB,0CAAc,GAAG,CAAC,KAAK;YAAC;SAAO;IAEnC;IAEA,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,IAAI;QACR,OAAO;YACL,sDAAsD;YACtD,oEAAoE;YACpE,IAAI,gCACF,+BAAS,UAAU,CAAC;YAEtB,0CAAc,MAAM,CAAC;QACvB;IACF,GAAG;QAAC;KAAI;IAER,2EAA2E;IAC3E,2BAA2B;IAC3B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,QAAQ,OAAO,OAAO;QAC1B,IAAI,OAAS,SAAS;QAEtB,OAAO;YACL,IAAI,OAAS,OAAO,OAAO,GAAG;QAChC;IACF;IAEA,OAAO;AACT;AAMO,SAAS,0CAAS,GAAW,EAAE,GAAW;IAC/C,IAAI,QAAQ,KACV,OAAO;IAGT,IAAI,UAAU,0CAAc,GAAG,CAAC;IAChC,IAAI,SAAS;QACX,QAAQ,OAAO,CAAC,CAAA,MAAQ,IAAI,OAAO,GAAG;QACtC,OAAO;IACT;IAEA,IAAI,UAAU,0CAAc,GAAG,CAAC;IAChC,IAAI,SAAS;QACX,QAAQ,OAAO,CAAC,CAAC,MAAS,IAAI,OAAO,GAAG;QACxC,OAAO;IACT;IAEA,OAAO;AACT;AAOO,SAAS,0CAAU,WAA+B,EAAE;IACzD,IAAI,KAAK;IACT,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,0KAAA,iBAAa,EAAE;IACjD,IAAI,WAAW,CAAA,iKAAA,cAAU,EAAE;QACzB,cAAc;YACZ,MAAM;YAEN,MAAM,SAAS,cAAc,CAAC,MAAM,KAAK;QAC3C;IACF,GAAG;QAAC;QAAI;KAAc;IAEtB,CAAA,2KAAA,kBAAc,EAAE,UAAU;QAAC;QAAI;WAAa;KAAS;IAErD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2747, "column": 0}, "map": {"version": 3, "file": "useLabels.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useLabels.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\nimport {useId} from './useId';\n\n/**\n * Merges aria-label and aria-labelledby into aria-labelledby when both exist.\n * @param props - Aria label props.\n * @param defaultLabel - Default value for aria-label when not present.\n */\nexport function useLabels(props: DOMProps & AriaLabelingProps, defaultLabel?: string): DOMProps & AriaLabelingProps {\n  let {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  } = props;\n\n  // If there is both an aria-label and aria-labelledby,\n  // combine them by pointing to the element itself.\n  id = useId(id);\n  if (labelledBy && label) {\n    let ids = new Set([id, ...labelledBy.trim().split(/\\s+/)]);\n    labelledBy = [...ids].join(' ');\n  } else if (labelledBy) {\n    labelledBy = labelledBy.trim().split(/\\s+/).join(' ');\n  }\n\n  // If no labels are provided, use the default\n  if (!label && !labelledBy && defaultLabel) {\n    label = defaultLabel;\n  }\n\n  return {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAUM,SAAS,0CAAU,KAAmC,EAAE,YAAqB;IAClF,IAAI,EAAA,IACF,EAAE,EACF,cAAc,KAAK,EACnB,mBAAmB,UAAU,EAC9B,GAAG;IAEJ,sDAAsD;IACtD,kDAAkD;IAClD,KAAK,CAAA,iKAAA,QAAI,EAAE;IACX,IAAI,cAAc,OAAO;QACvB,IAAI,MAAM,IAAI,IAAI;YAAC;eAAO,WAAW,IAAI,GAAG,KAAK,CAAC;SAAO;QACzD,aAAa;eAAI;SAAI,CAAC,IAAI,CAAC;IAC7B,OAAO,IAAI,YACT,aAAa,WAAW,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC;IAGnD,6CAA6C;IAC7C,IAAI,CAAC,SAAS,CAAC,cAAc,cAC3B,QAAQ;IAGV,OAAO;YACL;QACA,cAAc;QACd,mBAAmB;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2792, "column": 0}, "map": {"version": 3, "file": "chain.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/chain.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Calls all functions in the order they were chained with the same arguments.\n */\nexport function chain(...callbacks: any[]): (...args: any[]) => void {\n  return (...args: any[]) => {\n    for (let callback of callbacks) {\n      if (typeof callback === 'function') {\n        callback(...args);\n      }\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;CAEC;;;AACM,SAAS,0CAAM,GAAG,SAAgB;IACvC,OAAO,CAAC,GAAG;QACT,KAAK,IAAI,YAAY,UACnB,IAAI,OAAO,aAAa,YACtB,YAAY;IAGlB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2820, "column": 0}, "map": {"version": 3, "file": "mergeProps.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/mergeProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from './chain';\nimport clsx from 'clsx';\nimport {mergeIds} from './useId';\n\ninterface Props {\n  [key: string]: any\n}\n\ntype PropsArg = Props | null | undefined;\n\n// taken from: https://stackoverflow.com/questions/51603250/typescript-3-parameter-list-intersection-type/51604379#51604379\ntype TupleTypes<T> = { [P in keyof T]: T[P] } extends { [key: number]: infer V } ? NullToObject<V> : never;\ntype NullToObject<T> = T extends (null | undefined) ? {} : T;\n\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n/**\n * Merges multiple props objects together. Event handlers are chained,\n * classNames are combined, and ids are deduplicated - different ids\n * will trigger a side-effect and re-render components hooked up with `useId`.\n * For all other props, the last prop object overrides all previous ones.\n * @param args - Multiple sets of props to merge together.\n */\nexport function mergeProps<T extends PropsArg[]>(...args: T): UnionToIntersection<TupleTypes<T>> {\n  // Start with a base clone of the first argument. This is a lot faster than starting\n  // with an empty object and adding properties as we go.\n  let result: Props = {...args[0]};\n  for (let i = 1; i < args.length; i++) {\n    let props = args[i];\n    for (let key in props) {\n      let a = result[key];\n      let b = props[key];\n\n      // Chain events\n      if (\n        typeof a === 'function' &&\n        typeof b === 'function' &&\n        // This is a lot faster than a regex.\n        key[0] === 'o' &&\n        key[1] === 'n' &&\n        key.charCodeAt(2) >= /* 'A' */ 65 &&\n        key.charCodeAt(2) <= /* 'Z' */ 90\n      ) {\n        result[key] = chain(a, b);\n\n        // Merge classnames, sometimes classNames are empty string which eval to false, so we just need to do a type check\n      } else if (\n        (key === 'className' || key === 'UNSAFE_className') &&\n        typeof a === 'string' &&\n        typeof b === 'string'\n      ) {\n        result[key] = clsx(a, b);\n      } else if (key === 'id' && a && b) {\n        result.id = mergeIds(a, b);\n        // Override others\n      } else {\n        result[key] = b !== undefined ? b : a;\n      }\n    }\n  }\n\n  return result as UnionToIntersection<TupleTypes<T>>;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAyBM,SAAS,0CAAiC,GAAG,IAAO;IACzD,oFAAoF;IACpF,uDAAuD;IACvD,IAAI,SAAgB;QAAC,GAAG,IAAI,CAAC,EAAE;IAAA;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,QAAQ,IAAI,CAAC,EAAE;QACnB,IAAK,IAAI,OAAO,MAAO;YACrB,IAAI,IAAI,MAAM,CAAC,IAAI;YACnB,IAAI,IAAI,KAAK,CAAC,IAAI;YAElB,eAAe;YACf,IACE,OAAO,MAAM,cACb,OAAO,MAAM,cACb,qCAAqC;YACrC,GAAG,CAAC,EAAE,KAAK,OACX,GAAG,CAAC,EAAE,KAAK,OACX,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,MAC/B,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,IAE/B,MAAM,CAAC,IAAI,GAAG,CAAA,iKAAA,QAAI,EAAE,GAAG;iBAGlB,IACJ,CAAA,QAAQ,eAAe,QAAQ,kBAAiB,KACjD,OAAO,MAAM,YACb,OAAO,MAAM,UAEb,MAAM,CAAC,IAAI,GAAG,CAAA,4IAAA,UAAG,EAAE,GAAG;iBACjB,IAAI,QAAQ,QAAQ,KAAK,GAC9B,OAAO,EAAE,GAAG,CAAA,iKAAA,WAAO,EAAE,GAAG;iBAGxB,MAAM,CAAC,IAAI,GAAG,MAAM,YAAY,IAAI;QAExC;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2868, "column": 0}, "map": {"version": 3, "file": "useDescription.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useDescription.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps} from '@react-types/shared';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useState} from 'react';\n\nlet descriptionId = 0;\nconst descriptionNodes = new Map<string, {refCount: number, element: Element}>();\n\nexport function useDescription(description?: string): AriaLabelingProps {\n  let [id, setId] = useState<string | undefined>();\n\n  useLayoutEffect(() => {\n    if (!description) {\n      return;\n    }\n\n    let desc = descriptionNodes.get(description);\n    if (!desc) {\n      let id = `react-aria-description-${descriptionId++}`;\n      setId(id);\n\n      let node = document.createElement('div');\n      node.id = id;\n      node.style.display = 'none';\n      node.textContent = description;\n      document.body.appendChild(node);\n      desc = {refCount: 0, element: node};\n      descriptionNodes.set(description, desc);\n    } else {\n      setId(desc.element.id);\n    }\n\n    desc.refCount++;\n    return () => {\n      if (desc && --desc.refCount === 0) {\n        desc.element.remove();\n        descriptionNodes.delete(description);\n      }\n    };\n  }, [description]);\n\n  return {\n    'aria-describedby': description ? id : undefined\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMD,IAAI,sCAAgB;AACpB,MAAM,yCAAmB,IAAI;AAEtB,SAAS,0CAAe,WAAoB;IACjD,IAAI,CAAC,IAAI,MAAM,GAAG,CAAA,iKAAA,WAAO;IAEzB,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,CAAC,aACH;QAGF,IAAI,OAAO,uCAAiB,GAAG,CAAC;QAChC,IAAI,CAAC,MAAM;YACT,IAAI,KAAK,CAAC,uBAAuB,EAAE,uCAAiB;YACpD,MAAM;YAEN,IAAI,OAAO,SAAS,aAAa,CAAC;YAClC,KAAK,EAAE,GAAG;YACV,KAAK,KAAK,CAAC,OAAO,GAAG;YACrB,KAAK,WAAW,GAAG;YACnB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;gBAAC,UAAU;gBAAG,SAAS;YAAI;YAClC,uCAAiB,GAAG,CAAC,aAAa;QACpC,OACE,MAAM,KAAK,OAAO,CAAC,EAAE;QAGvB,KAAK,QAAQ;QACb,OAAO;YACL,IAAI,QAAQ,EAAE,KAAK,QAAQ,KAAK,GAAG;gBACjC,KAAK,OAAO,CAAC,MAAM;gBACnB,uCAAiB,MAAM,CAAC;YAC1B;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL,oBAAoB,cAAc,KAAK;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2928, "column": 0}, "map": {"version": 3, "file": "focusWithoutScrolling.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/focusWithoutScrolling.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\n\n// This is a polyfill for element.focus({preventScroll: true});\n// Currently necessary for Safari and old Edge:\n// https://caniuse.com/#feat=mdn-api_htmlelement_focus_preventscroll_option\n// See https://bugs.webkit.org/show_bug.cgi?id=178583\n//\n\n// Original licensing for the following methods can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/calvellido/focus-options-polyfill\n\ninterface ScrollableElement {\n  element: HTMLElement,\n  scrollTop: number,\n  scrollLeft: number\n}\n\nexport function focusWithoutScrolling(element: FocusableElement): void {\n  if (supportsPreventScroll()) {\n    element.focus({preventScroll: true});\n  } else {\n    let scrollableElements = getScrollableElements(element);\n    element.focus();\n    restoreScrollPosition(scrollableElements);\n  }\n}\n\nlet supportsPreventScrollCached: boolean | null = null;\nfunction supportsPreventScroll() {\n  if (supportsPreventScrollCached == null) {\n    supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch {\n      // Ignore\n    }\n  }\n\n  return supportsPreventScrollCached;\n}\n\nfunction getScrollableElements(element: FocusableElement): ScrollableElement[] {\n  let parent = element.parentNode;\n  let scrollableElements: ScrollableElement[] = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (\n      parent.offsetHeight < parent.scrollHeight ||\n      parent.offsetWidth < parent.scrollWidth\n    ) {\n      scrollableElements.push({\n        element: parent,\n        scrollTop: parent.scrollTop,\n        scrollLeft: parent.scrollLeft\n      });\n    }\n    parent = parent.parentNode;\n  }\n\n  if (rootScrollingElement instanceof HTMLElement) {\n    scrollableElements.push({\n      element: rootScrollingElement,\n      scrollTop: rootScrollingElement.scrollTop,\n      scrollLeft: rootScrollingElement.scrollLeft\n    });\n  }\n\n  return scrollableElements;\n}\n\nfunction restoreScrollPosition(scrollableElements: ScrollableElement[]) {\n  for (let {element, scrollTop, scrollLeft} of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAoBM,SAAS,0CAAsB,OAAyB;IAC7D,IAAI,+CACF,QAAQ,KAAK,CAAC;QAAC,eAAe;IAAI;SAC7B;QACL,IAAI,qBAAqB,4CAAsB;QAC/C,QAAQ,KAAK;QACb,4CAAsB;IACxB;AACF;AAEA,IAAI,oDAA8C;AAClD,SAAS;IACP,IAAI,qDAA+B,MAAM;QACvC,oDAA8B;QAC9B,IAAI;YACF,IAAI,YAAY,SAAS,aAAa,CAAC;YACvC,UAAU,KAAK,CAAC;gBACd,IAAI,iBAAgB;oBAClB,oDAA8B;oBAC9B,OAAO;gBACT;YACF;QACF,EAAE,OAAM;QACN,SAAS;QACX;IACF;IAEA,OAAO;AACT;AAEA,SAAS,4CAAsB,OAAyB;IACtD,IAAI,SAAS,QAAQ,UAAU;IAC/B,IAAI,qBAA0C,EAAE;IAChD,IAAI,uBAAuB,SAAS,gBAAgB,IAAI,SAAS,eAAe;IAEhF,MAAO,kBAAkB,eAAe,WAAW,qBAAsB;QACvE,IACE,OAAO,YAAY,GAAG,OAAO,YAAY,IACzC,OAAO,WAAW,GAAG,OAAO,WAAW,EAEvC,mBAAmB,IAAI,CAAC;YACtB,SAAS;YACT,WAAW,OAAO,SAAS;YAC3B,YAAY,OAAO,UAAU;QAC/B;QAEF,SAAS,OAAO,UAAU;IAC5B;IAEA,IAAI,gCAAgC,aAClC,mBAAmB,IAAI,CAAC;QACtB,SAAS;QACT,WAAW,qBAAqB,SAAS;QACzC,YAAY,qBAAqB,UAAU;IAC7C;IAGF,OAAO;AACT;AAEA,SAAS,4CAAsB,kBAAuC;IACpE,KAAK,IAAI,EAAA,SAAC,OAAO,EAAA,WAAE,SAAS,EAAA,YAAE,UAAU,EAAC,IAAI,mBAAoB;QAC/D,QAAQ,SAAS,GAAG;QACpB,QAAQ,UAAU,GAAG;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3002, "column": 0}, "map": {"version": 3, "file": "isScrollable.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isScrollable.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport function isScrollable(node: Element | null, checkForOverflow?: boolean): boolean {\n  if (!node) {\n    return false;\n  }\n  let style = window.getComputedStyle(node);\n  let isScrollable = /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n\n  if (isScrollable && checkForOverflow) {\n    isScrollable = node.scrollHeight !== node.clientHeight || node.scrollWidth !== node.clientWidth;\n  }\n\n  return isScrollable;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAEM,SAAS,0CAAa,IAAoB,EAAE,gBAA0B;IAC3E,IAAI,CAAC,MACH,OAAO;IAET,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,IAAI,eAAe,gBAAgB,IAAI,CAAC,MAAM,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,SAAS;IAE1F,IAAI,gBAAgB,kBAClB,eAAe,KAAK,YAAY,KAAK,KAAK,YAAY,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAGjG,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3030, "column": 0}, "map": {"version": 3, "file": "getScrollParents.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/getScrollParents.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParents(node: Element, checkForOverflow?: boolean): Element[] {\n  const scrollParents: Element[] = [];\n\n  while (node && node !== document.documentElement) {\n    if (isScrollable(node, checkForOverflow)) {\n      scrollParents.push(node);\n    }\n    node = node.parentElement as Element;\n  }\n\n  return scrollParents;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAIM,SAAS,0CAAiB,IAAa,EAAE,gBAA0B;IACxE,MAAM,gBAA2B,EAAE;IAEnC,MAAO,QAAQ,SAAS,SAAS,eAAe,CAAE;QAChD,IAAI,CAAA,wKAAA,eAAW,EAAE,MAAM,mBACrB,cAAc,IAAI,CAAC;QAErB,OAAO,KAAK,aAAa;IAC3B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3061, "column": 0}, "map": {"version": 3, "file": "scrollIntoView.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/scrollIntoView.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getScrollParents} from './getScrollParents';\n\ninterface ScrollIntoViewportOpts {\n  /** The optional containing element of the target to be centered in the viewport. */\n  containingElement?: Element | null\n}\n\n/**\n * Scrolls `scrollView` so that `element` is visible.\n * Similar to `element.scrollIntoView({block: 'nearest'})` (not supported in Edge),\n * but doesn't affect parents above `scrollView`.\n */\nexport function scrollIntoView(scrollView: HTMLElement, element: HTMLElement): void {\n  let offsetX = relativeOffset(scrollView, element, 'left');\n  let offsetY = relativeOffset(scrollView, element, 'top');\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n  let x = scrollView.scrollLeft;\n  let y = scrollView.scrollTop;\n\n  // Account for top/left border offsetting the scroll top/Left + scroll padding\n  let {\n    borderTopWidth,\n    borderLeftWidth,\n    scrollPaddingTop,\n    scrollPaddingRight,\n    scrollPaddingBottom,\n    scrollPaddingLeft\n  } = getComputedStyle(scrollView);\n\n  let borderAdjustedX = x + parseInt(borderLeftWidth, 10);\n  let borderAdjustedY = y + parseInt(borderTopWidth, 10);\n  // Ignore end/bottom border via clientHeight/Width instead of offsetHeight/Width\n  let maxX = borderAdjustedX + scrollView.clientWidth;\n  let maxY = borderAdjustedY + scrollView.clientHeight;\n\n  // Get scroll padding values as pixels - defaults to 0 if no scroll padding\n  // is used.\n  let scrollPaddingTopNumber = parseInt(scrollPaddingTop, 10) || 0;\n  let scrollPaddingBottomNumber = parseInt(scrollPaddingBottom, 10) || 0;\n  let scrollPaddingRightNumber = parseInt(scrollPaddingRight, 10) || 0;\n  let scrollPaddingLeftNumber = parseInt(scrollPaddingLeft, 10) || 0;\n\n  if (offsetX <= x + scrollPaddingLeftNumber) {\n    x = offsetX - parseInt(borderLeftWidth, 10) - scrollPaddingLeftNumber;\n  } else if (offsetX + width > maxX - scrollPaddingRightNumber) {\n    x += offsetX + width - maxX + scrollPaddingRightNumber;\n  }\n  if (offsetY <= borderAdjustedY + scrollPaddingTopNumber) {\n    y = offsetY - parseInt(borderTopWidth, 10) - scrollPaddingTopNumber;\n  } else if (offsetY + height > maxY - scrollPaddingBottomNumber) {\n    y += offsetY + height - maxY + scrollPaddingBottomNumber;\n  }\n\n  scrollView.scrollLeft = x;\n  scrollView.scrollTop = y;\n}\n\n/**\n * Computes the offset left or top from child to ancestor by accumulating\n * offsetLeft or offsetTop through intervening offsetParents.\n */\nfunction relativeOffset(ancestor: HTMLElement, child: HTMLElement, axis: 'left'|'top') {\n  const prop = axis === 'left' ? 'offsetLeft' : 'offsetTop';\n  let sum = 0;\n  while (child.offsetParent) {\n    sum += child[prop];\n    if (child.offsetParent === ancestor) {\n      // Stop once we have found the ancestor we are interested in.\n      break;\n    } else if (child.offsetParent.contains(ancestor)) {\n      // If the ancestor is not `position:relative`, then we stop at\n      // _its_ offset parent, and we subtract off _its_ offset, so that\n      // we end up with the proper offset from child to ancestor.\n      sum -= ancestor[prop];\n      break;\n    }\n    child = child.offsetParent as HTMLElement;\n  }\n  return sum;\n}\n\n/**\n * Scrolls the `targetElement` so it is visible in the viewport. Accepts an optional `opts.containingElement`\n * that will be centered in the viewport prior to scrolling the targetElement into view. If scrolling is prevented on\n * the body (e.g. targetElement is in a popover), this will only scroll the scroll parents of the targetElement up to but not including the body itself.\n */\nexport function scrollIntoViewport(targetElement: Element | null, opts?: ScrollIntoViewportOpts): void {\n  if (targetElement && document.contains(targetElement)) {\n    let root = document.scrollingElement || document.documentElement;\n    let isScrollPrevented = window.getComputedStyle(root).overflow === 'hidden';\n    // If scrolling is not currently prevented then we aren’t in a overlay nor is a overlay open, just use element.scrollIntoView to bring the element into view\n    if (!isScrollPrevented) {\n      let {left: originalLeft, top: originalTop} = targetElement.getBoundingClientRect();\n\n      // use scrollIntoView({block: 'nearest'}) instead of .focus to check if the element is fully in view or not since .focus()\n      // won't cause a scroll if the element is already focused and doesn't behave consistently when an element is partially out of view horizontally vs vertically\n      targetElement?.scrollIntoView?.({block: 'nearest'});\n      let {left: newLeft, top: newTop} = targetElement.getBoundingClientRect();\n      // Account for sub pixel differences from rounding\n      if ((Math.abs(originalLeft - newLeft) > 1) || (Math.abs(originalTop - newTop) > 1)) {\n        opts?.containingElement?.scrollIntoView?.({block: 'center', inline: 'center'});\n        targetElement.scrollIntoView?.({block: 'nearest'});\n      }\n    } else {\n      let scrollParents = getScrollParents(targetElement);\n      // If scrolling is prevented, we don't want to scroll the body since it might move the overlay partially offscreen and the user can't scroll it back into view.\n      for (let scrollParent of scrollParents) {\n        scrollIntoView(scrollParent as HTMLElement, targetElement as HTMLElement);\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAcM,SAAS,0CAAe,UAAuB,EAAE,OAAoB;IAC1E,IAAI,UAAU,qCAAe,YAAY,SAAS;IAClD,IAAI,UAAU,qCAAe,YAAY,SAAS;IAClD,IAAI,QAAQ,QAAQ,WAAW;IAC/B,IAAI,SAAS,QAAQ,YAAY;IACjC,IAAI,IAAI,WAAW,UAAU;IAC7B,IAAI,IAAI,WAAW,SAAS;IAE5B,8EAA8E;IAC9E,IAAI,EAAA,gBACF,cAAc,EAAA,iBACd,eAAe,EAAA,kBACf,gBAAgB,EAAA,oBAChB,kBAAkB,EAAA,qBAClB,mBAAmB,EAAA,mBACnB,iBAAiB,EAClB,GAAG,iBAAiB;IAErB,IAAI,kBAAkB,IAAI,SAAS,iBAAiB;IACpD,IAAI,kBAAkB,IAAI,SAAS,gBAAgB;IACnD,gFAAgF;IAChF,IAAI,OAAO,kBAAkB,WAAW,WAAW;IACnD,IAAI,OAAO,kBAAkB,WAAW,YAAY;IAEpD,2EAA2E;IAC3E,WAAW;IACX,IAAI,yBAAyB,SAAS,kBAAkB,OAAO;IAC/D,IAAI,4BAA4B,SAAS,qBAAqB,OAAO;IACrE,IAAI,2BAA2B,SAAS,oBAAoB,OAAO;IACnE,IAAI,0BAA0B,SAAS,mBAAmB,OAAO;IAEjE,IAAI,WAAW,IAAI,yBACjB,IAAI,UAAU,SAAS,iBAAiB,MAAM;SACzC,IAAI,UAAU,QAAQ,OAAO,0BAClC,KAAK,UAAU,QAAQ,OAAO;IAEhC,IAAI,WAAW,kBAAkB,wBAC/B,IAAI,UAAU,SAAS,gBAAgB,MAAM;SACxC,IAAI,UAAU,SAAS,OAAO,2BACnC,KAAK,UAAU,SAAS,OAAO;IAGjC,WAAW,UAAU,GAAG;IACxB,WAAW,SAAS,GAAG;AACzB;AAEA;;;CAGC,GACD,SAAS,qCAAe,QAAqB,EAAE,KAAkB,EAAE,IAAkB;IACnF,MAAM,OAAO,SAAS,SAAS,eAAe;IAC9C,IAAI,MAAM;IACV,MAAO,MAAM,YAAY,CAAE;QACzB,OAAO,KAAK,CAAC,KAAK;QAClB,IAAI,MAAM,YAAY,KAAK,UAEzB;aACK,IAAI,MAAM,YAAY,CAAC,QAAQ,CAAC,WAAW;YAChD,8DAA8D;YAC9D,iEAAiE;YACjE,2DAA2D;YAC3D,OAAO,QAAQ,CAAC,KAAK;YACrB;QACF;QACA,QAAQ,MAAM,YAAY;IAC5B;IACA,OAAO;AACT;AAOO,SAAS,0CAAmB,aAA6B,EAAE,IAA6B;IAC7F,IAAI,iBAAiB,SAAS,QAAQ,CAAC,gBAAgB;QACrD,IAAI,OAAO,SAAS,gBAAgB,IAAI,SAAS,eAAe;QAChE,IAAI,oBAAoB,OAAO,gBAAgB,CAAC,MAAM,QAAQ,KAAK;QACnE,4JAA4J;QAC5J,IAAI,CAAC,mBAAmB;gBAGtB,AACA,0HAD0H,mCACmC;YAC7J;YAJA,IAAI,EAAC,MAAM,YAAY,EAAE,KAAK,WAAW,EAAC,GAAG,cAAc,qBAAqB;YAIhF,kBAAA,QAAA,kBAAA,KAAA,IAAA,KAAA,IAAA,CAAA,gCAAA,cAAe,cAAc,MAAA,QAA7B,kCAAA,KAAA,IAAA,KAAA,IAAA,8BAAA,IAAA,CAAA,eAAgC;gBAAC,OAAO;YAAS;YACjD,IAAI,EAAC,MAAM,OAAO,EAAE,KAAK,MAAM,EAAC,GAAG,cAAc,qBAAqB;YACtE,kDAAkD;YAClD,IAAK,KAAK,GAAG,CAAC,eAAe,WAAW,KAAO,KAAK,GAAG,CAAC,cAAc,UAAU,GAAI;oBAClF,wCAAA,yBACA;gBADA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,0BAAA,KAAM,iBAAiB,MAAA,QAAvB,4BAAA,KAAA,IAAA,KAAA,IAAA,CAAA,yCAAA,wBAAyB,cAAc,MAAA,QAAvC,2CAAA,KAAA,IAAA,KAAA,IAAA,uCAAA,IAAA,CAAA,yBAA0C;oBAAC,OAAO;oBAAU,QAAQ;gBAAQ;iBAC5E,iCAAA,cAAc,cAAc,MAAA,QAA5B,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAA,IAAA,CAAA,eAA+B;oBAAC,OAAO;gBAAS;YAClD;QACF,OAAO;YACL,IAAI,gBAAgB,CAAA,4KAAA,mBAAe,EAAE;YACrC,+JAA+J;YAC/J,KAAK,IAAI,gBAAgB,cACvB,0CAAe,cAA6B;QAEhD;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3163, "column": 0}, "map": {"version": 3, "file": "getScrollParent.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/getScrollParent.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParent(node: Element, checkForOverflow?: boolean): Element {\n  let scrollableNode: Element | null = node;\n  if (isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  while (scrollableNode && !isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  return scrollableNode || document.scrollingElement || document.documentElement;\n}\n\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAIM,SAAS,0CAAgB,IAAa,EAAE,gBAA0B;IACvE,IAAI,iBAAiC;IACrC,IAAI,CAAA,wKAAA,eAAW,EAAE,gBAAgB,mBAC/B,iBAAiB,eAAe,aAAa;IAG/C,MAAO,kBAAkB,CAAC,CAAA,wKAAA,eAAW,EAAE,gBAAgB,kBACrD,iBAAiB,eAAe,aAAa;IAG/C,OAAO,kBAAkB,SAAS,gBAAgB,IAAI,SAAS,eAAe;AAChF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3192, "column": 0}, "map": {"version": 3, "file": "domHelpers.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/domHelpers.ts"], "sourcesContent": ["export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,4CAAmB,CAAC;QACxB;IAAP,OAAO,CAAA,oBAAA,OAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAI,aAAa,MAAA,QAAjB,sBAAA,KAAA,IAAA,oBAAqB;AAC9B;AAEO,MAAM,4CAAiB,CAC5B;IAEA,IAAI,MAAM,YAAY,MAAM,GAAG,MAAM,KAAK,IACxC,OAAO;IAGT,MAAM,MAAM,0CAAiB;IAC7B,OAAO,IAAI,WAAW,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,6BAAO,KAAc;IAC5B,OAAO,UAAU,QACf,OAAO,UAAU,YACjB,cAAc,SACd,OAAQ,MAAe,QAAQ,KAAK;AACxC;AAKO,SAAS,0CAAa,IAAiB;IAC5C,OAAO,6BAAO,SACZ,KAAK,QAAQ,KAAK,KAAK,sBAAsB,IAC7C,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3222, "column": 0}, "map": {"version": 3, "file": "isElementVisible.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isElementVisible.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerWindow} from './domHelpers';\n\nconst supportsCheckVisibility = typeof Element !== 'undefined' && 'checkVisibility' in Element.prototype;\n\nfunction isStyleVisible(element: Element) {\n  const windowObject = getOwnerWindow(element);\n  if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) {\n    return false;\n  }\n\n  let {display, visibility} = element.style;\n\n  let isVisible = (\n    display !== 'none' &&\n    visibility !== 'hidden' &&\n    visibility !== 'collapse'\n  );\n\n  if (isVisible) {\n    const {getComputedStyle} = element.ownerDocument.defaultView as unknown as Window;\n    let {display: computedDisplay, visibility: computedVisibility} = getComputedStyle(element);\n\n    isVisible = (\n      computedDisplay !== 'none' &&\n      computedVisibility !== 'hidden' &&\n      computedVisibility !== 'collapse'\n    );\n  }\n\n  return isVisible;\n}\n\nfunction isAttributeVisible(element: Element, childElement?: Element) {\n  return (\n    !element.hasAttribute('hidden') &&\n    // Ignore HiddenSelect when tree walking.\n    !element.hasAttribute('data-react-aria-prevent-focus') &&\n    (element.nodeName === 'DETAILS' &&\n      childElement &&\n      childElement.nodeName !== 'SUMMARY'\n      ? element.hasAttribute('open')\n      : true)\n  );\n}\n\n/**\n * Adapted from https://github.com/testing-library/jest-dom and\n * https://github.com/vuejs/vue-test-utils-next/.\n * Licensed under the MIT License.\n * @param element - Element to evaluate for display or visibility.\n */\nexport function isElementVisible(element: Element, childElement?: Element): boolean {\n  if (supportsCheckVisibility) {\n    return element.checkVisibility() && !element.closest('[data-react-aria-prevent-focus]');\n  }\n\n  return (\n    element.nodeName !== '#comment' &&\n    isStyleVisible(element) &&\n    isAttributeVisible(element, childElement) &&\n    (!element.parentElement || isElementVisible(element.parentElement, element))\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAID,MAAM,gDAA0B,OAAO,YAAY,eAAe,qBAAqB,QAAQ,SAAS;AAExG,SAAS,qCAAe,OAAgB;IACtC,MAAM,eAAe,CAAA,sKAAA,iBAAa,EAAE;IACpC,IAAI,CAAE,CAAA,mBAAmB,aAAa,WAAU,KAAM,CAAE,CAAA,mBAAmB,aAAa,UAAS,GAC/F,OAAO;IAGT,IAAI,EAAA,SAAC,OAAO,EAAA,YAAE,UAAU,EAAC,GAAG,QAAQ,KAAK;IAEzC,IAAI,YACF,YAAY,UACZ,eAAe,YACf,eAAe;IAGjB,IAAI,WAAW;QACb,MAAM,EAAA,kBAAC,gBAAgB,EAAC,GAAG,QAAQ,aAAa,CAAC,WAAW;QAC5D,IAAI,EAAC,SAAS,eAAe,EAAE,YAAY,kBAAkB,EAAC,GAAG,iBAAiB;QAElF,YACE,oBAAoB,UACpB,uBAAuB,YACvB,uBAAuB;IAE3B;IAEA,OAAO;AACT;AAEA,SAAS,yCAAmB,OAAgB,EAAE,YAAsB;IAClE,OACE,CAAC,QAAQ,YAAY,CAAC,aACtB,yCAAyC;IACzC,CAAC,QAAQ,YAAY,CAAC,oCACrB,CAAA,QAAQ,QAAQ,KAAK,aACpB,gBACA,aAAa,QAAQ,KAAK,YACxB,QAAQ,YAAY,CAAC,UACrB,IAAG;AAEX;AAQO,SAAS,0CAAiB,OAAgB,EAAE,YAAsB;IACvE,IAAI,+CACF,OAAO,QAAQ,eAAe,MAAM,CAAC,QAAQ,OAAO,CAAC;IAGvD,OACE,QAAQ,QAAQ,KAAK,cACrB,qCAAe,YACf,yCAAmB,SAAS,iBAC3B,CAAA,CAAC,QAAQ,aAAa,IAAI,0CAAiB,QAAQ,aAAa,EAAE,QAAO;AAE9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3266, "column": 0}, "map": {"version": 3, "file": "isFocusable.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isFocusable.ts"], "sourcesContent": ["/*\n * Copyright 2025 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isElementVisible} from './isElementVisible';\n\nconst focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])',\n  'permission'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nfunction isInert(element: Element): boolean {\n  let node: Element | null = element;\n  while (node != null) {\n    if (node instanceof node.ownerDocument.defaultView!.HTMLElement && node.inert) {\n      return true;\n    }\n\n    node = node.parentElement;\n  }\n\n  return false;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAID,MAAM,0CAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mDAA6B,wCAAkB,IAAI,CAAC,qBAAqB;AAE/E,wCAAkB,IAAI,CAAC;AACvB,MAAM,kDAA4B,wCAAkB,IAAI,CAAC;AAElD,SAAS,0CAAY,OAAgB;IAC1C,OAAO,QAAQ,OAAO,CAAC,qDAA+B,CAAA,4KAAA,mBAAe,EAAE,YAAY,CAAC,8BAAQ;AAC9F;AAEO,SAAS,0CAAW,OAAgB;IACzC,OAAO,QAAQ,OAAO,CAAC,oDAA8B,CAAA,4KAAA,mBAAe,EAAE,YAAY,CAAC,8BAAQ;AAC7F;AAEA,SAAS,8BAAQ,OAAgB;IAC/B,IAAI,OAAuB;IAC3B,MAAO,QAAQ,KAAM;QACnB,IAAI,gBAAgB,KAAK,aAAa,CAAC,WAAW,CAAE,WAAW,IAAI,KAAK,KAAK,EAC3E,OAAO;QAGT,OAAO,KAAK,aAAa;IAC3B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3323, "column": 0}, "map": {"version": 3, "file": "platform.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/platform.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  let brands = window.navigator['userAgentData']?.brands;\n  return Array.isArray(brands) && brands.some((brand: {brand: string, version: string}) => re.test(brand.brand)) ||\n    re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n\n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac: () => boolean = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone: () => boolean = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad: () => boolean = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS: () => boolean = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice: () => boolean = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit: () => boolean = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome: () => boolean = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid: () => boolean = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox: () => boolean = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;;;;;;AAkBK,QAAQ,GAAG,CAAC,QAAQ,KAAK;AAhB/B,SAAS,oCAAc,EAAU;QAIlB;IAHb,IAAI,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,MACvD,OAAO;IAET,IAAI,SAAA,CAAS,kCAAA,OAAO,SAAS,CAAC,gBAAgB,MAAA,QAAjC,oCAAA,KAAA,IAAA,KAAA,IAAA,gCAAmC,MAAM;IACtD,OAAO,MAAM,OAAO,CAAC,WAAW,OAAO,IAAI,CAAC,CAAC,QAA4C,GAAG,IAAI,CAAC,MAAM,KAAK,MAC1G,GAAG,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AACtC;AAEA,SAAS,mCAAa,EAAU;QAElB;IADZ,OAAO,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,OACxD,GAAG,IAAI,CAAC,CAAA,CAAA,kCAAA,OAAO,SAAS,CAAC,gBAAgB,MAAA,QAAjC,oCAAA,KAAA,IAAA,KAAA,IAAA,gCAAmC,QAAQ,KAAI,OAAO,SAAS,CAAC,QAAQ,IAChF;AACN;AAEA,SAAS,6BAAO,EAAiB;IAC/B,uCACE,OAAO;;IAAA;IAGT,IAAI,MAAsB;IAC1B,OAAO;QACL,IAAI,OAAO,MACT,MAAM;QAER,OAAO;IACT;AACF;AAEO,MAAM,4CAAuB,6BAAO;IACzC,OAAO,mCAAa;AACtB;AAEO,MAAM,2CAA0B,6BAAO;IAC5C,OAAO,mCAAa;AACtB;AAEO,MAAM,4CAAwB,6BAAO;IAC1C,OAAO,mCAAa,aAClB,yFAAyF;IACxF,+CAAW,UAAU,cAAc,GAAG;AAC3C;AAEO,MAAM,4CAAuB,6BAAO;IACzC,OAAO,8CAAc;AACvB;AAEO,MAAM,4CAA+B,6BAAO;IACjD,OAAO,+CAAW;AACpB;AAEO,MAAM,4CAA0B,6BAAO;IAC5C,OAAO,oCAAc,mBAAmB,CAAC;AAC3C;AAEO,MAAM,4CAA0B,6BAAO;IAC5C,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAA2B,6BAAO;IAC7C,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAA2B,6BAAO;IAC7C,OAAO,oCAAc;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3401, "column": 0}, "map": {"version": 3, "file": "runAfterTransition.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/runAfterTransition.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We store a global list of elements that are currently transitioning,\n// mapped to a set of CSS properties that are transitioning for that element.\n// This is necessary rather than a simple count of transitions because of browser\n// bugs, e.g. Chrome sometimes fires both transitionend and transitioncancel rather\n// than one or the other. So we need to track what's actually transitioning so that\n// we can ignore these duplicate events.\nlet transitionsByElement = new Map<EventTarget, Set<string>>();\n\n// A list of callbacks to call once there are no transitioning elements.\nlet transitionCallbacks = new Set<() => void>();\n\nfunction setupGlobalEvents() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  function isTransitionEvent(event: Event): event is TransitionEvent {\n    return 'propertyName' in event;\n  }\n\n  let onTransitionStart = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Add the transitioning property to the list for this element.\n    let transitions = transitionsByElement.get(e.target);\n    if (!transitions) {\n      transitions = new Set();\n      transitionsByElement.set(e.target, transitions);\n\n      // The transitioncancel event must be registered on the element itself, rather than as a global\n      // event. This enables us to handle when the node is deleted from the document while it is transitioning.\n      // In that case, the cancel event would have nowhere to bubble to so we need to handle it directly.\n      e.target.addEventListener('transitioncancel', onTransitionEnd, {\n        once: true\n      });\n    }\n\n    transitions.add(e.propertyName);\n  };\n\n  let onTransitionEnd = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Remove property from list of transitioning properties.\n    let properties = transitionsByElement.get(e.target);\n    if (!properties) {\n      return;\n    }\n\n    properties.delete(e.propertyName);\n\n    // If empty, remove transitioncancel event, and remove the element from the list of transitioning elements.\n    if (properties.size === 0) {\n      e.target.removeEventListener('transitioncancel', onTransitionEnd);\n      transitionsByElement.delete(e.target);\n    }\n\n    // If no transitioning elements, call all of the queued callbacks.\n    if (transitionsByElement.size === 0) {\n      for (let cb of transitionCallbacks) {\n        cb();\n      }\n\n      transitionCallbacks.clear();\n    }\n  };\n\n  document.body.addEventListener('transitionrun', onTransitionStart);\n  document.body.addEventListener('transitionend', onTransitionEnd);\n}\n\nif (typeof document !== 'undefined') {\n  if (document.readyState !== 'loading') {\n    setupGlobalEvents();\n  } else {\n    document.addEventListener('DOMContentLoaded', setupGlobalEvents);\n  }\n}\n\n/**\n * Cleans up any elements that are no longer in the document.\n * This is necessary because we can't rely on transitionend events to fire\n * for elements that are removed from the document while transitioning.\n */\nfunction cleanupDetachedElements() {\n  for (const [eventTarget] of transitionsByElement) {\n    // Similar to `eventTarget instanceof Element && !eventTarget.isConnected`, but avoids\n    // the explicit instanceof check, since it may be different in different contexts.\n    if ('isConnected' in eventTarget && !eventTarget.isConnected) {\n      transitionsByElement.delete(eventTarget);\n    }\n  }\n}\n\nexport function runAfterTransition(fn: () => void): void {\n  // Wait one frame to see if an animation starts, e.g. a transition on mount.\n  requestAnimationFrame(() => {\n    cleanupDetachedElements();\n    // If no transitions are running, call the function immediately.\n    // Otherwise, add it to a list of callbacks to run at the end of the animation.\n    if (transitionsByElement.size === 0) {\n      fn();\n    } else {\n      transitionCallbacks.add(fn);\n    }\n  });\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED,uEAAuE;AACvE,6EAA6E;AAC7E,iFAAiF;AACjF,mFAAmF;AACnF,mFAAmF;AACnF,wCAAwC;;;;AACxC,IAAI,6CAAuB,IAAI;AAE/B,wEAAwE;AACxE,IAAI,4CAAsB,IAAI;AAE9B,SAAS;IACP,IAAI,OAAO,WAAW,aACpB;IAGF,SAAS,kBAAkB,KAAY;QACrC,OAAO,kBAAkB;IAC3B;IAEA,IAAI,oBAAoB,CAAC;QACvB,IAAI,CAAC,kBAAkB,MAAM,CAAC,EAAE,MAAM,EACpC;QAEF,+DAA+D;QAC/D,IAAI,cAAc,2CAAqB,GAAG,CAAC,EAAE,MAAM;QACnD,IAAI,CAAC,aAAa;YAChB,cAAc,IAAI;YAClB,2CAAqB,GAAG,CAAC,EAAE,MAAM,EAAE;YAEnC,+FAA+F;YAC/F,yGAAyG;YACzG,mGAAmG;YACnG,EAAE,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,iBAAiB;gBAC7D,MAAM;YACR;QACF;QAEA,YAAY,GAAG,CAAC,EAAE,YAAY;IAChC;IAEA,IAAI,kBAAkB,CAAC;QACrB,IAAI,CAAC,kBAAkB,MAAM,CAAC,EAAE,MAAM,EACpC;QAEF,yDAAyD;QACzD,IAAI,aAAa,2CAAqB,GAAG,CAAC,EAAE,MAAM;QAClD,IAAI,CAAC,YACH;QAGF,WAAW,MAAM,CAAC,EAAE,YAAY;QAEhC,2GAA2G;QAC3G,IAAI,WAAW,IAAI,KAAK,GAAG;YACzB,EAAE,MAAM,CAAC,mBAAmB,CAAC,oBAAoB;YACjD,2CAAqB,MAAM,CAAC,EAAE,MAAM;QACtC;QAEA,kEAAkE;QAClE,IAAI,2CAAqB,IAAI,KAAK,GAAG;YACnC,KAAK,IAAI,MAAM,0CACb;YAGF,0CAAoB,KAAK;QAC3B;IACF;IAEA,SAAS,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;IAChD,SAAS,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;AAClD;AAEA,IAAI,OAAO,aAAa,aAAA;IACtB,IAAI,SAAS,UAAU,KAAK,WAC1B;SAEA,SAAS,gBAAgB,CAAC,oBAAoB;;AAIlD;;;;CAIC,GACD,SAAS;IACP,KAAK,MAAM,CAAC,YAAY,IAAI,2CAC1B,AACA,kFAAkF,IADI;IAEtF,IAAI,iBAAiB,eAAe,CAAC,YAAY,WAAW,EAC1D,2CAAqB,MAAM,CAAC;AAGlC;AAEO,SAAS,0CAAmB,EAAc;IAC/C,4EAA4E;IAC5E,sBAAsB;QACpB;QACA,gEAAgE;QAChE,+EAA+E;QAC/E,IAAI,2CAAqB,IAAI,KAAK,GAChC;aAEA,0CAAoB,GAAG,CAAC;IAE5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3494, "column": 0}, "map": {"version": 3, "file": "useSyncRef.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useSyncRef.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from './';\n\ninterface ContextValue<T> {\n  ref?: MutableRefObject<T | null>\n}\n\n// Syncs ref from context with ref passed to hook\nexport function useSyncRef<T>(context?: ContextValue<T> | null, ref?: RefObject<T | null>): void {\n  useLayoutEffect(() => {\n    if (context && context.ref && ref) {\n      context.ref.current = ref.current;\n      return () => {\n        if (context.ref) {\n          context.ref.current = null;\n        }\n      };\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAWM,SAAS,0CAAc,OAAgC,EAAE,GAAyB;IACvF,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,WAAW,QAAQ,GAAG,IAAI,KAAK;YACjC,QAAQ,GAAG,CAAC,OAAO,GAAG,IAAI,OAAO;YACjC,OAAO;gBACL,IAAI,QAAQ,GAAG,EACb,QAAQ,GAAG,CAAC,OAAO,GAAG;YAE1B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3527, "column": 0}, "map": {"version": 3, "file": "useGlobalListeners.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useGlobalListeners.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef} from 'react';\n\ninterface GlobalListeners {\n  addGlobalListener<K extends keyof WindowEventMap>(el: Window, type: K, listener: (this: Document, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void,\n  removeGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void,\n  removeGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void,\n  removeAllGlobalListeners(): void\n}\n\nexport function useGlobalListeners(): GlobalListeners {\n  let globalListeners = useRef(new Map());\n  let addGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = options?.once ? (...args) => {\n      globalListeners.current.delete(listener);\n      listener(...args);\n    } : listener;\n    globalListeners.current.set(listener, {type, eventTarget, fn, options});\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    let fn = globalListeners.current.get(listener)?.fn || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = useCallback(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n\n   \n  useEffect(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n\n  return {addGlobalListener, removeGlobalListener, removeAllGlobalListeners};\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS;IACd,IAAI,kBAAkB,CAAA,iKAAA,SAAK,EAAE,IAAI;IACjC,IAAI,oBAAoB,CAAA,iKAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;QAChE,8EAA8E;QAC9E,IAAI,KAAK,CAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,IAAI,IAAG,CAAC,GAAG;YAC3B,gBAAgB,OAAO,CAAC,MAAM,CAAC;YAC/B,YAAY;QACd,IAAI;QACJ,gBAAgB,OAAO,CAAC,GAAG,CAAC,UAAU;kBAAC;yBAAM;gBAAa;qBAAI;QAAO;QACrE,YAAY,gBAAgB,CAAC,MAAM,IAAI;IACzC,GAAG,EAAE;IACL,IAAI,uBAAuB,CAAA,iKAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;YAC1D;QAAT,IAAI,KAAK,CAAA,CAAA,+BAAA,gBAAgB,OAAO,CAAC,GAAG,CAAC,SAAA,MAAA,QAA5B,iCAAA,KAAA,IAAA,KAAA,IAAA,6BAAuC,EAAE,KAAI;QACtD,YAAY,mBAAmB,CAAC,MAAM,IAAI;QAC1C,gBAAgB,OAAO,CAAC,MAAM,CAAC;IACjC,GAAG,EAAE;IACL,IAAI,2BAA2B,CAAA,iKAAA,cAAU,EAAE;QACzC,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;YACtC,qBAAqB,MAAM,WAAW,EAAE,MAAM,IAAI,EAAE,KAAK,MAAM,OAAO;QACxE;IACF,GAAG;QAAC;KAAqB;IAGzB,CAAA,iKAAA,YAAQ,EAAE;QACR,OAAO;IACT,GAAG;QAAC;KAAyB;IAE7B,OAAO;2BAAC;8BAAmB;kCAAsB;IAAwB;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3590, "column": 0}, "map": {"version": 3, "file": "DOMFunctions.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/shadowdom/DOMFunctions.ts"], "sourcesContent": ["// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,kIAAkI;AAQ3H,SAAS,0CACd,IAA6B,EAC7B,SAAkC;IAElC,IAAI,CAAC,CAAA,qKAAA,YAAQ,KACX,OAAO,aAAa,OAAO,KAAK,QAAQ,CAAC,aAAa;IAGxD,IAAI,CAAC,QAAQ,CAAC,WACZ,OAAO;IAGT,IAAI,cAAqD;IAEzD,MAAO,gBAAgB,KAAM;QAC3B,IAAI,gBAAgB,MAClB,OAAO;QAGT,IAAK,YAAgC,OAAO,KAAK,UAC9C,YAAgC,YAAY,EAC7C,AACA,cAAe,OADM,KAC0B,YAAY,CAAE,UAAU;aAClE,IAAI,CAAA,sKAAA,eAAW,EAAE,cACtB,AACA,cAAc,YAAY,EADE,EACE;aAE9B,cAAc,YAAY,UAAU;IAExC;IAEA,OAAO;AACT;AAKO,MAAM,4CAAmB,CAAC,MAAgB,QAAQ;QAOvD;IANA,IAAI,CAAC,CAAA,qKAAA,YAAQ,KACX,OAAO,IAAI,aAAa;IAE1B,IAAI,gBAAgC,IAAI,aAAa;IAErD,MAAO,iBAAiB,gBAAgB,iBAAA,CAAA,CACxC,4BAAA,cAAc,UAAU,MAAA,QAAxB,8BAAA,KAAA,IAAA,KAAA,IAAA,0BAA0B,aAAa,EACrC,gBAAgB,cAAc,UAAU,CAAC,aAAa;IAGxD,OAAO;AACT;AAKO,SAAS,0CAAgC,KAAQ;IACtD,IAAI,CAAA,qKAAA,YAAQ,OAAQ,MAAM,MAAM,CAAiB,UAAU,EAAE;QAC3D,IAAI,MAAM,YAAY,EACpB,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAElC;IACA,OAAO,MAAM,MAAM;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3633, "column": 0}, "map": {"version": 3, "file": "openLink.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/openLink.tsx"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {focusWithoutScrolling, isMac, isWebKit} from './index';\nimport {Href, LinkDOMProps, RouterOptions} from '@react-types/shared';\nimport {isFirefox, isIPad} from './platform';\nimport React, {createContext, DOMAttributes, JSX, MouseEvent as ReactMouseEvent, ReactNode, useContext, useMemo} from 'react';\n\ninterface Router {\n  isNative: boolean,\n  open: (target: Element, modifiers: Modifiers, href: Href, routerOptions: RouterOptions | undefined) => void,\n  useHref: (href: Href) => string\n}\n\nconst RouterContext = createContext<Router>({\n  isNative: true,\n  open: openSyntheticLink,\n  useHref: (href) => href\n});\n\ninterface RouterProviderProps {\n  navigate: (path: Href, routerOptions: RouterOptions | undefined) => void,\n  useHref?: (href: Href) => string,\n  children: ReactNode\n}\n\n/**\n * A RouterProvider accepts a `navigate` function from a framework or client side router,\n * and provides it to all nested React Aria links to enable client side navigation.\n */\nexport function RouterProvider(props: RouterProviderProps): JSX.Element {\n  let {children, navigate, useHref} = props;\n\n  let ctx = useMemo(() => ({\n    isNative: false,\n    open: (target: Element, modifiers: Modifiers, href: Href, routerOptions: RouterOptions | undefined) => {\n      getSyntheticLink(target, link => {\n        if (shouldClientNavigate(link, modifiers)) {\n          navigate(href, routerOptions);\n        } else {\n          openLink(link, modifiers);\n        }\n      });\n    },\n    useHref: useHref || ((href) => href)\n  }), [navigate, useHref]);\n\n  return (\n    <RouterContext.Provider value={ctx}>\n      {children}\n    </RouterContext.Provider>\n  );\n}\n\nexport function useRouter(): Router {\n  return useContext(RouterContext);\n}\n\ninterface Modifiers {\n  metaKey?: boolean,\n  ctrlKey?: boolean,\n  altKey?: boolean,\n  shiftKey?: boolean\n}\n\nexport function shouldClientNavigate(link: HTMLAnchorElement, modifiers: Modifiers): boolean {\n  // Use getAttribute here instead of link.target. Firefox will default link.target to \"_parent\" when inside an iframe.\n  let target = link.getAttribute('target');\n  return (\n    (!target || target === '_self') &&\n    link.origin === location.origin &&\n    !link.hasAttribute('download') &&\n    !modifiers.metaKey && // open in new tab (mac)\n    !modifiers.ctrlKey && // open in new tab (windows)\n    !modifiers.altKey && // download\n    !modifiers.shiftKey\n  );\n}\n\nexport function openLink(target: HTMLAnchorElement, modifiers: Modifiers, setOpening = true): void {\n  let {metaKey, ctrlKey, altKey, shiftKey} = modifiers;\n\n  // Firefox does not recognize keyboard events as a user action by default, and the popup blocker\n  // will prevent links with target=\"_blank\" from opening. However, it does allow the event if the\n  // Command/Control key is held, which opens the link in a background tab. This seems like the best we can do.\n  // See https://bugzilla.mozilla.org/show_bug.cgi?id=257870 and https://bugzilla.mozilla.org/show_bug.cgi?id=746640.\n  if (isFirefox() && window.event?.type?.startsWith('key') && target.target === '_blank') {\n    if (isMac()) {\n      metaKey = true;\n    } else {\n      ctrlKey = true;\n    }\n  }\n\n  // WebKit does not support firing click events with modifier keys, but does support keyboard events.\n  // https://github.com/WebKit/WebKit/blob/c03d0ac6e6db178f90923a0a63080b5ca210d25f/Source/WebCore/html/HTMLAnchorElement.cpp#L184\n  let event = isWebKit() && isMac() && !isIPad() && process.env.NODE_ENV !== 'test'\n    // @ts-ignore - keyIdentifier is a non-standard property, but it's what webkit expects\n    ? new KeyboardEvent('keydown', {keyIdentifier: 'Enter', metaKey, ctrlKey, altKey, shiftKey})\n    : new MouseEvent('click', {metaKey, ctrlKey, altKey, shiftKey, bubbles: true, cancelable: true});\n  (openLink as any).isOpening = setOpening;\n  focusWithoutScrolling(target);\n  target.dispatchEvent(event);\n  (openLink as any).isOpening = false;\n}\n// https://github.com/parcel-bundler/parcel/issues/8724\n(openLink as any).isOpening = false;\n\nfunction getSyntheticLink(target: Element, open: (link: HTMLAnchorElement) => void) {\n  if (target instanceof HTMLAnchorElement) {\n    open(target);\n  } else if (target.hasAttribute('data-href')) {\n    let link = document.createElement('a');\n    link.href = target.getAttribute('data-href')!;\n    if (target.hasAttribute('data-target')) {\n      link.target = target.getAttribute('data-target')!;\n    }\n    if (target.hasAttribute('data-rel')) {\n      link.rel = target.getAttribute('data-rel')!;\n    }\n    if (target.hasAttribute('data-download')) {\n      link.download = target.getAttribute('data-download')!;\n    }\n    if (target.hasAttribute('data-ping')) {\n      link.ping = target.getAttribute('data-ping')!;\n    }\n    if (target.hasAttribute('data-referrer-policy')) {\n      link.referrerPolicy = target.getAttribute('data-referrer-policy')!;\n    }\n    target.appendChild(link);\n    open(link);\n    target.removeChild(link);\n  }\n}\n\nfunction openSyntheticLink(target: Element, modifiers: Modifiers) {\n  getSyntheticLink(target, link => openLink(link, modifiers));\n}\n\nexport function useSyntheticLinkProps(props: LinkDOMProps): DOMAttributes<HTMLElement> {\n  let router = useRouter();\n  const href = router.useHref(props.href ?? '');\n  return {\n    'data-href': props.href ? href : undefined,\n    'data-target': props.target,\n    'data-rel': props.rel,\n    'data-download': props.download,\n    'data-ping': props.ping,\n    'data-referrer-policy': props.referrerPolicy\n  } as DOMAttributes<HTMLElement>;\n}\n\n/** @deprecated - For backward compatibility. */\nexport function getSyntheticLinkProps(props: LinkDOMProps): DOMAttributes<HTMLElement> {\n  return {\n    'data-href': props.href,\n    'data-target': props.target,\n    'data-rel': props.rel,\n    'data-download': props.download,\n    'data-ping': props.ping,\n    'data-referrer-policy': props.referrerPolicy\n  } as DOMAttributes<HTMLElement>;\n}\n\nexport function useLinkProps(props?: LinkDOMProps): LinkDOMProps {\n  let router = useRouter();\n  const href = router.useHref(props?.href ?? '');\n  return {\n    href: props?.href ? href : undefined,\n    target: props?.target,\n    rel: props?.rel,\n    download: props?.download,\n    ping: props?.ping,\n    referrerPolicy: props?.referrerPolicy\n  };\n}\n\nexport function handleLinkClick(e: ReactMouseEvent, router: Router, href: Href | undefined, routerOptions: RouterOptions | undefined): void {\n  // If a custom router is provided, prevent default and forward if this link should client navigate.\n  if (\n    !router.isNative &&\n    e.currentTarget instanceof HTMLAnchorElement &&\n    e.currentTarget.href &&\n    // If props are applied to a router Link component, it may have already prevented default.\n    !e.isDefaultPrevented() &&\n    shouldClientNavigate(e.currentTarget, e) &&\n    href\n  ) {\n    e.preventDefault();\n    router.open(e.currentTarget, e, href, routerOptions);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAyGoD,QAAQ,GAAG,CAAC,QAAQ;;;;;;;AAzGxE;;;;;;;;;;CAUC,GAaD,MAAM,sCAAA,WAAA,GAAgB,CAAA,iKAAA,gBAAY,EAAU;IAC1C,UAAU;IACV,MAAM;IACN,SAAS,CAAC,OAAS;AACrB;AAYO,SAAS,0CAAe,KAA0B;IACvD,IAAI,EAAA,UAAC,QAAQ,EAAA,UAAE,QAAQ,EAAA,SAAE,OAAO,EAAC,GAAG;IAEpC,IAAI,MAAM,CAAA,iKAAA,UAAM,EAAE,IAAO,CAAA;YACvB,UAAU;YACV,MAAM,CAAC,QAAiB,WAAsB,MAAY;gBACxD,uCAAiB,QAAQ,CAAA;oBACvB,IAAI,0CAAqB,MAAM,YAC7B,SAAS,MAAM;yBAEf,0CAAS,MAAM;gBAEnB;YACF;YACA,SAAS,WAAY,CAAA,CAAC,OAAS,IAAG;QACpC,CAAA,GAAI;QAAC;QAAU;KAAQ;IAEvB,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,oCAAc,QAAQ,EAAA;QAAC,OAAO;OAC5B;AAGP;AAEO,SAAS;IACd,OAAO,CAAA,iKAAA,aAAS,EAAE;AACpB;AASO,SAAS,0CAAqB,IAAuB,EAAE,SAAoB;IAChF,qHAAqH;IACrH,IAAI,SAAS,KAAK,YAAY,CAAC;IAC/B,OACG,CAAA,CAAC,UAAU,WAAW,OAAM,KAC7B,KAAK,MAAM,KAAK,SAAS,MAAM,IAC/B,CAAC,KAAK,YAAY,CAAC,eACnB,CAAC,UAAU,OAAO,IAAI,wBAAwB;IAC9C,CAAC,UAAU,OAAO,IAAI,4BAA4B;IAClD,CAAC,UAAU,MAAM,IAAI,WAAW;IAChC,CAAC,UAAU,QAAQ;AAEvB;AAEO,SAAS,0CAAS,MAAyB,EAAE,SAAoB,EAAE,aAAa,IAAI;QAOtE,oBAAA;IANnB,IAAI,EAAA,SAAC,OAAO,EAAA,SAAE,OAAO,EAAA,QAAE,MAAM,EAAA,UAAE,QAAQ,EAAC,GAAG;IAE3C,gGAAgG;IAChG,gGAAgG;IAChG,6GAA6G;IAC7G,mHAAmH;IACnH,IAAI,CAAA,oKAAA,YAAQ,OAAA,CAAA,CAAO,gBAAA,OAAO,KAAK,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,CAAA,qBAAA,cAAc,IAAI,MAAA,QAAlB,uBAAA,KAAA,IAAA,KAAA,IAAA,mBAAoB,UAAU,CAAC,MAAA,KAAU,OAAO,MAAM,KAAK,UAAA;QAC5E,IAAI,CAAA,oKAAA,QAAI,KACN,UAAU;aAEV,UAAU;;IAId,oGAAoG;IACpG,gIAAgI;IAChI,IAAI,QAAQ,CAAA,oKAAA,WAAO,OAAO,CAAA,mKAAA,SAAI,OAAO,CAAC,CAAA,oKAAA,SAAK,2DAAgC,SAEvE,IAAI,cAAc,WAAW;QAAC,eAAe;iBAAS;iBAAS;gBAAS;kBAAQ;IAAQ,KACxF,IAAI,WAAW,SAAS;iBAAC;iBAAS;gBAAS;kBAAQ;QAAU,SAAS;QAAM,YAAY;IAAI;IAC/F,0CAAiB,SAAS,GAAG;IAC9B,CAAA,iLAAA,wBAAoB,EAAE;IACtB,OAAO,aAAa,CAAC;IACpB,0CAAiB,SAAS,GAAG;AAChC;AACA,uDAAuD;AACtD,0CAAiB,SAAS,GAAG;AAE9B,SAAS,uCAAiB,MAAe,EAAE,IAAuC;IAChF,IAAI,kBAAkB,mBACpB,KAAK;SACA,IAAI,OAAO,YAAY,CAAC,cAAc;QAC3C,IAAI,OAAO,SAAS,aAAa,CAAC;QAClC,KAAK,IAAI,GAAG,OAAO,YAAY,CAAC;QAChC,IAAI,OAAO,YAAY,CAAC,gBACtB,KAAK,MAAM,GAAG,OAAO,YAAY,CAAC;QAEpC,IAAI,OAAO,YAAY,CAAC,aACtB,KAAK,GAAG,GAAG,OAAO,YAAY,CAAC;QAEjC,IAAI,OAAO,YAAY,CAAC,kBACtB,KAAK,QAAQ,GAAG,OAAO,YAAY,CAAC;QAEtC,IAAI,OAAO,YAAY,CAAC,cACtB,KAAK,IAAI,GAAG,OAAO,YAAY,CAAC;QAElC,IAAI,OAAO,YAAY,CAAC,yBACtB,KAAK,cAAc,GAAG,OAAO,YAAY,CAAC;QAE5C,OAAO,WAAW,CAAC;QACnB,KAAK;QACL,OAAO,WAAW,CAAC;IACrB;AACF;AAEA,SAAS,wCAAkB,MAAe,EAAE,SAAoB;IAC9D,uCAAiB,QAAQ,CAAA,OAAQ,0CAAS,MAAM;AAClD;AAEO,SAAS,0CAAsB,KAAmB;IACvD,IAAI,SAAS;QACe;IAA5B,MAAM,OAAO,OAAO,OAAO,CAAC,CAAA,cAAA,MAAM,IAAI,MAAA,QAAV,gBAAA,KAAA,IAAA,cAAc;IAC1C,OAAO;QACL,aAAa,MAAM,IAAI,GAAG,OAAO;QACjC,eAAe,MAAM,MAAM;QAC3B,YAAY,MAAM,GAAG;QACrB,iBAAiB,MAAM,QAAQ;QAC/B,aAAa,MAAM,IAAI;QACvB,wBAAwB,MAAM,cAAc;IAC9C;AACF;AAGO,SAAS,0CAAsB,KAAmB;IACvD,OAAO;QACL,aAAa,MAAM,IAAI;QACvB,eAAe,MAAM,MAAM;QAC3B,YAAY,MAAM,GAAG;QACrB,iBAAiB,MAAM,QAAQ;QAC/B,aAAa,MAAM,IAAI;QACvB,wBAAwB,MAAM,cAAc;IAC9C;AACF;AAEO,SAAS,0CAAa,KAAoB;IAC/C,IAAI,SAAS;QACe;IAA5B,MAAM,OAAO,OAAO,OAAO,CAAC,CAAA,cAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,IAAI,MAAA,QAAX,gBAAA,KAAA,IAAA,cAAe;IAC3C,OAAO;QACL,MAAM,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,IAAI,IAAG,OAAO;QAC3B,MAAM,EAAE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,MAAM;QACrB,GAAG,EAAE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,GAAG;QACf,QAAQ,EAAE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,QAAQ;QACzB,IAAI,EAAE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,IAAI;QACjB,cAAc,EAAE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,cAAc;IACvC;AACF;AAEO,SAAS,0CAAgB,CAAkB,EAAE,MAAc,EAAE,IAAsB,EAAE,aAAwC;IAClI,mGAAmG;IACnG,IACE,CAAC,OAAO,QAAQ,IAChB,EAAE,aAAa,YAAY,qBAC3B,EAAE,aAAa,CAAC,IAAI,IACpB,0FAA0F;IAC1F,CAAC,EAAE,kBAAkB,MACrB,0CAAqB,EAAE,aAAa,EAAE,MACtC,MACA;QACA,EAAE,cAAc;QAChB,OAAO,IAAI,CAAC,EAAE,aAAa,EAAE,GAAG,MAAM;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3799, "column": 0}, "map": {"version": 3, "file": "isVirtualEvent.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isVirtualEvent.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isAndroid} from './platform';\n\n// Original licensing for the following method can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/blob/3c713d513195a53788b3f8bb4b70279d68b15bcc/packages/react-interactions/events/src/dom/shared/index.js#L74-L87\n\n// Keyboards, Assistive Technologies, and element.click() all produce a \"virtual\"\n// click event. This is a method of inferring such clicks. Every browser except\n// IE 11 only sets a zero value of \"detail\" for click events that are \"virtual\".\n// However, IE 11 uses a zero value for all click events. For IE 11 we rely on\n// the quirk that it produces click events that are of type PointerEvent, and\n// where only the \"virtual\" click lacks a pointerType field.\n\nexport function isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as PointerEvent).pointerType === '' && event.isTrusted) {\n    return true;\n  }\n\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if (isAndroid() && (event as PointerEvent).pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n\n  return event.detail === 0 && !(event as PointerEvent).pointerType;\n}\n\nexport function isVirtualPointerEvent(event: PointerEvent): boolean {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return (\n    (!isAndroid() && event.width === 0 && event.height === 0) ||\n    (event.width === 1 &&\n      event.height === 1 &&\n      event.pressure === 0 &&\n      event.detail === 0 &&\n      event.pointerType === 'mouse'\n    )\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAeM,SAAS,0CAAe,KAAgC;IAC7D,0BAA0B;IAC1B,IAAK,MAAuB,WAAW,KAAK,MAAM,MAAM,SAAS,EAC/D,OAAO;IAGT,oIAAoI;IACpI,oIAAoI;IACpI,qCAAqC;IACrC,IAAI,CAAA,oKAAA,YAAQ,OAAQ,MAAuB,WAAW,EACpD,OAAO,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,KAAK;IAGrD,OAAO,MAAM,MAAM,KAAK,KAAK,CAAE,MAAuB,WAAW;AACnE;AAEO,SAAS,0CAAsB,KAAmB;IACvD,yEAAyE;IACzE,uFAAuF;IACvF,kGAAkG;IAClG,mHAAmH;IACnH,oHAAoH;IACpH,8DAA8D;IAC9D,OACG,CAAC,CAAA,oKAAA,YAAQ,OAAO,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KACtD,MAAM,KAAK,KAAK,KACf,MAAM,MAAM,KAAK,KACjB,MAAM,QAAQ,KAAK,KACnB,MAAM,MAAM,KAAK,KACjB,MAAM,WAAW,KAAK;AAG5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3841, "column": 0}, "map": {"version": 3, "file": "useEvent.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useEvent.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useEvent<K extends keyof GlobalEventHandlersEventMap>(\n  ref: RefObject<EventTarget | null>,\n  event: K | (string & {}),\n  handler?: (this: Document, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n): void {\n  let handleEvent = useEffectEvent(handler);\n  let isDisabled = handler == null;\n\n  useEffect(() => {\n    if (isDisabled || !ref.current) {\n      return;\n    }\n\n    let element = ref.current;\n    element.addEventListener(event, handleEvent as EventListener, options);\n    return () => {\n      element.removeEventListener(event, handleEvent as EventListener, options);\n    };\n  }, [ref, event, options, isDisabled, handleEvent]);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMM,SAAS,0CACd,GAAkC,EAClC,KAAwB,EACxB,OAAqE,EACrE,OAA2C;IAE3C,IAAI,cAAc,CAAA,0KAAA,iBAAa,EAAE;IACjC,IAAI,aAAa,WAAW;IAE5B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,cAAc,CAAC,IAAI,OAAO,EAC5B;QAGF,IAAI,UAAU,IAAI,OAAO;QACzB,QAAQ,gBAAgB,CAAC,OAAO,aAA8B;QAC9D,OAAO;YACL,QAAQ,mBAAmB,CAAC,OAAO,aAA8B;QACnE;IACF,GAAG;QAAC;QAAK;QAAO;QAAS;QAAY;KAAY;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3884, "column": 0}, "map": {"version": 3, "file": "useObjectRef.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useObjectRef.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, useCallback, useMemo, useRef} from 'react';\n\n/**\n * Offers an object ref for a given callback ref or an object ref. Especially\n * helfpul when passing forwarded refs (created using `React.forwardRef`) to\n * React Aria hooks.\n *\n * @param ref The original ref intended to be used.\n * @returns An object ref that updates the given ref.\n * @see https://react.dev/reference/react/forwardRef\n */\nexport function useObjectRef<T>(ref?: ((instance: T | null) => (() => void) | void) | MutableRefObject<T | null> | null): MutableRefObject<T | null> {\n  const objRef: MutableRefObject<T | null> = useRef<T>(null);\n  const cleanupRef: MutableRefObject<(() => void) | void> = useRef(undefined);\n\n  const refEffect = useCallback(\n    (instance: T | null) => {\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return () => {\n          if (typeof refCleanup === 'function') {\n            refCleanup();\n          } else {\n            refCallback(null);\n          }\n        };\n      } else if (ref) {\n        ref.current = instance;\n        return () => {\n          ref.current = null;\n        };\n      }\n    },\n    [ref]\n  );\n\n  return useMemo(\n    () => ({\n      get current() {\n        return objRef.current;\n      },\n      set current(value) {\n        objRef.current = value;\n        if (cleanupRef.current) {\n          cleanupRef.current();\n          cleanupRef.current = undefined;\n        }\n\n        if (value != null) {\n          cleanupRef.current = refEffect(value);\n        }\n      }\n    }),\n    [refEffect]\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS,0CAAgB,GAAuF;IACrH,MAAM,SAAqC,CAAA,iKAAA,SAAK,EAAK;IACrD,MAAM,aAAoD,CAAA,iKAAA,SAAK,EAAE;IAEjE,MAAM,YAAY,CAAA,iKAAA,cAAU,EAC1B,CAAC;QACC,IAAI,OAAO,QAAQ,YAAY;YAC7B,MAAM,cAAc;YACpB,MAAM,aAAa,YAAY;YAC/B,OAAO;gBACL,IAAI,OAAO,eAAe,YACxB;qBAEA,YAAY;YAEhB;QACF,OAAO,IAAI,KAAK;YACd,IAAI,OAAO,GAAG;YACd,OAAO;gBACL,IAAI,OAAO,GAAG;YAChB;QACF;IACF,GACA;QAAC;KAAI;IAGP,OAAO,CAAA,iKAAA,UAAM,EACX,IAAO,CAAA;YACL,IAAI,WAAU;gBACZ,OAAO,OAAO,OAAO;YACvB;YACA,IAAI,SAAQ,MAAO;gBACjB,OAAO,OAAO,GAAG;gBACjB,IAAI,WAAW,OAAO,EAAE;oBACtB,WAAW,OAAO;oBAClB,WAAW,OAAO,GAAG;gBACvB;gBAEA,IAAI,SAAS,MACX,WAAW,OAAO,GAAG,UAAU;YAEnC;QACF,CAAA,GACA;QAAC;KAAU;AAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3943, "column": 0}, "map": {"version": 3, "file": "mergeRefs.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/mergeRefs.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, Ref} from 'react';\n\n/**\n * Merges multiple refs into one. Works with either callback or object refs.\n */\nexport function mergeRefs<T>(...refs: Array<Ref<T> | MutableRefObject<T> | null | undefined>): Ref<T> {\n  if (refs.length === 1 && refs[0]) {\n    return refs[0];\n  }\n\n  return (value: T | null) => {\n    let hasCleanup = false;\n\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, value);\n      hasCleanup ||= typeof cleanup == 'function';\n      return cleanup;\n    });\n\n    if (hasCleanup) {\n      return () => {\n        cleanups.forEach((cleanup, i) => {\n          if (typeof cleanup === 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        });\n      };\n    }\n  };\n}\n\nfunction setRef<T>(ref: Ref<T> | MutableRefObject<T> | null | undefined, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref != null) {\n    ref.current = value;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAOM,SAAS,0CAAa,GAAG,IAA4D;IAC1F,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,EAC9B,OAAO,IAAI,CAAC,EAAE;IAGhB,OAAO,CAAC;QACN,IAAI,aAAa;QAEjB,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA;YACxB,MAAM,UAAU,6BAAO,KAAK;YAC5B,cAAA,CAAA,aAAe,OAAO,WAAW,UAAA;YACjC,OAAO;QACT;QAEA,IAAI,YACF,OAAO;YACL,SAAS,OAAO,CAAC,CAAC,SAAS;gBACzB,IAAI,OAAO,YAAY,YACrB;qBAEA,6BAAO,IAAI,CAAC,EAAE,EAAE;YAEpB;QACF;IAEJ;AACF;AAEA,SAAS,6BAAU,GAAoD,EAAE,KAAQ;IAC/E,IAAI,OAAO,QAAQ,YACjB,OAAO,IAAI;SACN,IAAI,OAAO,MAChB,IAAI,OAAO,GAAG;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3985, "column": 0}, "map": {"version": 3, "file": "ShadowTreeWalker.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/shadowdom/ShadowTreeWalker.ts"], "sourcesContent": ["// https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/ShadowTreeWalker.ts\n\nimport {nodeContains} from './DOMFunctions';\nimport {shadowDOM} from '@react-stately/flags';\n\nexport class ShadowTreeWalker implements TreeWalker {\n  public readonly filter: NodeFilter | null;\n  public readonly root: Node;\n  public readonly whatToShow: number;\n\n  private _doc: Document;\n  private _walkerStack: Array<TreeWalker> = [];\n  private _currentNode: Node;\n  private _currentSetFor: Set<TreeWalker> = new Set();\n\n  constructor(\n      doc: Document,\n      root: Node,\n      whatToShow?: number,\n      filter?: NodeFilter | null\n    ) {\n    this._doc = doc;\n    this.root = root;\n    this.filter = filter ?? null;\n    this.whatToShow = whatToShow ?? NodeFilter.SHOW_ALL;\n    this._currentNode = root;\n\n    this._walkerStack.unshift(\n      doc.createTreeWalker(root, whatToShow, this._acceptNode)\n    );\n\n    const shadowRoot = (root as Element).shadowRoot;\n\n    if (shadowRoot) {\n      const walker = this._doc.createTreeWalker(\n        shadowRoot,\n        this.whatToShow,\n        {acceptNode: this._acceptNode}\n      );\n\n      this._walkerStack.unshift(walker);\n    }\n  }\n\n  private _acceptNode = (node: Node): number => {\n    if (node.nodeType === Node.ELEMENT_NODE) {\n      const shadowRoot = (node as Element).shadowRoot;\n\n      if (shadowRoot) {\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        this._walkerStack.unshift(walker);\n\n        return NodeFilter.FILTER_ACCEPT;\n      } else {\n        if (typeof this.filter === 'function') {\n          return this.filter(node);\n        } else if (this.filter?.acceptNode) {\n          return this.filter.acceptNode(node);\n        } else if (this.filter === null) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n      }\n    }\n\n    return NodeFilter.FILTER_SKIP;\n  };\n\n  public get currentNode(): Node {\n    return this._currentNode;\n  }\n\n  public set currentNode(node: Node) {\n    if (!nodeContains(this.root, node)) {\n      throw new Error(\n        'Cannot set currentNode to a node that is not contained by the root node.'\n      );\n    }\n\n    const walkers: TreeWalker[] = [];\n    let curNode: Node | null | undefined = node;\n    let currentWalkerCurrentNode = node;\n\n    this._currentNode = node;\n\n    while (curNode && curNode !== this.root) {\n      if (curNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n        const shadowRoot = curNode as ShadowRoot;\n\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        walkers.push(walker);\n\n        walker.currentNode = currentWalkerCurrentNode;\n\n        this._currentSetFor.add(walker);\n\n        curNode = currentWalkerCurrentNode = shadowRoot.host;\n      } else {\n        curNode = curNode.parentNode;\n      }\n    }\n\n    const walker = this._doc.createTreeWalker(\n      this.root,\n      this.whatToShow,\n      {acceptNode: this._acceptNode}\n    );\n\n    walkers.push(walker);\n\n    walker.currentNode = currentWalkerCurrentNode;\n\n    this._currentSetFor.add(walker);\n\n    this._walkerStack = walkers;\n  }\n\n  public get doc(): Document {\n    return this._doc;\n  }\n\n  public firstChild(): Node | null {\n    let currentNode = this.currentNode;\n    let newNode = this.nextNode();\n    if (!nodeContains(currentNode, newNode)) {\n      this.currentNode = currentNode;\n      return null;\n    }\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public lastChild(): Node | null {\n    let walker = this._walkerStack[0];\n    let newNode = walker.lastChild();\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public nextNode(): Node | null {\n    const nextNode = this._walkerStack[0].nextNode();\n\n    if (nextNode) {\n      const shadowRoot = (nextNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(nextNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(nextNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          this.currentNode = nextNode;\n          return nextNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (nextNode) {\n        this.currentNode = nextNode;\n      }\n      return nextNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n  public previousNode(): Node | null {\n    const currentWalker = this._walkerStack[0];\n\n    if (currentWalker.currentNode === currentWalker.root) {\n      if (this._currentSetFor.has(currentWalker)) {\n        this._currentSetFor.delete(currentWalker);\n\n        if (this._walkerStack.length > 1) {\n          this._walkerStack.shift();\n          let newNode = this.previousNode();\n          if (newNode) {\n            this.currentNode = newNode;\n          }\n          return newNode;\n        } else {\n          return null;\n        }\n      }\n\n      return null;\n    }\n\n    const previousNode = currentWalker.previousNode();\n\n    if (previousNode) {\n      const shadowRoot = (previousNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(previousNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(previousNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          if (previousNode) {\n            this.currentNode = previousNode;\n          }\n          return previousNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.lastChild();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (previousNode) {\n        this.currentNode = previousNode;\n      }\n      return previousNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.previousNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n    /**\n     * @deprecated\n     */\n  public nextSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public previousSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public parentNode(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n}\n\n/**\n * ShadowDOM safe version of document.createTreeWalker.\n */\nexport function createShadowTreeWalker(\n    doc: Document,\n    root: Node,\n    whatToShow?: number,\n    filter?: NodeFilter | null\n): TreeWalker {\n  if (shadowDOM()) {\n    return new ShadowTreeWalker(doc, root, whatToShow, filter);\n  }\n  return doc.createTreeWalker(root, whatToShow, filter);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,0HAA0H;AAKnH,MAAM;IAmEX,IAAW,cAAoB;QAC7B,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,IAAW,YAAY,IAAU,EAAE;QACjC,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,IAAI,CAAC,IAAI,EAAE,OAC3B,MAAM,IAAI,MACR;QAIJ,MAAM,UAAwB,EAAE;QAChC,IAAI,UAAmC;QACvC,IAAI,2BAA2B;QAE/B,IAAI,CAAC,YAAY,GAAG;QAEpB,MAAO,WAAW,YAAY,IAAI,CAAC,IAAI,CACrC,IAAI,QAAQ,QAAQ,KAAK,KAAK,sBAAsB,EAAE;YACpD,MAAM,aAAa;YAEnB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;gBAAC,YAAY,IAAI,CAAC,WAAW;YAAA;YAG/B,QAAQ,IAAI,CAAC;YAEb,OAAO,WAAW,GAAG;YAErB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YAExB,UAAU,2BAA2B,WAAW,IAAI;QACtD,OACE,UAAU,QAAQ,UAAU;QAIhC,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,UAAU,EACf;YAAC,YAAY,IAAI,CAAC,WAAW;QAAA;QAG/B,QAAQ,IAAI,CAAC;QAEb,OAAO,WAAW,GAAG;QAErB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA,IAAW,MAAgB;QACzB,OAAO,IAAI,CAAC,IAAI;IAClB;IAEO,aAA0B;QAC/B,IAAI,cAAc,IAAI,CAAC,WAAW;QAClC,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,aAAa,UAAU;YACvC,IAAI,CAAC,WAAW,GAAG;YACnB,OAAO;QACT;QACA,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;QAErB,OAAO;IACT;IAEO,YAAyB;QAC9B,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE;QACjC,IAAI,UAAU,OAAO,SAAS;QAC9B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;QAErB,OAAO;IACT;IAEO,WAAwB;QAC7B,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ;QAE9C,IAAI,UAAU;YACZ,MAAM,aAAc,SAAqB,UAAU;YAEnD,IAAI,YAAY;oBAKH;gBAJX,IAAI;gBAEJ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,aAAa,IAAI,CAAC,MAAM,CAAC;qBACpB,IAAA,CAAI,eAAA,IAAI,CAAC,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,UAAU,EAChC,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAGtC,IAAI,eAAe,WAAW,aAAa,EAAE;oBAC3C,IAAI,CAAC,WAAW,GAAG;oBACnB,OAAO;gBACT;gBAEA,4DAA4D;gBAC5D,qBAAqB;gBACrB,IAAI,UAAU,IAAI,CAAC,QAAQ;gBAC3B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT;YAEA,IAAI,UACF,IAAI,CAAC,WAAW,GAAG;YAErB,OAAO;QACT,OAAO;YACL,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;gBAEvB,IAAI,UAAU,IAAI,CAAC,QAAQ;gBAC3B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT,OACE,OAAO;QAEX;IACF;IAEO,eAA4B;QACjC,MAAM,gBAAgB,IAAI,CAAC,YAAY,CAAC,EAAE;QAE1C,IAAI,cAAc,WAAW,KAAK,cAAc,IAAI,EAAE;YACpD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB;gBAC1C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBAE3B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;oBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;oBACvB,IAAI,UAAU,IAAI,CAAC,YAAY;oBAC/B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;oBAErB,OAAO;gBACT,OACE,OAAO;YAEX;YAEA,OAAO;QACT;QAEA,MAAM,eAAe,cAAc,YAAY;QAE/C,IAAI,cAAc;YAChB,MAAM,aAAc,aAAyB,UAAU;YAEvD,IAAI,YAAY;oBAKH;gBAJX,IAAI;gBAEJ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,aAAa,IAAI,CAAC,MAAM,CAAC;qBACpB,IAAA,CAAI,eAAA,IAAI,CAAC,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,UAAU,EAChC,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAGtC,IAAI,eAAe,WAAW,aAAa,EAAE;oBAC3C,IAAI,cACF,IAAI,CAAC,WAAW,GAAG;oBAErB,OAAO;gBACT;gBAEA,4DAA4D;gBAC5D,qBAAqB;gBACrB,IAAI,UAAU,IAAI,CAAC,SAAS;gBAC5B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT;YAEA,IAAI,cACF,IAAI,CAAC,WAAW,GAAG;YAErB,OAAO;QACT,OAAO;YACL,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;gBAEvB,IAAI,UAAU,IAAI,CAAC,YAAY;gBAC/B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT,OACE,OAAO;QAEX;IACF;IAEE;;KAEC,GACI,cAA2B;QAChC,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IAEE;;KAEC,GACI,kBAA+B;QACpC,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IAEE;;KAEC,GACI,aAA0B;QAC/B,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IA/RA,YACI,GAAa,EACb,IAAU,EACV,UAAmB,EACnB,MAA0B,CAC1B;aATI,YAAA,GAAkC,EAAE;aAEpC,cAAA,GAAkC,IAAI;aA+BtC,WAAA,GAAc,CAAC;YACrB,IAAI,KAAK,QAAQ,KAAK,KAAK,YAAY,EAAE;gBACvC,MAAM,aAAc,KAAiB,UAAU;gBAE/C,IAAI,YAAY;oBACd,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;wBAAC,YAAY,IAAI,CAAC,WAAW;oBAAA;oBAG/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;oBAE1B,OAAO,WAAW,aAAa;gBACjC,OAAO;wBAGM;oBAFX,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,OAAO,IAAI,CAAC,MAAM,CAAC;yBACd,IAAA,CAAI,eAAA,IAAI,CAAC,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,UAAU,EAChC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;yBACzB,IAAI,IAAI,CAAC,MAAM,KAAK,MACzB,OAAO,WAAW,aAAa;gBAEnC;YACF;YAEA,OAAO,WAAW,WAAW;QAC/B;QAjDE,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG,WAAA,QAAA,WAAA,KAAA,IAAA,SAAU;QACxB,IAAI,CAAC,UAAU,GAAG,eAAA,QAAA,eAAA,KAAA,IAAA,aAAc,WAAW,QAAQ;QACnD,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI,CAAC,YAAY,CAAC,OAAO,CACvB,IAAI,gBAAgB,CAAC,MAAM,YAAY,IAAI,CAAC,WAAW;QAGzD,MAAM,aAAc,KAAiB,UAAU;QAE/C,IAAI,YAAY;YACd,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;gBAAC,YAAY,IAAI,CAAC,WAAW;YAAA;YAG/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;QAC5B;IACF;AAqQF;AAKO,SAAS,0CACZ,GAAa,EACb,IAAU,EACV,UAAmB,EACnB,MAA0B;IAE5B,IAAI,CAAA,qKAAA,YAAQ,KACV,OAAO,IAAI,0CAAiB,KAAK,MAAM,YAAY;IAErD,OAAO,IAAI,gBAAgB,CAAC,MAAM,YAAY;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4186, "column": 0}, "map": {"version": 3, "file": "useFormReset.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useFormReset.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useFormReset<T>(\n  ref: RefObject<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null> | undefined,\n  initialValue: T,\n  onReset: (value: T) => void\n): void {\n  let handleReset = useEffectEvent(() => {\n    if (onReset) {\n      onReset(initialValue);\n    }\n  });\n\n  useEffect(() => {\n    let form = ref?.current?.form;\n\n    form?.addEventListener('reset', handleReset);\n    return () => {\n      form?.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMM,SAAS,0CACd,GAA6F,EAC7F,YAAe,EACf,OAA2B;IAE3B,IAAI,cAAc,CAAA,0KAAA,iBAAa,EAAE;QAC/B,IAAI,SACF,QAAQ;IAEZ;IAEA,CAAA,iKAAA,YAAQ,EAAE;YACG;QAAX,IAAI,OAAO,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,CAAA,eAAA,IAAK,OAAO,MAAA,QAAZ,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAc,IAAI;QAE7B,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,gBAAgB,CAAC,SAAS;QAChC,OAAO;YACL,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,mBAAmB,CAAC,SAAS;QACrC;IACF,GAAG;QAAC;QAAK;KAAY;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4227, "column": 0}, "map": {"version": 3, "file": "LiveAnnouncer.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/live-announcer/dist/packages/%40react-aria/live-announcer/src/LiveAnnouncer.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\ntype Assertiveness = 'assertive' | 'polite';\n\n/* Inspired by https://github.com/AlmeroSteyn/react-aria-live */\nconst LIVEREGION_TIMEOUT_DELAY = 7000;\n\nlet liveAnnouncer: LiveAnnouncer | null = null;\n\ntype Message = string | {'aria-labelledby': string};\n\n/**\n * Announces the message using screen reader technology.\n */\nexport function announce(\n  message: Message,\n  assertiveness: Assertiveness = 'assertive',\n  timeout: number = LIVEREGION_TIMEOUT_DELAY\n): void {\n  if (!liveAnnouncer) {\n    liveAnnouncer = new LiveAnnouncer();\n    // wait for the live announcer regions to be added to the dom, then announce\n    // otherwise <PERSON><PERSON> won't announce the message if it's added too quickly\n    // found most times less than 100ms were not consistent when announcing with Safari\n\n    // IS_REACT_ACT_ENVIRONMENT is used by React 18. Previous versions checked for the `jest` global.\n    // https://github.com/reactwg/react-18/discussions/102\n    // if we're in a test environment, announce without waiting\n    // @ts-ignore\n    if (!(typeof IS_REACT_ACT_ENVIRONMENT === 'boolean' ? IS_REACT_ACT_ENVIRONMENT : typeof jest !== 'undefined')) {\n      setTimeout(() => {\n        if (liveAnnouncer?.isAttached()) {\n          liveAnnouncer?.announce(message, assertiveness, timeout);\n        }\n      }, 100);\n    } else {\n      liveAnnouncer.announce(message, assertiveness, timeout);\n    }\n  } else {\n    liveAnnouncer.announce(message, assertiveness, timeout);\n  }\n}\n\n/**\n * Stops all queued announcements.\n */\nexport function clearAnnouncer(assertiveness: Assertiveness): void {\n  if (liveAnnouncer) {\n    liveAnnouncer.clear(assertiveness);\n  }\n}\n\n/**\n * Removes the announcer from the DOM.\n */\nexport function destroyAnnouncer(): void {\n  if (liveAnnouncer) {\n    liveAnnouncer.destroy();\n    liveAnnouncer = null;\n  }\n}\n\n// LiveAnnouncer is implemented using vanilla DOM, not React. That's because as of React 18\n// ReactDOM.render is deprecated, and the replacement, ReactDOM.createRoot is moved into a\n// subpath import `react-dom/client`. That makes it hard for us to support multiple React versions.\n// As a global API, we can't use portals without introducing a breaking API change. LiveAnnouncer\n// is simple enough to implement without React, so that's what we do here.\n// See this discussion for more details: https://github.com/reactwg/react-18/discussions/125#discussioncomment-2382638\nclass LiveAnnouncer {\n  node: HTMLElement | null = null;\n  assertiveLog: HTMLElement | null = null;\n  politeLog: HTMLElement | null = null;\n\n  constructor() {\n    if (typeof document !== 'undefined') {\n      this.node = document.createElement('div');\n      this.node.dataset.liveAnnouncer = 'true';\n      // copied from VisuallyHidden\n      Object.assign(this.node.style, {\n        border: 0,\n        clip: 'rect(0 0 0 0)',\n        clipPath: 'inset(50%)',\n        height: '1px',\n        margin: '-1px',\n        overflow: 'hidden',\n        padding: 0,\n        position: 'absolute',\n        width: '1px',\n        whiteSpace: 'nowrap'\n      });\n\n      this.assertiveLog = this.createLog('assertive');\n      this.node.appendChild(this.assertiveLog);\n\n      this.politeLog = this.createLog('polite');\n      this.node.appendChild(this.politeLog);\n\n      document.body.prepend(this.node);\n    }\n  }\n\n  isAttached() {\n    return this.node?.isConnected;\n  }\n\n  createLog(ariaLive: string) {\n    let node = document.createElement('div');\n    node.setAttribute('role', 'log');\n    node.setAttribute('aria-live', ariaLive);\n    node.setAttribute('aria-relevant', 'additions');\n    return node;\n  }\n\n  destroy() {\n    if (!this.node) {\n      return;\n    }\n\n    document.body.removeChild(this.node);\n    this.node = null;\n  }\n\n  announce(message: Message, assertiveness = 'assertive', timeout = LIVEREGION_TIMEOUT_DELAY) {\n    if (!this.node) {\n      return;\n    }\n\n    let node = document.createElement('div');\n    if (typeof message === 'object') {\n      // To read an aria-labelledby, the element must have an appropriate role, such as img.\n      node.setAttribute('role', 'img');\n      node.setAttribute('aria-labelledby', message['aria-labelledby']);\n    } else {\n      node.textContent = message;\n    }\n\n    if (assertiveness === 'assertive') {\n      this.assertiveLog?.appendChild(node);\n    } else {\n      this.politeLog?.appendChild(node);\n    }\n\n    if (message !== '') {\n      setTimeout(() => {\n        node.remove();\n      }, timeout);\n    }\n  }\n\n  clear(assertiveness: Assertiveness) {\n    if (!this.node) {\n      return;\n    }\n\n    if ((!assertiveness || assertiveness === 'assertive') && this.assertiveLog) {\n      this.assertiveLog.innerHTML = '';\n    }\n\n    if ((!assertiveness || assertiveness === 'polite') && this.politeLog) {\n      this.politeLog.innerHTML = '';\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAID,8DAA8D;;;;;AAC9D,MAAM,iDAA2B;AAEjC,IAAI,sCAAsC;AAOnC,SAAS,0CACd,OAAgB,EAChB,gBAA+B,WAAW,EAC1C,UAAkB,8CAAwB;IAE1C,IAAI,CAAC,qCAAe;QAClB,sCAAgB,IAAI;QACpB,4EAA4E;QAC5E,wEAAwE;QACxE,mFAAmF;QAEnF,iGAAiG;QACjG,sDAAsD;QACtD,2DAA2D;QAC3D,aAAa;QACb,IAAI,CAAE,CAAA,OAAO,6BAA6B,YAAY,2BAA2B,OAAO,SAAS,WAAU,GACzG,WAAW;YACT,IAAI,wCAAA,QAAA,wCAAA,KAAA,IAAA,KAAA,IAAA,oCAAe,UAAU,IAC3B,wCAAA,QAAA,wCAAA,KAAA,IAAA,KAAA,IAAA,oCAAe,QAAQ,CAAC,SAAS,eAAe;QAEpD,GAAG;aAEH,oCAAc,QAAQ,CAAC,SAAS,eAAe;IAEnD,OACE,oCAAc,QAAQ,CAAC,SAAS,eAAe;AAEnD;AAKO,SAAS,0CAAe,aAA4B;IACzD,IAAI,qCACF,oCAAc,KAAK,CAAC;AAExB;AAKO,SAAS;IACd,IAAI,qCAAe;QACjB,oCAAc,OAAO;QACrB,sCAAgB;IAClB;AACF;AAEA,2FAA2F;AAC3F,0FAA0F;AAC1F,mGAAmG;AACnG,iGAAiG;AACjG,0EAA0E;AAC1E,sHAAsH;AACtH,MAAM;IAiCJ,aAAa;YACJ;QAAP,OAAA,CAAO,aAAA,IAAI,CAAC,IAAI,MAAA,QAAT,eAAA,KAAA,IAAA,KAAA,IAAA,WAAW,WAAW;IAC/B;IAEA,UAAU,QAAgB,EAAE;QAC1B,IAAI,OAAO,SAAS,aAAa,CAAC;QAClC,KAAK,YAAY,CAAC,QAAQ;QAC1B,KAAK,YAAY,CAAC,aAAa;QAC/B,KAAK,YAAY,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,IAAI,EACZ;QAGF,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QACnC,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,SAAS,OAAgB,EAAE,gBAAgB,WAAW,EAAE,UAAU,8CAAwB,EAAE;YAexF,oBAEA;QAhBF,IAAI,CAAC,IAAI,CAAC,IAAI,EACZ;QAGF,IAAI,OAAO,SAAS,aAAa,CAAC;QAClC,IAAI,OAAO,YAAY,UAAU;YAC/B,sFAAsF;YACtF,KAAK,YAAY,CAAC,QAAQ;YAC1B,KAAK,YAAY,CAAC,mBAAmB,OAAO,CAAC,kBAAkB;QACjE,OACE,KAAK,WAAW,GAAG;QAGrB,IAAI,kBAAkB,aAAA,CACpB,qBAAA,IAAI,CAAC,YAAY,MAAA,QAAjB,uBAAA,KAAA,IAAA,KAAA,IAAA,mBAAmB,WAAW,CAAC;cAE/B,kBAAA,IAAI,CAAC,SAAS,MAAA,QAAd,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAgB,WAAW,CAAC;QAG9B,IAAI,YAAY,IACd,WAAW;YACT,KAAK,MAAM;QACb,GAAG;IAEP;IAEA,MAAM,aAA4B,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,IAAI,EACZ;QAGF,IAAK,CAAA,CAAC,iBAAiB,kBAAkB,WAAU,KAAM,IAAI,CAAC,YAAY,EACxE,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG;QAGhC,IAAK,CAAA,CAAC,iBAAiB,kBAAkB,QAAO,KAAM,IAAI,CAAC,SAAS,EAClE,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;IAE/B;IAxFA,aAAc;aAJd,IAAA,GAA2B;aAC3B,YAAA,GAAmC;aACnC,SAAA,GAAgC;QAG9B,IAAI,OAAO,aAAa,aAAa;YACnC,IAAI,CAAC,IAAI,GAAG,SAAS,aAAa,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YAClC,6BAA6B;YAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBAC7B,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,YAAY;YACd;YAEA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY;YAEvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YAEpC,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QACjC;IACF;AA+DF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4348, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, SyntheticEvent, useCallback, useRef} from 'react';\n\n// Turn a native event into a React synthetic event.\nexport function createSyntheticEvent<E extends SyntheticEvent>(nativeEvent: Event): E {\n  let event = nativeEvent as any as E;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => (event as any).cancelBubble;\n  event.persist = () => {};\n  return event;\n}\n\nexport function setEventTarget(event: Event, target: Element): void {\n  Object.defineProperty(event, 'target', {value: target});\n  Object.defineProperty(event, 'currentTarget', {value: target});\n}\n\nexport function useSyntheticBlurEvent<Target extends Element = Element>(onBlur: (e: ReactFocusEvent<Target>) => void): (e: ReactFocusEvent<Target>) => void {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n\n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: ReactFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = createSyntheticEvent<ReactFocusEvent<Target>>(e);\n          dispatchBlur(event);\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null): (() => void) | undefined {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n\n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAOM,SAAS,yCAA+C,WAAkB;IAC/E,IAAI,QAAQ;IACZ,MAAM,WAAW,GAAG;IACpB,MAAM,kBAAkB,GAAG,IAAM,MAAM,gBAAgB;IACvD,2FAA2F;IAC3F,MAAM,oBAAoB,GAAG,IAAO,MAAc,YAAY;IAC9D,MAAM,OAAO,GAAG,KAAO;IACvB,OAAO;AACT;AAEO,SAAS,0CAAe,KAAY,EAAE,MAAe;IAC1D,OAAO,cAAc,CAAC,OAAO,UAAU;QAAC,OAAO;IAAM;IACrD,OAAO,cAAc,CAAC,OAAO,iBAAiB;QAAC,OAAO;IAAM;AAC9D;AAEO,SAAS,0CAAwD,MAA4C;IAClH,IAAI,WAAW,CAAA,iKAAA,SAAK,EAAE;QACpB,WAAW;QACX,UAAU;IACZ;IAEA,mDAAmD;IAEnD,CAAA,2KAAA,kBAAc,EAAE;QACd,MAAM,QAAQ,SAAS,OAAO;QAC9B,OAAO;YACL,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,QAAQ,CAAC,UAAU;gBACzB,MAAM,QAAQ,GAAG;YACnB;QACF;IACF,GAAG,EAAE;IAEL,IAAI,eAAe,CAAA,0KAAA,iBAAa,EAAE,CAAC;QACjC,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAA,OAAS;IACX;IAEA,wDAAwD;IACxD,OAAO,CAAA,iKAAA,cAAU,EAAE,CAAC;QAClB,wGAAwG;QACxG,sGAAsG;QACtG,6FAA6F;QAC7F,qGAAqG;QACrG,IACE,EAAE,MAAM,YAAY,qBACpB,EAAE,MAAM,YAAY,oBACpB,EAAE,MAAM,YAAY,uBACpB,EAAE,MAAM,YAAY,mBACpB;YACA,SAAS,OAAO,CAAC,SAAS,GAAG;YAE7B,IAAI,SAAS,EAAE,MAAM;YACrB,IAAI,gBAA2D,CAAC;gBAC9D,SAAS,OAAO,CAAC,SAAS,GAAG;gBAE7B,IAAI,OAAO,QAAQ,EAAE;oBACnB,uEAAuE;oBACvE,IAAI,QAAQ,yCAA8C;oBAC1D,aAAa;gBACf;gBAEA,qEAAqE;gBACrE,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;oBAC7B,SAAS,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACpC,SAAS,OAAO,CAAC,QAAQ,GAAG;gBAC9B;YACF;YAEA,OAAO,gBAAgB,CAAC,YAAY,eAAe;gBAAC,MAAM;YAAI;YAE9D,SAAS,OAAO,CAAC,QAAQ,GAAG,IAAI,iBAAiB;gBAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,IAAI,OAAO,QAAQ,EAAE;wBACjD;qBAAA,6BAAA,SAAS,OAAO,CAAC,QAAQ,MAAA,QAAzB,+BAAA,KAAA,IAAA,KAAA,IAAA,2BAA2B,UAAU;oBACrC,IAAI,kBAAkB,WAAW,SAAS,aAAa,GAAG,OAAO,SAAS,aAAa;oBACvF,OAAO,aAAa,CAAC,IAAI,WAAW,QAAQ;wBAAC,eAAe;oBAAe;oBAC3E,OAAO,aAAa,CAAC,IAAI,WAAW,YAAY;wBAAC,SAAS;wBAAM,eAAe;oBAAe;gBAChG;YACF;YAEA,SAAS,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ;gBAAC,YAAY;gBAAM,iBAAiB;oBAAC;iBAAW;YAAA;QAC5F;IACF,GAAG;QAAC;KAAa;AACnB;AAEO,IAAI,4CAAmB;AAOvB,SAAS,0CAAa,MAA+B;IAC1D,uEAAuE;IACvE,MAAO,UAAU,CAAC,CAAA,uKAAA,cAAU,EAAE,QAC5B,SAAS,OAAO,aAAa;IAG/B,IAAI,SAAS,CAAA,sKAAA,iBAAa,EAAE;IAC5B,IAAI,gBAAgB,OAAO,QAAQ,CAAC,aAAa;IACjD,IAAI,CAAC,iBAAiB,kBAAkB,QACtC;IAGF,4CAAmB;IACnB,IAAI,eAAe;IACnB,IAAI,SAAS,CAAC;QACZ,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAChC,EAAE,wBAAwB;IAE9B;IAEA,IAAI,aAAa,CAAC;QAChB,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAAc;YAC9C,EAAE,wBAAwB;YAE1B,qEAAqE;YACrE,6CAA6C;YAC7C,IAAI,CAAC,UAAU,CAAC,cAAc;gBAC5B,eAAe;gBACf,CAAA,iLAAA,wBAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,EAAE,MAAM,KAAK,UAAU,cACzB,EAAE,wBAAwB;IAE9B;IAEA,IAAI,YAAY,CAAC;QACf,IAAI,EAAE,MAAM,KAAK,UAAU,cAAc;YACvC,EAAE,wBAAwB;YAE1B,IAAI,CAAC,cAAc;gBACjB,eAAe;gBACf,CAAA,iLAAA,wBAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,OAAO,gBAAgB,CAAC,QAAQ,QAAQ;IACxC,OAAO,gBAAgB,CAAC,YAAY,YAAY;IAChD,OAAO,gBAAgB,CAAC,WAAW,WAAW;IAC9C,OAAO,gBAAgB,CAAC,SAAS,SAAS;IAE1C,IAAI,UAAU;QACZ,qBAAqB;QACrB,OAAO,mBAAmB,CAAC,QAAQ,QAAQ;QAC3C,OAAO,mBAAmB,CAAC,YAAY,YAAY;QACnD,OAAO,mBAAmB,CAAC,WAAW,WAAW;QACjD,OAAO,mBAAmB,CAAC,SAAS,SAAS;QAC7C,4CAAmB;QACnB,eAAe;IACjB;IAEA,IAAI,MAAM,sBAAsB;IAChC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4519, "column": 0}, "map": {"version": 3, "file": "textSelection.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/textSelection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerDocument, isIOS, runAfterTransition} from '@react-aria/utils';\n\n// Safari on iOS starts selecting text on long press. The only way to avoid this, it seems,\n// is to add user-select: none to the entire page. Adding it to the pressable element prevents\n// that element from being selected, but nearby elements may still receive selection. We add\n// user-select: none on touch start, and remove it again on touch end to prevent this.\n// This must be implemented using global state to avoid race conditions between multiple elements.\n\n// There are three possible states due to the delay before removing user-select: none after\n// pointer up. The 'default' state always transitions to the 'disabled' state, which transitions\n// to 'restoring'. The 'restoring' state can either transition back to 'disabled' or 'default'.\n\n// For non-iOS devices, we apply user-select: none to the pressed element instead to avoid possible\n// performance issues that arise from applying and removing user-select: none to the entire page\n// (see https://github.com/adobe/react-spectrum/issues/1609).\ntype State = 'default' | 'disabled' | 'restoring';\n\n// Note that state only matters here for iOS. Non-iOS gets user-select: none applied to the target element\n// rather than at the document level so we just need to apply/remove user-select: none for each pressed element individually\nlet state: State = 'default';\nlet savedUserSelect = '';\nlet modifiedElementMap = new WeakMap<Element, string>();\n\nexport function disableTextSelection(target?: Element): void {\n  if (isIOS()) {\n    if (state === 'default') {\n\n      const documentObject = getOwnerDocument(target);\n      savedUserSelect = documentObject.documentElement.style.webkitUserSelect;\n      documentObject.documentElement.style.webkitUserSelect = 'none';\n    }\n\n    state = 'disabled';\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, store the target's original user-select and change to user-select: none\n    // Ignore state since it doesn't apply for non iOS\n    let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n    modifiedElementMap.set(target, target.style[property]);\n    target.style[property] = 'none';\n  }\n}\n\nexport function restoreTextSelection(target?: Element): void {\n  if (isIOS()) {\n    // If the state is already default, there's nothing to do.\n    // If it is restoring, then there's no need to queue a second restore.\n    if (state !== 'disabled') {\n      return;\n    }\n\n    state = 'restoring';\n\n    // There appears to be a delay on iOS where selection still might occur\n    // after pointer up, so wait a bit before removing user-select.\n    setTimeout(() => {\n      // Wait for any CSS transitions to complete so we don't recompute style\n      // for the whole page in the middle of the animation and cause jank.\n      runAfterTransition(() => {\n        // Avoid race conditions\n        if (state === 'restoring') {\n\n          const documentObject = getOwnerDocument(target);\n          if (documentObject.documentElement.style.webkitUserSelect === 'none') {\n            documentObject.documentElement.style.webkitUserSelect = savedUserSelect || '';\n          }\n\n          savedUserSelect = '';\n          state = 'default';\n        }\n      });\n    }, 300);\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, restore the target's original user-select if any\n    // Ignore state since it doesn't apply for non iOS\n    if (target && modifiedElementMap.has(target)) {\n      let targetOldUserSelect = modifiedElementMap.get(target) as string;\n      let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n\n      if (target.style[property] === 'none') {\n        target.style[property] = targetOldUserSelect;\n      }\n\n      if (target.getAttribute('style') === '') {\n        target.removeAttribute('style');\n      }\n      modifiedElementMap.delete(target);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC,GAmBD,0GAA0G;AAC1G,4HAA4H;AAC5H,IAAI,8BAAe;AACnB,IAAI,wCAAkB;AACtB,IAAI,2CAAqB,IAAI;AAEtB,SAAS,0CAAqB,MAAgB;IACnD,IAAI,CAAA,oKAAA,QAAI,KAAK;QACX,IAAI,gCAAU,WAAW;YAEvB,MAAM,iBAAiB,CAAA,sKAAA,mBAAe,EAAE;YACxC,wCAAkB,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB;YACvE,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB,GAAG;QAC1D;QAEA,8BAAQ;IACV,OAAO,IAAI,kBAAkB,eAAe,kBAAkB,YAAY;QACxE,sFAAsF;QACtF,kDAAkD;QAClD,IAAI,WAAW,gBAAgB,OAAO,KAAK,GAAG,eAAe;QAC7D,yCAAmB,GAAG,CAAC,QAAQ,OAAO,KAAK,CAAC,SAAS;QACrD,OAAO,KAAK,CAAC,SAAS,GAAG;IAC3B;AACF;AAEO,SAAS,0CAAqB,MAAgB;IACnD,IAAI,CAAA,oKAAA,QAAI,KAAK;QACX,0DAA0D;QAC1D,sEAAsE;QACtE,IAAI,gCAAU,YACZ;QAGF,8BAAQ;QAER,uEAAuE;QACvE,+DAA+D;QAC/D,WAAW;YACT,uEAAuE;YACvE,oEAAoE;YACpE,CAAA,8KAAA,qBAAiB,EAAE;gBACjB,wBAAwB;gBACxB,IAAI,gCAAU,aAAa;oBAEzB,MAAM,iBAAiB,CAAA,sKAAA,mBAAe,EAAE;oBACxC,IAAI,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB,KAAK,QAC5D,eAAe,eAAe,CAAC,KAAK,CAAC,gBAAgB,GAAG,yCAAmB;oBAG7E,wCAAkB;oBAClB,8BAAQ;gBACV;YACF;QACF,GAAG;IACL,OAAO,IAAI,kBAAkB,eAAe,kBAAkB,YAC5D,AACA,kDAAkD,aADa;IAE/D;QAAA,IAAI,UAAU,yCAAmB,GAAG,CAAC,SAAS;YAC5C,IAAI,sBAAsB,yCAAmB,GAAG,CAAC;YACjD,IAAI,WAAW,gBAAgB,OAAO,KAAK,GAAG,eAAe;YAE7D,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,QAC7B,OAAO,KAAK,CAAC,SAAS,GAAG;YAG3B,IAAI,OAAO,YAAY,CAAC,aAAa,IACnC,OAAO,eAAe,CAAC;YAEzB,yCAAmB,MAAM,CAAC;QAC5B;IAAA;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4598, "column": 0}, "map": {"version": 3, "file": "context.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/context.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {PressProps} from './usePress';\nimport React, {MutableRefObject} from 'react';\n\ninterface IPressResponderContext extends PressProps {\n  register(): void,\n  ref?: MutableRefObject<FocusableElement>\n}\n\nexport const PressResponderContext: React.Context<IPressResponderContext> = React.createContext<IPressResponderContext>({register: () => {}});\nPressResponderContext.displayName = 'PressResponderContext';\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAWM,MAAM,4CAA+D,CAAA,iKAAA,UAAI,EAAE,aAAa,CAAyB;IAAC,UAAU,KAAO;AAAC;AAC3I,0CAAsB,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4625, "column": 0}, "map": {"version": 3, "file": "usePress.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/usePress.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {\n  chain,\n  focusWithoutScrolling,\n  getEventTarget,\n  getOwnerDocument,\n  getOwnerWindow,\n  isMac,\n  isVirtualClick,\n  isVirtualPointerEvent,\n  mergeProps,\n  nodeContains,\n  openLink,\n  useEffectEvent,\n  useGlobalListeners,\n  useSyncRef\n} from '@react-aria/utils';\nimport {createSyntheticEvent, preventFocus, setEventTarget} from './utils';\nimport {disableTextSelection, restoreTextSelection} from './textSelection';\nimport {DOMAttributes, FocusableElement, PressEvent as IPressEvent, PointerType, PressEvents, RefObject} from '@react-types/shared';\nimport {flushSync} from 'react-dom';\nimport {PressResponderContext} from './context';\nimport {MouseEvent as RMouseEvent, TouchEvent as RTouchEvent, useContext, useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface PressProps extends PressEvents {\n  /** Whether the target is in a controlled press state (e.g. an overlay it triggers is open). */\n  isPressed?: boolean,\n  /** Whether the press events should be disabled. */\n  isDisabled?: boolean,\n  /** Whether the target should not receive focus on press. */\n  preventFocusOnPress?: boolean,\n  /**\n   * Whether press events should be canceled when the pointer leaves the target while pressed.\n   * By default, this is `false`, which means if the pointer returns back over the target while\n   * still pressed, onPressStart will be fired again. If set to `true`, the press is canceled\n   * when the pointer leaves the target and onPressStart will not be fired if the pointer returns.\n   */\n  shouldCancelOnPointerExit?: boolean,\n  /** Whether text selection should be enabled on the pressable element. */\n  allowTextSelectionOnPress?: boolean\n}\n\nexport interface PressHookProps extends PressProps {\n  /** A ref to the target element. */\n  ref?: RefObject<Element | null>\n}\n\ninterface PressState {\n  isPressed: boolean,\n  ignoreEmulatedMouseEvents: boolean,\n  didFirePressStart: boolean,\n  isTriggeringEvent: boolean,\n  activePointerId: any,\n  target: FocusableElement | null,\n  isOverTarget: boolean,\n  pointerType: PointerType | null,\n  userSelect?: string,\n  metaKeyEvents?: Map<string, KeyboardEvent>,\n  disposables: Array<() => void>\n}\n\ninterface EventBase {\n  currentTarget: EventTarget | null,\n  shiftKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean,\n  altKey: boolean,\n  clientX?: number,\n  clientY?: number,\n  targetTouches?: Array<{clientX?: number, clientY?: number}>\n}\n\nexport interface PressResult {\n  /** Whether the target is currently pressed. */\n  isPressed: boolean,\n  /** Props to spread on the target element. */\n  pressProps: DOMAttributes\n}\n\nfunction usePressResponderContext(props: PressHookProps): PressHookProps {\n  // Consume context from <PressResponder> and merge with props.\n  let context = useContext(PressResponderContext);\n  if (context) {\n    let {register, ...contextProps} = context;\n    props = mergeProps(contextProps, props) as PressHookProps;\n    register();\n  }\n  useSyncRef(context, props.ref);\n\n  return props;\n}\n\nclass PressEvent implements IPressEvent {\n  type: IPressEvent['type'];\n  pointerType: PointerType;\n  target: Element;\n  shiftKey: boolean;\n  ctrlKey: boolean;\n  metaKey: boolean;\n  altKey: boolean;\n  x: number;\n  y: number;\n  #shouldStopPropagation = true;\n\n  constructor(type: IPressEvent['type'], pointerType: PointerType, originalEvent: EventBase, state?: PressState) {\n    let currentTarget = state?.target ?? originalEvent.currentTarget;\n    const rect: DOMRect | undefined = (currentTarget as Element)?.getBoundingClientRect();\n    let x, y = 0;\n    let clientX, clientY: number | null = null;\n    if (originalEvent.clientX != null && originalEvent.clientY != null) {\n      clientX = originalEvent.clientX;\n      clientY = originalEvent.clientY;\n    }\n    if (rect) {\n      if (clientX != null && clientY != null) {\n        x = clientX - rect.left;\n        y = clientY - rect.top;\n      } else {\n        x = rect.width / 2;\n        y = rect.height / 2;\n      }\n    }\n    this.type = type;\n    this.pointerType = pointerType;\n    this.target = originalEvent.currentTarget as Element;\n    this.shiftKey = originalEvent.shiftKey;\n    this.metaKey = originalEvent.metaKey;\n    this.ctrlKey = originalEvent.ctrlKey;\n    this.altKey = originalEvent.altKey;\n    this.x = x;\n    this.y = y;\n  }\n\n  continuePropagation() {\n    this.#shouldStopPropagation = false;\n  }\n\n  get shouldStopPropagation() {\n    return this.#shouldStopPropagation;\n  }\n}\n\nconst LINK_CLICKED = Symbol('linkClicked');\nconst STYLE_ID = 'react-aria-pressable-style';\nconst PRESSABLE_ATTRIBUTE = 'data-react-aria-pressable';\n\n/**\n * Handles press interactions across mouse, touch, keyboard, and screen readers.\n * It normalizes behavior across browsers and platforms, and handles many nuances\n * of dealing with pointer and keyboard events.\n */\nexport function usePress(props: PressHookProps): PressResult {\n  let {\n    onPress,\n    onPressChange,\n    onPressStart,\n    onPressEnd,\n    onPressUp,\n    onClick,\n    isDisabled,\n    isPressed: isPressedProp,\n    preventFocusOnPress,\n    shouldCancelOnPointerExit,\n    allowTextSelectionOnPress,\n    ref: domRef,\n    ...domProps\n  } = usePressResponderContext(props);\n\n  let [isPressed, setPressed] = useState(false);\n  let ref = useRef<PressState>({\n    isPressed: false,\n    ignoreEmulatedMouseEvents: false,\n    didFirePressStart: false,\n    isTriggeringEvent: false,\n    activePointerId: null,\n    target: null,\n    isOverTarget: false,\n    pointerType: null,\n    disposables: []\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let triggerPressStart = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled || state.didFirePressStart) {\n      return false;\n    }\n\n    let shouldStopPropagation = true;\n    state.isTriggeringEvent = true;\n    if (onPressStart) {\n      let event = new PressEvent('pressstart', pointerType, originalEvent);\n      onPressStart(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(true);\n    }\n\n    state.isTriggeringEvent = false;\n    state.didFirePressStart = true;\n    setPressed(true);\n    return shouldStopPropagation;\n  });\n\n  let triggerPressEnd = useEffectEvent((originalEvent: EventBase, pointerType: PointerType, wasPressed = true) => {\n    let state = ref.current;\n    if (!state.didFirePressStart) {\n      return false;\n    }\n\n    state.didFirePressStart = false;\n    state.isTriggeringEvent = true;\n\n    let shouldStopPropagation = true;\n    if (onPressEnd) {\n      let event = new PressEvent('pressend', pointerType, originalEvent);\n      onPressEnd(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(false);\n    }\n\n    setPressed(false);\n\n    if (onPress && wasPressed && !isDisabled) {\n      let event = new PressEvent('press', pointerType, originalEvent);\n      onPress(event);\n      shouldStopPropagation &&= event.shouldStopPropagation;\n    }\n\n    state.isTriggeringEvent = false;\n    return shouldStopPropagation;\n  });\n\n  let triggerPressUp = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled) {\n      return false;\n    }\n\n    if (onPressUp) {\n      state.isTriggeringEvent = true;\n      let event = new PressEvent('pressup', pointerType, originalEvent);\n      onPressUp(event);\n      state.isTriggeringEvent = false;\n      return event.shouldStopPropagation;\n    }\n\n    return true;\n  });\n\n  let cancel = useEffectEvent((e: EventBase) => {\n    let state = ref.current;\n    if (state.isPressed && state.target) {\n      if (state.didFirePressStart && state.pointerType != null) {\n        triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n      }\n      state.isPressed = false;\n      state.isOverTarget = false;\n      state.activePointerId = null;\n      state.pointerType = null;\n      removeAllGlobalListeners();\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    }\n  });\n\n  let cancelOnPointerExit = useEffectEvent((e: EventBase) => {\n    if (shouldCancelOnPointerExit) {\n      cancel(e);\n    }\n  });\n\n  let triggerClick = useEffectEvent((e: RMouseEvent<FocusableElement>) => {\n    onClick?.(e);\n  });\n\n  let triggerSyntheticClick = useEffectEvent((e: KeyboardEvent | TouchEvent, target: FocusableElement) => {\n    // Some third-party libraries pass in onClick instead of onPress.\n    // Create a fake mouse event and trigger onClick as well.\n    // This matches the browser's native activation behavior for certain elements (e.g. button).\n    // https://html.spec.whatwg.org/#activation\n    // https://html.spec.whatwg.org/#fire-a-synthetic-pointer-event\n    if (onClick) {\n      let event = new MouseEvent('click', e);\n      setEventTarget(event, target);\n      onClick(createSyntheticEvent(event));\n    }\n  });\n\n  let pressProps = useMemo(() => {\n    let state = ref.current;\n    let pressProps: DOMAttributes = {\n      onKeyDown(e) {\n        if (isValidKeyboardEvent(e.nativeEvent, e.currentTarget) && nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          if (shouldPreventDefaultKeyboard(getEventTarget(e.nativeEvent), e.key)) {\n            e.preventDefault();\n          }\n\n          // If the event is repeating, it may have started on a different element\n          // after which focus moved to the current element. Ignore these events and\n          // only handle the first key down event.\n          let shouldStopPropagation = true;\n          if (!state.isPressed && !e.repeat) {\n            state.target = e.currentTarget;\n            state.isPressed = true;\n            state.pointerType = 'keyboard';\n            shouldStopPropagation = triggerPressStart(e, 'keyboard');\n\n            // Focus may move before the key up event, so register the event on the document\n            // instead of the same element where the key down event occurred. Make it capturing so that it will trigger\n            // before stopPropagation from useKeyboard on a child element may happen and thus we can still call triggerPress for the parent element.\n            let originalTarget = e.currentTarget;\n            let pressUp = (e) => {\n              if (isValidKeyboardEvent(e, originalTarget) && !e.repeat && nodeContains(originalTarget, getEventTarget(e)) && state.target) {\n                triggerPressUp(createEvent(state.target, e), 'keyboard');\n              }\n            };\n\n            addGlobalListener(getOwnerDocument(e.currentTarget), 'keyup', chain(pressUp, onKeyUp), true);\n          }\n\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n\n          // Keep track of the keydown events that occur while the Meta (e.g. Command) key is held.\n          // macOS has a bug where keyup events are not fired while the Meta key is down.\n          // When the Meta key itself is released we will get an event for that, and we'll act as if\n          // all of these other keys were released as well.\n          // https://bugs.chromium.org/p/chromium/issues/detail?id=1393524\n          // https://bugs.webkit.org/show_bug.cgi?id=55291\n          // https://bugzilla.mozilla.org/show_bug.cgi?id=1299553\n          if (e.metaKey && isMac()) {\n            state.metaKeyEvents?.set(e.key, e.nativeEvent);\n          }\n        } else if (e.key === 'Meta') {\n          state.metaKeyEvents = new Map();\n        }\n      },\n      onClick(e) {\n        if (e && !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e && e.button === 0 && !state.isTriggeringEvent && !(openLink as any).isOpening) {\n          let shouldStopPropagation = true;\n          if (isDisabled) {\n            e.preventDefault();\n          }\n          \n          // If triggered from a screen reader or by using element.click(),\n          // trigger as if it were a keyboard click.\n          if (!state.ignoreEmulatedMouseEvents && !state.isPressed && (state.pointerType === 'virtual' || isVirtualClick(e.nativeEvent))) {\n            let stopPressStart = triggerPressStart(e, 'virtual');\n            let stopPressUp = triggerPressUp(e, 'virtual');\n            let stopPressEnd = triggerPressEnd(e, 'virtual');\n            triggerClick(e);\n            shouldStopPropagation = stopPressStart && stopPressUp && stopPressEnd;\n          } else if (state.isPressed && state.pointerType !== 'keyboard') {\n            let pointerType = state.pointerType || (e.nativeEvent as PointerEvent).pointerType as PointerType || 'virtual';\n            let stopPressUp = triggerPressUp(createEvent(e.currentTarget, e), pointerType);\n            let stopPressEnd =  triggerPressEnd(createEvent(e.currentTarget, e), pointerType, true);\n            shouldStopPropagation = stopPressUp && stopPressEnd;\n            state.isOverTarget = false;\n            triggerClick(e);\n            cancel(e);\n          }\n\n          state.ignoreEmulatedMouseEvents = false;\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n        }\n      }\n    };\n\n    let onKeyUp = (e: KeyboardEvent) => {\n      if (state.isPressed && state.target && isValidKeyboardEvent(e, state.target)) {\n        if (shouldPreventDefaultKeyboard(getEventTarget(e), e.key)) {\n          e.preventDefault();\n        }\n\n        let target = getEventTarget(e);\n        let wasPressed = nodeContains(state.target, getEventTarget(e));\n        triggerPressEnd(createEvent(state.target, e), 'keyboard', wasPressed);\n        if (wasPressed) {\n          triggerSyntheticClick(e, state.target);\n        }\n        removeAllGlobalListeners();\n\n        // If a link was triggered with a key other than Enter, open the URL ourselves.\n        // This means the link has a role override, and the default browser behavior\n        // only applies when using the Enter key.\n        if (e.key !== 'Enter' && isHTMLAnchorLink(state.target) && nodeContains(state.target, target) && !e[LINK_CLICKED]) {\n          // Store a hidden property on the event so we only trigger link click once,\n          // even if there are multiple usePress instances attached to the element.\n          e[LINK_CLICKED] = true;\n          openLink(state.target, e, false);\n        }\n\n        state.isPressed = false;\n        state.metaKeyEvents?.delete(e.key);\n      } else if (e.key === 'Meta' && state.metaKeyEvents?.size) {\n        // If we recorded keydown events that occurred while the Meta key was pressed,\n        // and those haven't received keyup events already, fire keyup events ourselves.\n        // See comment above for more info about the macOS bug causing this.\n        let events = state.metaKeyEvents;\n        state.metaKeyEvents = undefined;\n        for (let event of events.values()) {\n          state.target?.dispatchEvent(new KeyboardEvent('keyup', event));\n        }\n      }\n    };\n\n    if (typeof PointerEvent !== 'undefined') {\n      pressProps.onPointerDown = (e) => {\n        // Only handle left clicks, and ignore events that bubbled through portals.\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // iOS safari fires pointer events from VoiceOver with incorrect coordinates/target.\n        // Ignore and let the onClick handler take care of it instead.\n        // https://bugs.webkit.org/show_bug.cgi?id=222627\n        // https://bugs.webkit.org/show_bug.cgi?id=223202\n        if (isVirtualPointerEvent(e.nativeEvent)) {\n          state.pointerType = 'virtual';\n          return;\n        }\n\n        state.pointerType = e.pointerType;\n\n        let shouldStopPropagation = true;\n        if (!state.isPressed) {\n          state.isPressed = true;\n          state.isOverTarget = true;\n          state.activePointerId = e.pointerId;\n          state.target = e.currentTarget as FocusableElement;\n\n          if (!allowTextSelectionOnPress) {\n            disableTextSelection(state.target);\n          }\n\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n\n          // Release pointer capture so that touch interactions can leave the original target.\n          // This enables onPointerLeave and onPointerEnter to fire.\n          let target = getEventTarget(e.nativeEvent);\n          if ('releasePointerCapture' in target) {\n            target.releasePointerCapture(e.pointerId);\n          }\n\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointerup', onPointerUp, false);\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointercancel', onPointerCancel, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseDown = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e.button === 0) {\n          if (preventFocusOnPress) {\n            let dispose = preventFocus(e.target as FocusableElement);\n            if (dispose) {\n              state.disposables.push(dispose);\n            }\n          }\n\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onPointerUp = (e) => {\n        // iOS fires pointerup with zero width and height, so check the pointerType recorded during pointerdown.\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent)) || state.pointerType === 'virtual') {\n          return;\n        }\n\n        // Only handle left clicks. If isPressed is true, delay until onClick.\n        if (e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || e.pointerType);\n        }\n      };\n\n      pressProps.onPointerEnter = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && !state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = true;\n          triggerPressStart(createEvent(state.target, e), state.pointerType);\n        }\n      };\n\n      pressProps.onPointerLeave = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n      };\n\n      let onPointerUp = (e: PointerEvent) => {\n        if (e.pointerId === state.activePointerId && state.isPressed && e.button === 0 && state.target) {\n          if (nodeContains(state.target, getEventTarget(e)) && state.pointerType != null) {\n            // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n            // is mutated between onPointerUp and onClick, and is more compatible with third party libraries.\n            // https://github.com/adobe/react-spectrum/issues/1513\n            // https://issues.chromium.org/issues/40732224\n            // However, iOS and Android do not focus or fire onClick after a long press.\n            // We work around this by triggering a click ourselves after a timeout.\n            // This timeout is canceled during the click event in case the real one fires first.\n            // The timeout must be at least 32ms, because Safari on iOS delays the click event on\n            // non-form elements without certain ARIA roles (for hover emulation).\n            // https://github.com/WebKit/WebKit/blob/dccfae42bb29bd4bdef052e469f604a9387241c0/Source/WebKit/WebProcess/WebPage/ios/WebPageIOS.mm#L875-L892\n            let clicked = false;\n            let timeout = setTimeout(() => {\n              if (state.isPressed && state.target instanceof HTMLElement) {\n                if (clicked) {\n                  cancel(e);\n                } else {\n                  focusWithoutScrolling(state.target);\n                  state.target.click();\n                }\n              }\n            }, 80);\n            // Use a capturing listener to track if a click occurred.\n            // If stopPropagation is called it may never reach our handler.\n            addGlobalListener(e.currentTarget as Document, 'click', () => clicked = true, true);\n            state.disposables.push(() => clearTimeout(timeout));\n          } else {\n            cancel(e);\n          }\n\n          // Ignore subsequent onPointerLeave event before onClick on touch devices.\n          state.isOverTarget = false;\n        }\n      };\n\n      let onPointerCancel = (e: PointerEvent) => {\n        cancel(e);\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // Safari does not call onPointerCancel when a drag starts, whereas Chrome and Firefox do.\n        cancel(e);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      // NOTE: this fallback branch is entirely used by unit tests.\n      // All browsers now support pointer events, but JSDOM still does not.\n\n      pressProps.onMouseDown = (e) => {\n        // Only handle left clicks\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          e.stopPropagation();\n          return;\n        }\n\n        state.isPressed = true;\n        state.isOverTarget = true;\n        state.target = e.currentTarget;\n        state.pointerType = isVirtualClick(e.nativeEvent) ? 'virtual' : 'mouse';\n\n        // Flush sync so that focus moved during react re-renders occurs before we yield back to the browser.\n        let shouldStopPropagation = flushSync(() => triggerPressStart(e, state.pointerType!));\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        if (preventFocusOnPress) {\n          let dispose = preventFocus(e.target as FocusableElement);\n          if (dispose) {\n            state.disposables.push(dispose);\n          }\n        }\n\n        addGlobalListener(getOwnerDocument(e.currentTarget), 'mouseup', onMouseUp, false);\n      };\n\n      pressProps.onMouseEnter = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = true;\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseLeave = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(e, state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseUp = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.ignoreEmulatedMouseEvents && e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || 'mouse');\n        }\n      };\n\n      let onMouseUp = (e: MouseEvent) => {\n        // Only handle left clicks\n        if (e.button !== 0) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n          return;\n        }\n\n        if (state.target && state.target.contains(e.target as Element) && state.pointerType != null) {\n          // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n          // is mutated between onMouseUp and onClick, and is more compatible with third party libraries.\n        } else {\n          cancel(e);\n        }\n\n        state.isOverTarget = false;\n      };\n\n      pressProps.onTouchStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let touch = getTouchFromEvent(e.nativeEvent);\n        if (!touch) {\n          return;\n        }\n        state.activePointerId = touch.identifier;\n        state.ignoreEmulatedMouseEvents = true;\n        state.isOverTarget = true;\n        state.isPressed = true;\n        state.target = e.currentTarget;\n        state.pointerType = 'touch';\n\n        if (!allowTextSelectionOnPress) {\n          disableTextSelection(state.target);\n        }\n\n        let shouldStopPropagation = triggerPressStart(createTouchEvent(state.target, e), state.pointerType);\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        addGlobalListener(getOwnerWindow(e.currentTarget), 'scroll', onScroll, true);\n      };\n\n      pressProps.onTouchMove = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget)) {\n          if (!state.isOverTarget && state.pointerType != null) {\n            state.isOverTarget = true;\n            shouldStopPropagation = triggerPressStart(createTouchEvent(state.target!, e), state.pointerType);\n          }\n        } else if (state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n          cancelOnPointerExit(createTouchEvent(state.target!, e));\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onTouchEnd = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget) && state.pointerType != null) {\n          triggerPressUp(createTouchEvent(state.target!, e), state.pointerType);\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType);\n          triggerSyntheticClick(e.nativeEvent, state.target!);\n        } else if (state.isOverTarget && state.pointerType != null) {\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        state.isPressed = false;\n        state.activePointerId = null;\n        state.isOverTarget = false;\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.target && !allowTextSelectionOnPress) {\n          restoreTextSelection(state.target);\n        }\n        removeAllGlobalListeners();\n      };\n\n      pressProps.onTouchCancel = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        e.stopPropagation();\n        if (state.isPressed) {\n          cancel(createTouchEvent(state.target!, e));\n        }\n      };\n\n      let onScroll = (e: Event) => {\n        if (state.isPressed && nodeContains(getEventTarget(e), state.target)) {\n          cancel({\n            currentTarget: state.target,\n            shiftKey: false,\n            ctrlKey: false,\n            metaKey: false,\n            altKey: false\n          });\n        }\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        cancel(e);\n      };\n    }\n\n    return pressProps;\n  }, [\n    addGlobalListener,\n    isDisabled,\n    preventFocusOnPress,\n    removeAllGlobalListeners,\n    allowTextSelectionOnPress,\n    cancel,\n    cancelOnPointerExit,\n    triggerPressEnd,\n    triggerPressStart,\n    triggerPressUp,\n    triggerClick,\n    triggerSyntheticClick\n  ]);\n\n  // Avoid onClick delay for double tap to zoom by default.\n  useEffect(() => {\n    if (!domRef || process.env.NODE_ENV === 'test') {\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(domRef.current);\n    if (!ownerDocument || !ownerDocument.head || ownerDocument.getElementById(STYLE_ID)) {\n      return;\n    }\n\n    const style = ownerDocument.createElement('style');\n    style.id = STYLE_ID;\n    // touchAction: 'manipulation' is supposed to be equivalent, but in\n    // Safari it causes onPointerCancel not to fire on scroll.\n    // https://bugs.webkit.org/show_bug.cgi?id=240917\n    style.textContent = `\n@layer {\n  [${PRESSABLE_ATTRIBUTE}] {\n    touch-action: pan-x pan-y pinch-zoom;\n  }\n}\n    `.trim();\n    ownerDocument.head.prepend(style);\n  }, [domRef]);\n\n  // Remove user-select: none in case component unmounts immediately after pressStart\n  useEffect(() => {\n    let state = ref.current;\n    return () => {\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target ?? undefined);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    };\n  }, [allowTextSelectionOnPress]);\n\n  return {\n    isPressed: isPressedProp || isPressed,\n    pressProps: mergeProps(domProps, pressProps, {[PRESSABLE_ATTRIBUTE]: true})\n  };\n}\n\nfunction isHTMLAnchorLink(target: Element): target is HTMLAnchorElement {\n  return target.tagName === 'A' && target.hasAttribute('href');\n}\n\nfunction isValidKeyboardEvent(event: KeyboardEvent, currentTarget: Element): boolean {\n  const {key, code} = event;\n  const element = currentTarget as HTMLElement;\n  const role = element.getAttribute('role');\n  // Accessibility for keyboards. Space and Enter only.\n  // \"Spacebar\" is for IE 11\n  return (\n    (key === 'Enter' || key === ' ' || key === 'Spacebar' || code === 'Space') &&\n    !((element instanceof getOwnerWindow(element).HTMLInputElement && !isValidInputKey(element, key)) ||\n      element instanceof getOwnerWindow(element).HTMLTextAreaElement ||\n      element.isContentEditable) &&\n    // Links should only trigger with Enter key\n    !((role === 'link' || (!role && isHTMLAnchorLink(element))) && key !== 'Enter')\n  );\n}\n\nfunction getTouchFromEvent(event: TouchEvent): Touch | null {\n  const {targetTouches} = event;\n  if (targetTouches.length > 0) {\n    return targetTouches[0];\n  }\n  return null;\n}\n\nfunction getTouchById(\n  event: TouchEvent,\n  pointerId: null | number\n): null | Touch {\n  const changedTouches = event.changedTouches;\n  for (let i = 0; i < changedTouches.length; i++) {\n    const touch = changedTouches[i];\n    if (touch.identifier === pointerId) {\n      return touch;\n    }\n  }\n  return null;\n}\n\nfunction createTouchEvent(target: FocusableElement, e: RTouchEvent<FocusableElement>): EventBase {\n  let clientX = 0;\n  let clientY = 0;\n  if (e.targetTouches && e.targetTouches.length === 1) {\n    clientX = e.targetTouches[0].clientX;\n    clientY = e.targetTouches[0].clientY;\n  }\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\nfunction createEvent(target: FocusableElement, e: EventBase): EventBase {\n  let clientX = e.clientX;\n  let clientY = e.clientY;\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\ninterface Rect {\n  top: number,\n  right: number,\n  bottom: number,\n  left: number\n}\n\ninterface EventPoint {\n  clientX: number,\n  clientY: number,\n  width?: number,\n  height?: number,\n  radiusX?: number,\n  radiusY?: number\n}\n\nfunction getPointClientRect(point: EventPoint): Rect {\n  let offsetX = 0;\n  let offsetY = 0;\n  if (point.width !== undefined) {\n    offsetX = (point.width / 2);\n  } else if (point.radiusX !== undefined) {\n    offsetX = point.radiusX;\n  }\n  if (point.height !== undefined) {\n    offsetY = (point.height / 2);\n  } else if (point.radiusY !== undefined) {\n    offsetY = point.radiusY;\n  }\n\n  return {\n    top: point.clientY - offsetY,\n    right: point.clientX + offsetX,\n    bottom: point.clientY + offsetY,\n    left: point.clientX - offsetX\n  };\n}\n\nfunction areRectanglesOverlapping(a: Rect, b: Rect) {\n  // check if they cannot overlap on x axis\n  if (a.left > b.right || b.left > a.right) {\n    return false;\n  }\n  // check if they cannot overlap on y axis\n  if (a.top > b.bottom || b.top > a.bottom) {\n    return false;\n  }\n  return true;\n}\n\nfunction isOverTarget(point: EventPoint, target: Element) {\n  let rect = target.getBoundingClientRect();\n  let pointRect = getPointClientRect(point);\n  return areRectanglesOverlapping(rect, pointRect);\n}\n\nfunction shouldPreventDefaultUp(target: Element) {\n  if (target instanceof HTMLInputElement) {\n    return false;\n  }\n\n  if (target instanceof HTMLButtonElement) {\n    return target.type !== 'submit' && target.type !== 'reset';\n  }\n\n  if (isHTMLAnchorLink(target)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction shouldPreventDefaultKeyboard(target: Element, key: string) {\n  if (target instanceof HTMLInputElement) {\n    return !isValidInputKey(target, key);\n  }\n\n  return shouldPreventDefaultUp(target);\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\nfunction isValidInputKey(target: HTMLInputElement, key: string) {\n  // Only space should toggle checkboxes and radios, not enter.\n  return target.type === 'checkbox' || target.type === 'radio'\n    ? key === ' '\n    : nonTextInputTypes.has(target.type);\n}\n"], "names": [], "mappings": ";;;AAqkBe,QAAQ,GAAG,CAAC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArkBxC;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAgFlH,SAAS,+CAAyB,KAAqB;IACrD,8DAA8D;IAC9D,IAAI,UAAU,CAAA,iKAAA,aAAS,EAAE,CAAA,0KAAA,wBAAoB;IAC7C,IAAI,SAAS;QACX,IAAI,EAAA,UAAC,QAAQ,EAAE,GAAG,cAAa,GAAG;QAClC,QAAQ,CAAA,sKAAA,aAAS,EAAE,cAAc;QACjC;IACF;IACA,CAAA,sKAAA,aAAS,EAAE,SAAS,MAAM,GAAG;IAE7B,OAAO;AACT;IAYE,+CAAA,WAAA,GAAA,IAAA;AAVF,MAAM;IAyCJ,sBAAsB;uNACf,8CAAyB;IAChC;IAEA,IAAI,wBAAwB;QAC1B,OAAA,CAAA,GAAA,8LAAA,CAAA,IAAA,EAAO,IAAI,EAAC;IACd;IAnCA,YAAY,IAAyB,EAAE,WAAwB,EAAE,aAAwB,EAAE,KAAkB,CAAE;QAF/G,CAAA,GAAA,+LAAA,CAAA,IAAA,EAAA,IAAA,EAAA,8CAAA;;mBAAA,KAAA;;uNAAA,8CAAyB;YAGH;QAApB,IAAI,gBAAgB,CAAA,gBAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,MAAM,MAAA,QAAb,kBAAA,KAAA,IAAA,gBAAiB,cAAc,aAAa;QAChE,MAAM,OAA6B,kBAAA,QAAA,kBAAA,KAAA,IAAA,KAAA,IAAA,cAA2B,qBAAqB;QACnF,IAAI,GAAG,IAAI;QACX,IAAI,SAAS,UAAyB;QACtC,IAAI,cAAc,OAAO,IAAI,QAAQ,cAAc,OAAO,IAAI,MAAM;YAClE,UAAU,cAAc,OAAO;YAC/B,UAAU,cAAc,OAAO;QACjC;QACA,IAAI,MAAA;YACF,IAAI,WAAW,QAAQ,WAAW,MAAM;gBACtC,IAAI,UAAU,KAAK,IAAI;gBACvB,IAAI,UAAU,KAAK,GAAG;YACxB,OAAO;gBACL,IAAI,KAAK,KAAK,GAAG;gBACjB,IAAI,KAAK,MAAM,GAAG;YACpB;;QAEF,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG,cAAc,aAAa;QACzC,IAAI,CAAC,QAAQ,GAAG,cAAc,QAAQ;QACtC,IAAI,CAAC,OAAO,GAAG,cAAc,OAAO;QACpC,IAAI,CAAC,OAAO,GAAG,cAAc,OAAO;QACpC,IAAI,CAAC,MAAM,GAAG,cAAc,MAAM;QAClC,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;IACX;AASF;AAEA,MAAM,qCAAe,OAAO;AAC5B,MAAM,iCAAW;AACjB,MAAM,4CAAsB;AAOrB,SAAS,0CAAS,KAAqB;IAC5C,IAAI,EAAA,SACF,OAAO,EAAA,eACP,aAAa,EAAA,cACb,YAAY,EAAA,YACZ,UAAU,EAAA,WACV,SAAS,EAAA,SACT,OAAO,EAAA,YACP,UAAU,EACV,WAAW,aAAa,EAAA,qBACxB,mBAAmB,EAAA,2BACnB,yBAAyB,EAAA,2BACzB,yBAAyB,EACzB,KAAK,MAAM,EACX,GAAG,UACJ,GAAG,+CAAyB;IAE7B,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvC,IAAI,MAAM,CAAA,iKAAA,SAAK,EAAc;QAC3B,WAAW;QACX,2BAA2B;QAC3B,mBAAmB;QACnB,mBAAmB;QACnB,iBAAiB;QACjB,QAAQ;QACR,cAAc;QACd,aAAa;QACb,aAAa,EAAE;IACjB;IAEA,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,8KAAA,qBAAiB;IAErE,IAAI,oBAAoB,CAAA,0KAAA,iBAAa,EAAE,CAAC,eAA0B;QAChE,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,cAAc,MAAM,iBAAiB,EACvC,OAAO;QAGT,IAAI,wBAAwB;QAC5B,MAAM,iBAAiB,GAAG;QAC1B,IAAI,cAAc;YAChB,IAAI,QAAQ,IAAI,iCAAW,cAAc,aAAa;YACtD,aAAa;YACb,wBAAwB,MAAM,qBAAqB;QACrD;QAEA,IAAI,eACF,cAAc;QAGhB,MAAM,iBAAiB,GAAG;QAC1B,MAAM,iBAAiB,GAAG;QAC1B,WAAW;QACX,OAAO;IACT;IAEA,IAAI,kBAAkB,CAAA,0KAAA,iBAAa,EAAE,CAAC,eAA0B,aAA0B,aAAa,IAAI;QACzG,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,CAAC,MAAM,iBAAiB,EAC1B,OAAO;QAGT,MAAM,iBAAiB,GAAG;QAC1B,MAAM,iBAAiB,GAAG;QAE1B,IAAI,wBAAwB;QAC5B,IAAI,YAAY;YACd,IAAI,QAAQ,IAAI,iCAAW,YAAY,aAAa;YACpD,WAAW;YACX,wBAAwB,MAAM,qBAAqB;QACrD;QAEA,IAAI,eACF,cAAc;QAGhB,WAAW;QAEX,IAAI,WAAW,cAAc,CAAC,YAAY;YACxC,IAAI,QAAQ,IAAI,iCAAW,SAAS,aAAa;YACjD,QAAQ;YACR,yBAAA,CAAA,wBAA0B,MAAM,qBAAqB;QACvD;QAEA,MAAM,iBAAiB,GAAG;QAC1B,OAAO;IACT;IAEA,IAAI,iBAAiB,CAAA,0KAAA,iBAAa,EAAE,CAAC,eAA0B;QAC7D,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,YACF,OAAO;QAGT,IAAI,WAAW;YACb,MAAM,iBAAiB,GAAG;YAC1B,IAAI,QAAQ,IAAI,iCAAW,WAAW,aAAa;YACnD,UAAU;YACV,MAAM,iBAAiB,GAAG;YAC1B,OAAO,MAAM,qBAAqB;QACpC;QAEA,OAAO;IACT;IAEA,IAAI,SAAS,CAAA,0KAAA,iBAAa,EAAE,CAAC;QAC3B,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,EAAE;YACnC,IAAI,MAAM,iBAAiB,IAAI,MAAM,WAAW,IAAI,MAClD,gBAAgB,kCAAY,MAAM,MAAM,EAAE,IAAI,MAAM,WAAW,EAAE;YAEnE,MAAM,SAAS,GAAG;YAClB,MAAM,YAAY,GAAG;YACrB,MAAM,eAAe,GAAG;YACxB,MAAM,WAAW,GAAG;YACpB;YACA,IAAI,CAAC,2BACH,CAAA,gLAAA,uBAAmB,EAAE,MAAM,MAAM;YAEnC,KAAK,IAAI,WAAW,MAAM,WAAW,CACnC;YAEF,MAAM,WAAW,GAAG,EAAE;QACxB;IACF;IAEA,IAAI,sBAAsB,CAAA,0KAAA,iBAAa,EAAE,CAAC;QACxC,IAAI,2BACF,OAAO;IAEX;IAEA,IAAI,eAAe,CAAA,0KAAA,iBAAa,EAAE,CAAC;QACjC,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAU;IACZ;IAEA,IAAI,wBAAwB,CAAA,0KAAA,iBAAa,EAAE,CAAC,GAA+B;QACzE,iEAAiE;QACjE,yDAAyD;QACzD,4FAA4F;QAC5F,2CAA2C;QAC3C,+DAA+D;QAC/D,IAAI,SAAS;YACX,IAAI,QAAQ,IAAI,WAAW,SAAS;YACpC,CAAA,wKAAA,iBAAa,EAAE,OAAO;YACtB,QAAQ,CAAA,wKAAA,uBAAmB,EAAE;QAC/B;IACF;IAEA,IAAI,aAAa,CAAA,iKAAA,UAAM,EAAE;QACvB,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,aAA4B;YAC9B,WAAU,CAAC;gBACT,IAAI,2CAAqB,EAAE,WAAW,EAAE,EAAE,aAAa,KAAK,CAAA,wKAAA,eAAW,EAAE,EAAE,aAAa,EAAE,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW,IAAI;wBAwCtH;oBAvCF,IAAI,mDAA6B,CAAA,GAAA,sLAAa,EAAE,EAAE,WAAW,GAAG,EAAE,GAAG,GACnE,EAAE,cAAc;oBAGlB,wEAAwE;oBACxE,0EAA0E;oBAC1E,wCAAwC;oBACxC,IAAI,wBAAwB;oBAC5B,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,EAAE,MAAM,EAAE;wBACjC,MAAM,MAAM,GAAG,EAAE,aAAa;wBAC9B,MAAM,SAAS,GAAG;wBAClB,MAAM,WAAW,GAAG;wBACpB,wBAAwB,kBAAkB,GAAG;wBAE7C,gFAAgF;wBAChF,2GAA2G;wBAC3G,wIAAwI;wBACxI,IAAI,iBAAiB,EAAE,aAAa;wBACpC,IAAI,UAAU,CAAC;4BACb,IAAI,2CAAqB,GAAG,mBAAmB,CAAC,EAAE,MAAM,IAAI,CAAA,wKAAA,eAAW,EAAE,gBAAgB,CAAA,wKAAA,iBAAa,EAAE,OAAO,MAAM,MAAM,EACzH,eAAe,kCAAY,MAAM,MAAM,EAAE,IAAI;wBAEjD;wBAEA,kBAAkB,CAAA,sKAAA,mBAAe,EAAE,EAAE,aAAa,GAAG,SAAS,CAAA,GAAA,sKAAI,EAAE,SAAS,UAAU;oBACzF;oBAEA,IAAI,uBACF,EAAE,eAAe;oBAGnB,yFAAyF;oBACzF,+EAA+E;oBAC/E,0FAA0F;oBAC1F,iDAAiD;oBACjD,gEAAgE;oBAChE,gDAAgD;oBAChD,uDAAuD;oBACvD,IAAI,EAAE,OAAO,IAAI,CAAA,oKAAA,QAAI,KAAA,CACnB,uBAAA,MAAM,aAAa,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAqB,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,WAAW;gBAEjD,OAAO,IAAI,EAAE,GAAG,KAAK,QACnB,MAAM,aAAa,GAAG,IAAI;YAE9B;YACA,SAAQ,CAAC;gBACP,IAAI,KAAK,CAAC,CAAA,wKAAA,eAAW,EAAE,EAAE,aAAa,EAAE,CAAA,uKAAA,kBAAa,EAAE,EAAE,WAAW,IAClE;gBAGF,IAAI,KAAK,EAAE,MAAM,KAAK,KAAK,CAAC,MAAM,iBAAiB,IAAI,CAAE,CAAA,oKAAA,WAAO,EAAU,SAAS,EAAE;oBACnF,IAAI,wBAAwB;oBAC5B,IAAI,YACF,EAAE,cAAc;oBAGlB,iEAAiE;oBACjE,0CAA0C;oBAC1C,IAAI,CAAC,MAAM,yBAAyB,IAAI,CAAC,MAAM,SAAS,IAAK,CAAA,MAAM,WAAW,KAAK,aAAa,CAAA,0KAAA,iBAAa,EAAE,EAAE,WAAW,CAAA,GAAI;wBAC9H,IAAI,iBAAiB,kBAAkB,GAAG;wBAC1C,IAAI,cAAc,eAAe,GAAG;wBACpC,IAAI,eAAe,gBAAgB,GAAG;wBACtC,aAAa;wBACb,wBAAwB,kBAAkB,eAAe;oBAC3D,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM,WAAW,KAAK,YAAY;wBAC9D,IAAI,cAAc,MAAM,WAAW,IAAK,EAAE,WAAW,CAAkB,WAAW,IAAmB;wBACrG,IAAI,cAAc,eAAe,kCAAY,EAAE,aAAa,EAAE,IAAI;wBAClE,IAAI,eAAgB,gBAAgB,kCAAY,EAAE,aAAa,EAAE,IAAI,aAAa;wBAClF,wBAAwB,eAAe;wBACvC,MAAM,YAAY,GAAG;wBACrB,aAAa;wBACb,OAAO;oBACT;oBAEA,MAAM,yBAAyB,GAAG;oBAClC,IAAI,uBACF,EAAE,eAAe;gBAErB;YACF;QACF;QAEA,IAAI,UAAU,CAAC;gBA0BkB;YAzB/B,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI,2CAAqB,GAAG,MAAM,MAAM,GAAG;oBAwB5E;gBAvBA,IAAI,mDAA6B,CAAA,wKAAA,iBAAa,EAAE,IAAI,EAAE,GAAG,GACvD,EAAE,cAAc;gBAGlB,IAAI,SAAS,CAAA,wKAAA,iBAAa,EAAE;gBAC5B,IAAI,aAAa,CAAA,wKAAA,eAAW,EAAE,MAAM,MAAM,EAAE,CAAA,wKAAA,iBAAa,EAAE;gBAC3D,gBAAgB,kCAAY,MAAM,MAAM,EAAE,IAAI,YAAY;gBAC1D,IAAI,YACF,sBAAsB,GAAG,MAAM,MAAM;gBAEvC;gBAEA,+EAA+E;gBAC/E,4EAA4E;gBAC5E,yCAAyC;gBACzC,IAAI,EAAE,GAAG,KAAK,WAAW,uCAAiB,MAAM,MAAM,KAAK,CAAA,wKAAA,eAAW,EAAE,MAAM,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,mCAAa,EAAE;oBACjH,2EAA2E;oBAC3E,yEAAyE;oBACzE,CAAC,CAAC,mCAAa,GAAG;oBAClB,CAAA,mKAAA,YAAO,EAAE,MAAM,MAAM,EAAE,GAAG;gBAC5B;gBAEA,MAAM,SAAS,GAAG;iBAClB,wBAAA,MAAM,aAAa,MAAA,QAAnB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAqB,MAAM,CAAC,EAAE,GAAG;YACnC,OAAO,IAAI,EAAE,GAAG,KAAK,UAAA,CAAA,CAAU,uBAAA,MAAM,aAAa,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAqB,IAAI,GAAE;oBAOtD;gBANF,8EAA8E;gBAC9E,gFAAgF;gBAChF,oEAAoE;gBACpE,IAAI,SAAS,MAAM,aAAa;gBAChC,MAAM,aAAa,GAAG;gBACtB,KAAK,IAAI,SAAS,OAAO,MAAM,GAAA,CAC7B,gBAAA,MAAM,MAAM,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAc,aAAa,CAAC,IAAI,cAAc,SAAS;YAE3D;QACF;QAEA,IAAI,OAAO,iBAAiB,aAAa;YACvC,WAAW,aAAa,GAAG,CAAC;gBAC1B,2EAA2E;gBAC3E,IAAI,EAAE,MAAM,KAAK,KAAK,CAAC,CAAA,wKAAA,eAAW,EAAE,EAAE,aAAa,EAAE,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW,IAC/E;gBAGF,oFAAoF;gBACpF,8DAA8D;gBAC9D,iDAAiD;gBACjD,iDAAiD;gBACjD,IAAI,CAAA,0KAAA,wBAAoB,EAAE,EAAE,WAAW,GAAG;oBACxC,MAAM,WAAW,GAAG;oBACpB;gBACF;gBAEA,MAAM,WAAW,GAAG,EAAE,WAAW;gBAEjC,IAAI,wBAAwB;gBAC5B,IAAI,CAAC,MAAM,SAAS,EAAE;oBACpB,MAAM,SAAS,GAAG;oBAClB,MAAM,YAAY,GAAG;oBACrB,MAAM,eAAe,GAAG,EAAE,SAAS;oBACnC,MAAM,MAAM,GAAG,EAAE,aAAa;oBAE9B,IAAI,CAAC,2BACH,CAAA,gLAAA,uBAAmB,EAAE,MAAM,MAAM;oBAGnC,wBAAwB,kBAAkB,GAAG,MAAM,WAAW;oBAE9D,oFAAoF;oBACpF,0DAA0D;oBAC1D,IAAI,SAAS,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW;oBACzC,IAAI,2BAA2B,QAC7B,OAAO,qBAAqB,CAAC,EAAE,SAAS;oBAG1C,kBAAkB,CAAA,qKAAA,oBAAe,EAAE,EAAE,aAAa,GAAG,aAAa,aAAa;oBAC/E,kBAAkB,CAAA,sKAAA,mBAAe,EAAE,EAAE,aAAa,GAAG,iBAAiB,iBAAiB;gBACzF;gBAEA,IAAI,uBACF,EAAE,eAAe;YAErB;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,EAAE,aAAa,EAAE,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,IAAI,EAAE,MAAM,KAAK,GAAG;oBAClB,IAAI,qBAAqB;wBACvB,IAAI,UAAU,CAAA,wKAAA,eAAW,EAAE,EAAE,MAAM;wBACnC,IAAI,SACF,MAAM,WAAW,CAAC,IAAI,CAAC;oBAE3B;oBAEA,EAAE,eAAe;gBACnB;YACF;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,wGAAwG;gBACxG,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,EAAE,aAAa,EAAE,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW,MAAM,MAAM,WAAW,KAAK,WACzF;gBAGF,sEAAsE;gBACtE,IAAI,EAAE,MAAM,KAAK,KAAK,CAAC,MAAM,SAAS,EACpC,eAAe,GAAG,MAAM,WAAW,IAAI,EAAE,WAAW;YAExD;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,EAAE,SAAS,KAAK,MAAM,eAAe,IAAI,MAAM,MAAM,IAAI,CAAC,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,MAAM;oBAC7G,MAAM,YAAY,GAAG;oBACrB,kBAAkB,kCAAY,MAAM,MAAM,EAAE,IAAI,MAAM,WAAW;gBACnE;YACF;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,EAAE,SAAS,KAAK,MAAM,eAAe,IAAI,MAAM,MAAM,IAAI,MAAM,YAAY,IAAI,MAAM,WAAW,IAAI,MAAM;oBAC5G,MAAM,YAAY,GAAG;oBACrB,gBAAgB,kCAAY,MAAM,MAAM,EAAE,IAAI,MAAM,WAAW,EAAE;oBACjE,oBAAoB;gBACtB;YACF;YAEA,IAAI,cAAc,CAAC;gBACjB,IAAI,EAAE,SAAS,KAAK,MAAM,eAAe,IAAI,MAAM,SAAS,IAAI,EAAE,MAAM,KAAK,KAAK,MAAM,MAAM,EAAE;oBAC9F,IAAI,CAAA,GAAA,oLAAW,EAAE,MAAM,MAAM,EAAE,CAAA,wKAAA,iBAAa,EAAE,OAAO,MAAM,WAAW,IAAI,MAAM;wBAC9E,4EAA4E;wBAC5E,iGAAiG;wBACjG,sDAAsD;wBACtD,8CAA8C;wBAC9C,4EAA4E;wBAC5E,uEAAuE;wBACvE,oFAAoF;wBACpF,qFAAqF;wBACrF,sEAAsE;wBACtE,8IAA8I;wBAC9I,IAAI,UAAU;wBACd,IAAI,UAAU,WAAW;4BACvB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,YAAY,aAAA;gCAC7C,IAAI,SACF,OAAO;qCACF;oCACL,CAAA,iLAAA,wBAAoB,EAAE,MAAM,MAAM;oCAClC,MAAM,MAAM,CAAC,KAAK;gCACpB;;wBAEJ,GAAG;wBACH,yDAAyD;wBACzD,+DAA+D;wBAC/D,kBAAkB,EAAE,aAAa,EAAc,SAAS,IAAM,UAAU,MAAM;wBAC9E,MAAM,WAAW,CAAC,IAAI,CAAC,IAAM,aAAa;oBAC5C,OACE,OAAO;oBAGT,0EAA0E;oBAC1E,MAAM,YAAY,GAAG;gBACvB;YACF;YAEA,IAAI,kBAAkB,CAAC;gBACrB,OAAO;YACT;YAEA,WAAW,WAAW,GAAG,CAAC;gBACxB,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,EAAE,aAAa,EAAE,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW,IAC7D;gBAGF,0FAA0F;gBAC1F,OAAO;YACT;QACF,OAAO,uCAAqC;;QA4N5C;QAEA,OAAO;IACT,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,yDAAyD;IACzD,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,CAAC,UAAU,QAAQ,GAAG,CAAC,QAAQ,gCAAK,QACtC;QAGF,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,OAAO,OAAO;QACrD,IAAI,CAAC,iBAAiB,CAAC,cAAc,IAAI,IAAI,cAAc,cAAc,CAAC,iCACxE;QAGF,MAAM,QAAQ,cAAc,aAAa,CAAC;QAC1C,MAAM,EAAE,GAAG;QACX,mEAAmE;QACnE,0DAA0D;QAC1D,iDAAiD;QACjD,MAAM,WAAW,GAAG,CAAC;;GAEtB,EAAE,0CAAoB;;;;IAIrB,CAAC,CAAC,IAAI;QACN,cAAc,IAAI,CAAC,OAAO,CAAC;IAC7B,GAAG;QAAC;KAAO;IAEX,mFAAmF;IACnF,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,QAAQ,IAAI,OAAO;QACvB,OAAO;gBAEkB;YADvB,IAAI,CAAC,2BACH,CAAA,gLAAA,uBAAmB,EAAE,CAAA,gBAAA,MAAM,MAAM,MAAA,QAAZ,kBAAA,KAAA,IAAA,gBAAgB;YAEvC,KAAK,IAAI,WAAW,MAAM,WAAW,CACnC;YAEF,MAAM,WAAW,GAAG,EAAE;QACxB;IACF,GAAG;QAAC;KAA0B;IAE9B,OAAO;QACL,WAAW,iBAAiB;QAC5B,YAAY,CAAA,sKAAA,aAAS,EAAE,UAAU,YAAY;YAAC,CAAC,0CAAoB,EAAE;QAAI;IAC3E;AACF;AAEA,SAAS,uCAAiB,MAAe;IACvC,OAAO,OAAO,OAAO,KAAK,OAAO,OAAO,YAAY,CAAC;AACvD;AAEA,SAAS,2CAAqB,KAAoB,EAAE,aAAsB;IACxE,MAAM,EAAA,KAAC,GAAG,EAAA,MAAE,IAAI,EAAC,GAAG;IACpB,MAAM,UAAU;IAChB,MAAM,OAAO,QAAQ,YAAY,CAAC;IAClC,qDAAqD;IACrD,0BAA0B;IAC1B,OACG,CAAA,QAAQ,WAAW,QAAQ,OAAO,QAAQ,cAAc,SAAS,OAAM,KACxE,CAAE,CAAC,mBAAmB,CAAA,sKAAA,iBAAa,EAAE,SAAS,gBAAgB,IAAI,CAAC,sCAAgB,SAAS,QAC1F,mBAAmB,CAAA,sKAAA,iBAAa,EAAE,SAAS,mBAAmB,IAC9D,QAAQ,iBAAgB,KAC1B,2CAA2C;IAC3C,CAAE,CAAC,CAAA,SAAS,UAAW,CAAC,QAAQ,uCAAiB,QAAQ,KAAM,QAAQ,OAAM;AAEjF;AAEA,SAAS,wCAAkB,KAAiB;IAC1C,MAAM,EAAA,eAAC,aAAa,EAAC,GAAG;IACxB,IAAI,cAAc,MAAM,GAAG,GACzB,OAAO,aAAa,CAAC,EAAE;IAEzB,OAAO;AACT;AAEA,SAAS,mCACP,KAAiB,EACjB,SAAwB;IAExB,MAAM,iBAAiB,MAAM,cAAc;IAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;QAC9C,MAAM,QAAQ,cAAc,CAAC,EAAE;QAC/B,IAAI,MAAM,UAAU,KAAK,WACvB,OAAO;IAEX;IACA,OAAO;AACT;AAEA,SAAS,uCAAiB,MAAwB,EAAE,CAAgC;IAClF,IAAI,UAAU;IACd,IAAI,UAAU;IACd,IAAI,EAAE,aAAa,IAAI,EAAE,aAAa,CAAC,MAAM,KAAK,GAAG;QACnD,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO;QACpC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,OAAO;IACtC;IACA,OAAO;QACL,eAAe;QACf,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,OAAO;QAClB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,MAAM;iBAChB;iBACA;IACF;AACF;AAEA,SAAS,kCAAY,MAAwB,EAAE,CAAY;IACzD,IAAI,UAAU,EAAE,OAAO;IACvB,IAAI,UAAU,EAAE,OAAO;IACvB,OAAO;QACL,eAAe;QACf,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE,OAAO;QAClB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,MAAM;iBAChB;iBACA;IACF;AACF;AAkBA,SAAS,yCAAmB,KAAiB;IAC3C,IAAI,UAAU;IACd,IAAI,UAAU;IACd,IAAI,MAAM,KAAK,KAAK,WAClB,UAAW,MAAM,KAAK,GAAG;SACpB,IAAI,MAAM,OAAO,KAAK,WAC3B,UAAU,MAAM,OAAO;IAEzB,IAAI,MAAM,MAAM,KAAK,WACnB,UAAW,MAAM,MAAM,GAAG;SACrB,IAAI,MAAM,OAAO,KAAK,WAC3B,UAAU,MAAM,OAAO;IAGzB,OAAO;QACL,KAAK,MAAM,OAAO,GAAG;QACrB,OAAO,MAAM,OAAO,GAAG;QACvB,QAAQ,MAAM,OAAO,GAAG;QACxB,MAAM,MAAM,OAAO,GAAG;IACxB;AACF;AAEA,SAAS,+CAAyB,CAAO,EAAE,CAAO;IAChD,yCAAyC;IACzC,IAAI,EAAE,IAAI,GAAG,EAAE,KAAK,IAAI,EAAE,IAAI,GAAG,EAAE,KAAK,EACtC,OAAO;IAET,yCAAyC;IACzC,IAAI,EAAE,GAAG,GAAG,EAAE,MAAM,IAAI,EAAE,GAAG,GAAG,EAAE,MAAM,EACtC,OAAO;IAET,OAAO;AACT;AAEA,SAAS,mCAAa,KAAiB,EAAE,MAAe;IACtD,IAAI,OAAO,OAAO,qBAAqB;IACvC,IAAI,YAAY,yCAAmB;IACnC,OAAO,+CAAyB,MAAM;AACxC;AAEA,SAAS,6CAAuB,MAAe;IAC7C,IAAI,kBAAkB,kBACpB,OAAO;IAGT,IAAI,kBAAkB,mBACpB,OAAO,OAAO,IAAI,KAAK,YAAY,OAAO,IAAI,KAAK;IAGrD,IAAI,uCAAiB,SACnB,OAAO;IAGT,OAAO;AACT;AAEA,SAAS,mDAA6B,MAAe,EAAE,GAAW;IAChE,IAAI,kBAAkB,kBACpB,OAAO,CAAC,sCAAgB,QAAQ;IAGlC,OAAO,6CAAuB;AAChC;AAEA,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,sCAAgB,MAAwB,EAAE,GAAW;IAC5D,6DAA6D;IAC7D,OAAO,OAAO,IAAI,KAAK,cAAc,OAAO,IAAI,KAAK,UACjD,QAAQ,MACR,wCAAkB,GAAG,CAAC,OAAO,IAAI;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5189, "column": 0}, "map": {"version": 3, "file": "useFocusVisible.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocusVisible.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, getOwnerWindow, isMac, isVirtualClick} from '@react-aria/utils';\nimport {ignoreFocusEvent} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport type Modality = 'keyboard' | 'pointer' | 'virtual';\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent | null;\ntype Handler = (modality: Modality, e: HandlerEvent) => void;\nexport type FocusVisibleHandler = (isFocusVisible: boolean) => void;\nexport interface FocusVisibleProps {\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusVisibleResult {\n  /** Whether keyboard focus is visible globally. */\n  isFocusVisible: boolean\n}\n\nlet currentModality: null | Modality = null;\nlet changeHandlers = new Set<Handler>();\ninterface GlobalListenerData {\n  focus: () => void\n}\nexport let hasSetupGlobalListeners: Map<Window, GlobalListenerData> = new Map<Window, GlobalListenerData>(); // We use a map here to support setting event listeners across multiple document objects.\nlet hasEventBeforeFocus = false;\nlet hasBlurredWindowRecently = false;\n\n// Only Tab or Esc keys will make focus visible on text input elements\nconst FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\n\nfunction triggerChangeHandlers(modality: Modality, e: HandlerEvent) {\n  for (let handler of changeHandlers) {\n    handler(modality, e);\n  }\n}\n\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction isValidKey(e: KeyboardEvent) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || (!isMac() && e.altKey) || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\n\n\nfunction handleKeyboardEvent(e: KeyboardEvent) {\n  hasEventBeforeFocus = true;\n  if (isValidKey(e)) {\n    currentModality = 'keyboard';\n    triggerChangeHandlers('keyboard', e);\n  }\n}\n\nfunction handlePointerEvent(e: PointerEvent | MouseEvent) {\n  currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    hasEventBeforeFocus = true;\n    triggerChangeHandlers('pointer', e);\n  }\n}\n\nfunction handleClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true;\n    currentModality = 'virtual';\n  }\n}\n\nfunction handleFocusEvent(e: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || ignoreFocusEvent || !e.isTrusted) {\n    return;\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    currentModality = 'virtual';\n    triggerChangeHandlers('virtual', e);\n  }\n\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = false;\n}\n\nfunction handleWindowBlur() {\n  if (ignoreFocusEvent) {\n    return;\n  }\n\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = true;\n}\n\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction setupGlobalFocusEvents(element?: HTMLElement | null) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || hasSetupGlobalListeners.get(getOwnerWindow(element))) {\n    return;\n  }\n\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    hasEventBeforeFocus = true;\n    focus.apply(this, arguments as unknown as [options?: FocusOptions | undefined]);\n  };\n\n  documentObject.addEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.addEventListener('click', handleClickEvent, true);\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', handleFocusEvent, true);\n  windowObject.addEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    tearDownWindowFocusTracking(element);\n  }, {once: true});\n\n  hasSetupGlobalListeners.set(windowObject, {focus});\n}\n\nconst tearDownWindowFocusTracking = (element, loadListener?: () => void) => {\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n  if (loadListener) {\n    documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  }\n  if (!hasSetupGlobalListeners.has(windowObject)) {\n    return;\n  }\n  windowObject.HTMLElement.prototype.focus = hasSetupGlobalListeners.get(windowObject)!.focus;\n\n  documentObject.removeEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', handleClickEvent, true);\n\n  windowObject.removeEventListener('focus', handleFocusEvent, true);\n  windowObject.removeEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  hasSetupGlobalListeners.delete(windowObject);\n};\n\n/**\n * EXPERIMENTAL\n * Adds a window (i.e. iframe) to the list of windows that are being tracked for focus visible.\n *\n * Sometimes apps render portions of their tree into an iframe. In this case, we cannot accurately track if the focus\n * is visible because we cannot see interactions inside the iframe. If you have this in your application's architecture,\n * then this function will attach event listeners inside the iframe. You should call `addWindowFocusTracking` with an\n * element from inside the window you wish to add. We'll retrieve the relevant elements based on that.\n * Note, you do not need to call this for the default window, as we call it for you.\n *\n * When you are ready to stop listening, but you do not wish to unmount the iframe, you may call the cleanup function\n * returned by `addWindowFocusTracking`. Otherwise, when you unmount the iframe, all listeners and state will be cleaned\n * up automatically for you.\n *\n * @param element @default document.body - The element provided will be used to get the window to add.\n * @returns A function to remove the event listeners and cleanup the state.\n */\nexport function addWindowFocusTracking(element?: HTMLElement | null): () => void {\n  const documentObject = getOwnerDocument(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') {\n    setupGlobalFocusEvents(element);\n  } else {\n    loadListener = () => {\n      setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n\n  return () => tearDownWindowFocusTracking(element, loadListener);\n}\n\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') {\n  addWindowFocusTracking();\n}\n\n/**\n * If true, keyboard focus is visible.\n */\nexport function isFocusVisible(): boolean {\n  return currentModality !== 'pointer';\n}\n\nexport function getInteractionModality(): Modality | null {\n  return currentModality;\n}\n\nexport function setInteractionModality(modality: Modality): void {\n  currentModality = modality;\n  triggerChangeHandlers(modality, null);\n}\n\n/**\n * Keeps state of the current modality.\n */\nexport function useInteractionModality(): Modality | null {\n  setupGlobalFocusEvents();\n\n  let [modality, setModality] = useState(currentModality);\n  useEffect(() => {\n    let handler = () => {\n      setModality(currentModality);\n    };\n\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  }, []);\n\n  return useIsSSR() ? null : modality;\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction isKeyboardFocusEvent(isTextInput: boolean, modality: Modality, e: HandlerEvent) {\n  let document = getOwnerDocument(e?.target as Element);\n  const IHTMLInputElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).KeyboardEvent : KeyboardEvent;\n\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput ||\n    (document.activeElement instanceof IHTMLInputElement && !nonTextInputTypes.has(document.activeElement.type)) ||\n    document.activeElement instanceof IHTMLTextAreaElement ||\n    (document.activeElement instanceof IHTMLElement && document.activeElement.isContentEditable);\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\n\n/**\n * Manages focus visible state for the page, and subscribes individual components for updates.\n */\nexport function useFocusVisible(props: FocusVisibleProps = {}): FocusVisibleResult {\n  let {isTextInput, autoFocus} = props;\n  let [isFocusVisibleState, setFocusVisible] = useState(autoFocus || isFocusVisible());\n  useFocusVisibleListener((isFocusVisible) => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {isTextInput});\n\n  return {isFocusVisible: isFocusVisibleState};\n}\n\n/**\n * Listens for trigger change and reports if focus is visible (i.e., modality is not pointer).\n */\nexport function useFocusVisibleListener(fn: FocusVisibleHandler, deps: ReadonlyArray<any>, opts?: {isTextInput?: boolean}): void {\n  setupGlobalFocusEvents();\n\n  useEffect(() => {\n    let handler = (modality: Modality, e: HandlerEvent) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!isKeyboardFocusEvent(!!(opts?.isTextInput), modality, e)) {\n        return;\n      }\n      fn(isFocusVisible());\n    };\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AA2Ja,QAAQ,GAAG,CAAC,QAAQ,KAAK;;;;;;;;;;;AA3JtC;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAuBlH,IAAI,wCAAmC;AACvC,IAAI,uCAAiB,IAAI;AAIlB,IAAI,4CAA2D,IAAI,OAAmC,yFAAyF;AACtM,IAAI,4CAAsB;AAC1B,IAAI,iDAA2B;AAE/B,sEAAsE;AACtE,MAAM,iDAA2B;IAC/B,KAAK;IACL,QAAQ;AACV;AAEA,SAAS,4CAAsB,QAAkB,EAAE,CAAe;IAChE,KAAK,IAAI,WAAW,qCAClB,QAAQ,UAAU;AAEtB;AAEA;;CAEC,GACD,SAAS,iCAAW,CAAgB;IAClC,gFAAgF;IAChF,OAAO,CAAE,CAAA,EAAE,OAAO,IAAK,CAAC,CAAA,oKAAA,QAAI,OAAO,EAAE,MAAM,IAAK,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,MAAK;AAC1H;AAGA,SAAS,0CAAoB,CAAgB;IAC3C,4CAAsB;IACtB,IAAI,iCAAW,IAAI;QACjB,wCAAkB;QAClB,4CAAsB,YAAY;IACpC;AACF;AAEA,SAAS,yCAAmB,CAA4B;IACtD,wCAAkB;IAClB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,eAAe;QACtD,4CAAsB;QACtB,4CAAsB,WAAW;IACnC;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,IAAI,CAAA,0KAAA,iBAAa,EAAE,IAAI;QACrB,4CAAsB;QACtB,wCAAkB;IACpB;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,kFAAkF;IAClF,kFAAkF;IAClF,wCAAwC;IACxC,IAAI,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK,YAAY,CAAA,wKAAA,mBAAe,KAAK,CAAC,EAAE,SAAS,EAClF;IAGF,qGAAqG;IACrG,0FAA0F;IAC1F,IAAI,CAAC,6CAAuB,CAAC,gDAA0B;QACrD,wCAAkB;QAClB,4CAAsB,WAAW;IACnC;IAEA,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA,SAAS;IACP,IAAI,wKAAA,mBAAA,EACF;IAGF,6FAA6F;IAC7F,8DAA8D;IAC9D,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA;;CAEC,GACD,SAAS,6CAAuB,OAA4B;IAC1D,IAAI,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,0CAAwB,GAAG,CAAC,CAAA,sKAAA,iBAAa,EAAE,WACjH;IAGF,MAAM,eAAe,CAAA,sKAAA,iBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,sKAAA,mBAAe,EAAE;IAExC,0EAA0E;IAC1E,2EAA2E;IAC3E,0EAA0E;IAC1E,6DAA6D;IAC7D,IAAI,QAAQ,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK;IACpD,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG;QACzC,4CAAsB;QACtB,MAAM,KAAK,CAAC,IAAI,EAAE;IACpB;IAEA,eAAe,gBAAgB,CAAC,WAAW,2CAAqB;IAChE,eAAe,gBAAgB,CAAC,SAAS,2CAAqB;IAC9D,eAAe,gBAAgB,CAAC,SAAS,wCAAkB;IAE3D,iEAAiE;IACjE,+DAA+D;IAC/D,aAAa,gBAAgB,CAAC,SAAS,wCAAkB;IACzD,aAAa,gBAAgB,CAAC,QAAQ,wCAAkB;IAExD,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,aAAa,0CAAoB;IACnE,OAAO,uCAAqC;;IAI5C;IAEA,sBAAsB;IACtB,aAAa,gBAAgB,CAAC,gBAAgB;QAC5C,kDAA4B;IAC9B,GAAG;QAAC,MAAM;IAAI;IAEd,0CAAwB,GAAG,CAAC,cAAc;eAAC;IAAK;AAClD;AAEA,MAAM,oDAA8B,CAAC,SAAS;IAC5C,MAAM,eAAe,CAAA,sKAAA,iBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,sKAAA,mBAAe,EAAE;IACxC,IAAI,cACF,eAAe,mBAAmB,CAAC,oBAAoB;IAEzD,IAAI,CAAC,0CAAwB,GAAG,CAAC,eAC/B;IAEF,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,0CAAwB,GAAG,CAAC,cAAe,KAAK;IAE3F,eAAe,mBAAmB,CAAC,WAAW,2CAAqB;IACnE,eAAe,mBAAmB,CAAC,SAAS,2CAAqB;IACjE,eAAe,mBAAmB,CAAC,SAAS,wCAAkB;IAE9D,aAAa,mBAAmB,CAAC,SAAS,wCAAkB;IAC5D,aAAa,mBAAmB,CAAC,QAAQ,wCAAkB;IAE3D,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,aAAa,0CAAoB;IACtE,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;IAI5C;IAEA,0CAAwB,MAAM,CAAC;AACjC;AAmBO,SAAS,0CAAuB,OAA4B;IACjE,MAAM,iBAAiB,CAAA,sKAAA,mBAAe,EAAE;IACxC,IAAI;IACJ,IAAI,eAAe,UAAU,KAAK,WAChC,6CAAuB;SAClB;QACL,eAAe;YACb,6CAAuB;QACzB;QACA,eAAe,gBAAgB,CAAC,oBAAoB;IACtD;IAEA,OAAO,IAAM,kDAA4B,SAAS;AACpD;AAEA,kEAAkE;AAClE,iDAAiD;AACjD,IAAI,OAAO,aAAa,aACtB;AAMK,SAAS;IACd,OAAO,0CAAoB;AAC7B;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,0CAAuB,QAAkB;IACvD,wCAAkB;IAClB,4CAAsB,UAAU;AAClC;AAKO,SAAS;IACd;IAEA,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvC,CAAA,gKAAA,aAAQ,EAAE;QACR,IAAI,UAAU;YACZ,YAAY;QACd;QAEA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,GAAG,EAAE;IAEL,OAAO,CAAA,GAAA,6KAAO,MAAM,OAAO;AAC7B;AAEA,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED;;;CAGC,GACD,SAAS,2CAAqB,WAAoB,EAAE,QAAkB,EAAE,CAAe;IACrF,IAAI,YAAW,CAAA,sKAAA,mBAAe,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM;IACzC,MAAM,oBAAoB,OAAO,WAAW,cAAc,CAAA,sKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,gBAAgB,GAAG;IAClH,MAAM,uBAAuB,OAAO,WAAW,cAAc,CAAA,sKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,mBAAmB,GAAG;IACxH,MAAM,eAAe,OAAO,WAAW,cAAc,CAAA,GAAA,oLAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,WAAW,GAAG;IACxG,MAAM,iBAAiB,OAAO,WAAW,cAAc,CAAA,qKAAA,kBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,aAAa,GAAG;IAE5G,qKAAqK;IACrK,kIAAkI;IAClI,cAAc,eACX,UAAS,aAAa,YAAY,qBAAqB,CAAC,wCAAkB,GAAG,CAAC,UAAS,aAAa,CAAC,IAAI,KAC1G,UAAS,aAAa,YAAY,wBACjC,UAAS,aAAa,YAAY,gBAAgB,UAAS,aAAa,CAAC,iBAAiB;IAC7F,OAAO,CAAE,CAAA,eAAe,aAAa,cAAc,aAAa,kBAAkB,CAAC,8CAAwB,CAAC,EAAE,GAAG,CAAA;AACnH;AAKO,SAAS,0CAAgB,QAA2B,CAAC,CAAC;IAC3D,IAAI,EAAA,aAAC,WAAW,EAAA,WAAE,SAAS,EAAC,GAAG;IAC/B,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,iKAAA,WAAO,EAAE,aAAa;IACnE,0CAAwB,CAAC;QACvB,gBAAgB;IAClB,GAAG;QAAC;KAAY,EAAE;qBAAC;IAAW;IAE9B,OAAO;QAAC,gBAAgB;IAAmB;AAC7C;AAKO,SAAS,0CAAwB,EAAuB,EAAE,IAAwB,EAAE,IAA8B;IACvH;IAEA,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,UAAU,CAAC,UAAoB;YACjC,0GAA0G;YAC1G,IAAI,CAAC,2CAAqB,CAAC,CAAA,CAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,WAAW,GAAG,UAAU,IACzD;YAEF,GAAG;QACL;QACA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,uDAAuD;IACvD,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5444, "column": 0}, "map": {"version": 3, "file": "useFocus.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocus.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, FocusableElement, FocusEvents} from '@react-types/shared';\nimport {FocusEvent, useCallback} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument} from '@react-aria/utils';\nimport {useSyntheticBlurEvent} from './utils';\n\nexport interface FocusProps<Target = FocusableElement> extends FocusEvents<Target> {\n  /** Whether the focus events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusResult<Target = FocusableElement> {\n  /** Props to spread onto the target element. */\n  focusProps: DOMAttributes<Target>\n}\n\n/**\n * Handles focus events for the immediate target.\n * Focus events on child elements will be ignored.\n */\nexport function useFocus<Target extends FocusableElement = FocusableElement>(props: FocusProps<Target>): FocusResult<Target> {\n  let {\n    isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange\n  } = props;\n\n  const onBlur: FocusProps<Target>['onBlur'] = useCallback((e: FocusEvent<Target>) => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) {\n        onBlurProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(false);\n      }\n\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n\n\n  const onSyntheticFocus = useSyntheticBlurEvent<Target>(onBlur);\n\n  const onFocus: FocusProps<Target>['onFocus'] = useCallback((e: FocusEvent<Target>) => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = ownerDocument ? getActiveElement(ownerDocument) : getActiveElement();\n    if (e.target === e.currentTarget && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusProp) {\n        onFocusProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(true);\n      }\n\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n\n  return {\n    focusProps: {\n      onFocus: (!isDisabled && (onFocusProp || onFocusChange || onBlurProp)) ? onFocus : undefined,\n      onBlur: (!isDisabled && (onBlurProp || onFocusChange)) ? onBlur : undefined\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAqB3G,SAAS,0CAA6D,KAAyB;IACpG,IAAI,EAAA,YACF,UAAU,EACV,SAAS,WAAW,EACpB,QAAQ,UAAU,EAAA,eAClB,aAAa,EACd,GAAG;IAEJ,MAAM,SAAuC,CAAA,iKAAA,cAAU,EAAE,CAAC;QACxD,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC,IAAI,YACF,WAAW;YAGb,IAAI,eACF,cAAc;YAGhB,OAAO;QACT;IACF,GAAG;QAAC;QAAY;KAAc;IAG9B,MAAM,mBAAmB,CAAA,wKAAA,wBAAoB,EAAU;IAEvD,MAAM,UAAyC,CAAA,iKAAA,cAAU,EAAE,CAAC;QAC1D,kGAAkG;QAClG,oDAAoD;QAEpD,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,EAAE,MAAM;QAC/C,MAAM,gBAAgB,gBAAgB,CAAA,wKAAA,mBAAe,EAAE,iBAAiB,CAAA,wKAAA,mBAAe;QACvF,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,kBAAkB,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW,GAAG;YACnF,IAAI,aACF,YAAY;YAGd,IAAI,eACF,cAAc;YAGhB,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAe;QAAa;KAAiB;IAEjD,OAAO;QACL,YAAY;YACV,SAAU,CAAC,cAAe,CAAA,eAAe,iBAAiB,UAAS,IAAM,UAAU;YACnF,QAAS,CAAC,cAAe,CAAA,cAAc,aAAY,IAAM,SAAS;QACpE;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5511, "column": 0}, "map": {"version": 3, "file": "useFocusWithin.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocusWithin.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {createSyntheticEvent, setEventTarget, useSyntheticBlurEvent} from './utils';\nimport {DOMAttributes} from '@react-types/shared';\nimport {FocusEvent, useCallback, useRef} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\n\nexport interface FocusWithinProps {\n  /** Whether the focus within events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when the target element or a descendant receives focus. */\n  onFocusWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the target element and all descendants lose focus. */\n  onBlurWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the the focus within state changes. */\n  onFocusWithinChange?: (isFocusWithin: boolean) => void\n}\n\nexport interface FocusWithinResult {\n  /** Props to spread onto the target element. */\n  focusWithinProps: DOMAttributes\n}\n\n/**\n * Handles focus events for the target and its descendants.\n */\nexport function useFocusWithin(props: FocusWithinProps): FocusWithinResult {\n  let {\n    isDisabled,\n    onBlurWithin,\n    onFocusWithin,\n    onFocusWithinChange\n  } = props;\n  let state = useRef({\n    isFocusWithin: false\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let onBlur = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n    // when moving focus inside the element. Only trigger if the currentTarget doesn't\n    // include the relatedTarget (where focus is moving).\n    if (state.current.isFocusWithin && !(e.currentTarget as Element).contains(e.relatedTarget as Element)) {\n      state.current.isFocusWithin = false;\n      removeAllGlobalListeners();\n\n      if (onBlurWithin) {\n        onBlurWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(false);\n      }\n    }\n  }, [onBlurWithin, onFocusWithinChange, state, removeAllGlobalListeners]);\n\n  let onSyntheticFocus = useSyntheticBlurEvent(onBlur);\n  let onFocus = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = getActiveElement(ownerDocument);\n    if (!state.current.isFocusWithin && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusWithin) {\n        onFocusWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(true);\n      }\n\n      state.current.isFocusWithin = true;\n      onSyntheticFocus(e);\n\n      // Browsers don't fire blur events when elements are removed from the DOM.\n      // However, if a focus event occurs outside the element we're tracking, we\n      // can manually fire onBlur.\n      let currentTarget = e.currentTarget;\n      addGlobalListener(ownerDocument, 'focus', e => {\n        if (state.current.isFocusWithin && !nodeContains(currentTarget, e.target as Element)) {\n          let nativeEvent = new ownerDocument.defaultView!.FocusEvent('blur', {relatedTarget: e.target});\n          setEventTarget(nativeEvent, currentTarget);\n          let event = createSyntheticEvent<FocusEvent>(nativeEvent);\n          onBlur(event);\n        }\n      }, {capture: true});\n    }\n  }, [onFocusWithin, onFocusWithinChange, onSyntheticFocus, addGlobalListener, onBlur]);\n\n  if (isDisabled) {\n    return {\n      focusWithinProps: {\n        // These cannot be null, that would conflict in mergeProps\n        onFocus: undefined,\n        onBlur: undefined\n      }\n    };\n  }\n\n  return {\n    focusWithinProps: {\n      onFocus,\n      onBlur\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AA0B3G,SAAS,0CAAe,KAAuB;IACpD,IAAI,EAAA,YACF,UAAU,EAAA,cACV,YAAY,EAAA,eACZ,aAAa,EAAA,qBACb,mBAAmB,EACpB,GAAG;IACJ,IAAI,QAAQ,CAAA,iKAAA,SAAK,EAAE;QACjB,eAAe;IACjB;IAEA,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,8KAAA,qBAAiB;IAErE,IAAI,SAAS,CAAA,iKAAA,cAAU,EAAE,CAAC;QACxB,0CAA0C;QAC1C,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,iFAAiF;QACjF,kFAAkF;QAClF,qDAAqD;QACrD,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAE,EAAE,aAAa,CAAa,QAAQ,CAAC,EAAE,aAAa,GAAc;YACrG,MAAM,OAAO,CAAC,aAAa,GAAG;YAC9B;YAEA,IAAI,cACF,aAAa;YAGf,IAAI,qBACF,oBAAoB;QAExB;IACF,GAAG;QAAC;QAAc;QAAqB;QAAO;KAAyB;IAEvE,IAAI,mBAAmB,CAAA,wKAAA,wBAAoB,EAAE;IAC7C,IAAI,UAAU,CAAA,iKAAA,cAAU,EAAE,CAAC;QACzB,0CAA0C;QAC1C,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,kGAAkG;QAClG,oDAAoD;QACpD,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,EAAE,MAAM;QAC/C,MAAM,gBAAgB,CAAA,wKAAA,mBAAe,EAAE;QACvC,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,IAAI,kBAAkB,CAAA,wKAAA,iBAAa,EAAE,EAAE,WAAW,GAAG;YACnF,IAAI,eACF,cAAc;YAGhB,IAAI,qBACF,oBAAoB;YAGtB,MAAM,OAAO,CAAC,aAAa,GAAG;YAC9B,iBAAiB;YAEjB,0EAA0E;YAC1E,0EAA0E;YAC1E,4BAA4B;YAC5B,IAAI,gBAAgB,EAAE,aAAa;YACnC,kBAAkB,eAAe,SAAS,CAAA;gBACxC,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,eAAe,EAAE,MAAM,GAAc;oBACpF,IAAI,cAAc,IAAI,cAAc,WAAW,CAAE,UAAU,CAAC,QAAQ;wBAAC,eAAe,EAAE,MAAM;oBAAA;oBAC5F,CAAA,wKAAA,iBAAa,EAAE,aAAa;oBAC5B,IAAI,QAAQ,CAAA,wKAAA,uBAAmB,EAAc;oBAC7C,OAAO;gBACT;YACF,GAAG;gBAAC,SAAS;YAAI;QACnB;IACF,GAAG;QAAC;QAAe;QAAqB;QAAkB;QAAmB;KAAO;IAEpF,IAAI,YACF,OAAO;QACL,kBAAkB;YAChB,0DAA0D;YAC1D,SAAS;YACT,QAAQ;QACV;IACF;IAGF,OAAO;QACL,kBAAkB;qBAChB;oBACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5619, "column": 0}, "map": {"version": 3, "file": "useHover.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useHover.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, HoverEvents} from '@react-types/shared';\nimport {getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface HoverProps extends HoverEvents {\n  /** Whether the hover events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface HoverResult {\n  /** Props to spread on the target element. */\n  hoverProps: DOMAttributes,\n  isHovered: boolean\n}\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet globalIgnoreEmulatedMouseEvents = false;\nlet hoverCount = 0;\n\nfunction setGlobalIgnoreEmulatedMouseEvents() {\n  globalIgnoreEmulatedMouseEvents = true;\n\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\n\nfunction handleGlobalPointerEvent(e: PointerEvent) {\n  if (e.pointerType === 'touch') {\n    setGlobalIgnoreEmulatedMouseEvents();\n  }\n}\n\nfunction setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') {\n    return;\n  }\n\n  if (hoverCount === 0) {\n    if (typeof PointerEvent !== 'undefined') {\n      document.addEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.addEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  }\n\n  hoverCount++;\n  return () => {\n    hoverCount--;\n    if (hoverCount > 0) {\n      return;\n    }\n\n    if (typeof PointerEvent !== 'undefined') {\n      document.removeEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.removeEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  };\n}\n\n/**\n * Handles pointer hover interactions for an element. Normalizes behavior\n * across browsers and platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useHover(props: HoverProps): HoverResult {\n  let {\n    onHoverStart,\n    onHoverChange,\n    onHoverEnd,\n    isDisabled\n  } = props;\n\n  let [isHovered, setHovered] = useState(false);\n  let state = useRef({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n\n  useEffect(setupGlobalTouchEvents, []);\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let {hoverProps, triggerHoverEnd} = useMemo(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) {\n        return;\n      }\n\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener(getOwnerDocument(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !nodeContains(state.target, e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      }, {capture: true});\n\n      if (onHoverStart) {\n        onHoverStart({\n          type: 'hoverstart',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(true);\n      }\n\n      setHovered(true);\n    };\n\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n\n      if (pointerType === 'touch' || !state.isHovered || !target) {\n        return;\n      }\n\n      state.isHovered = false;\n      removeAllGlobalListeners();\n\n      if (onHoverEnd) {\n        onHoverEnd({\n          type: 'hoverend',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(false);\n      }\n\n      setHovered(false);\n    };\n\n    let hoverProps: DOMAttributes = {};\n\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = (e) => {\n        if (globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') {\n          return;\n        }\n\n        triggerHoverStart(e, e.pointerType);\n      };\n\n      hoverProps.onPointerLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n\n      hoverProps.onMouseEnter = (e) => {\n        if (!state.ignoreEmulatedMouseEvents && !globalIgnoreEmulatedMouseEvents) {\n          triggerHoverStart(e, 'mouse');\n        }\n\n        state.ignoreEmulatedMouseEvents = false;\n      };\n\n      hoverProps.onMouseLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, 'mouse');\n        }\n      };\n    }\n    return {hoverProps, triggerHoverEnd};\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n\n  useEffect(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) {\n      triggerHoverEnd({currentTarget: state.target}, state.pointerType);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n\n  return {\n    hoverProps,\n    isHovered\n  };\n}\n"], "names": [], "mappings": ";;;AAgEe,QAAQ,GAAG,CAAC,QAAQ,KAAK;;;;;;;AAhExC;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAiBlH,oGAAoG;AACpG,iFAAiF;AACjF,sDAAsD;AACtD,IAAI,wDAAkC;AACtC,IAAI,mCAAa;AAEjB,SAAS;IACP,wDAAkC;IAElC,wFAAwF;IACxF,sFAAsF;IACtF,wFAAwF;IACxF,oEAAoE;IACpE,WAAW;QACT,wDAAkC;IACpC,GAAG;AACL;AAEA,SAAS,+CAAyB,CAAe;IAC/C,IAAI,EAAE,WAAW,KAAK,SACpB;AAEJ;AAEA,SAAS;IACP,IAAI,OAAO,aAAa,aACtB;IAGF,IAAI,qCAAe,GAAG;QACpB,IAAI,OAAO,iBAAiB,aAC1B,SAAS,gBAAgB,CAAC,aAAa;aAClC,uCACL,SAAS,gBAAgB,CAAC,YAAY;;QAAA;IAE1C;IAEA;IACA,OAAO;QACL;QACA,IAAI,mCAAa,GACf;QAGF,IAAI,OAAO,iBAAiB,aAC1B,SAAS,mBAAmB,CAAC,aAAa;aACrC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAClC,SAAS,mBAAmB,CAAC,YAAY;;QAAA;IAE7C;AACF;AAMO,SAAS,0CAAS,KAAiB;IACxC,IAAI,EAAA,cACF,YAAY,EAAA,eACZ,aAAa,EAAA,YACb,UAAU,EAAA,YACV,UAAU,EACX,GAAG;IAEJ,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvC,IAAI,QAAQ,CAAA,iKAAA,SAAK,EAAE;QACjB,WAAW;QACX,2BAA2B;QAC3B,aAAa;QACb,QAAQ;IACV,GAAG,OAAO;IAEV,CAAA,iKAAA,YAAQ,EAAE,8CAAwB,EAAE;IACpC,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,8KAAA,qBAAiB;IAErE,IAAI,EAAA,YAAC,UAAU,EAAA,iBAAE,eAAe,EAAC,GAAG,CAAA,iKAAA,UAAM,EAAE;QAC1C,IAAI,oBAAoB,CAAC,OAAO;YAC9B,MAAM,WAAW,GAAG;YACpB,IAAI,cAAc,gBAAgB,WAAW,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxG;YAGF,MAAM,SAAS,GAAG;YAClB,IAAI,SAAS,MAAM,aAAa;YAChC,MAAM,MAAM,GAAG;YAEf,kGAAkG;YAClG,gGAAgG;YAChG,kFAAkF;YAClF,yGAAyG;YACzG,kBAAkB,CAAA,sKAAA,mBAAe,EAAE,MAAM,MAAM,GAAG,eAAe,CAAA;gBAC/D,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,MAAM,MAAM,EAAE,EAAE,MAAM,GACzE,gBAAgB,GAAG,EAAE,WAAW;YAEpC,GAAG;gBAAC,SAAS;YAAI;YAEjB,IAAI,cACF,aAAa;gBACX,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,kBAAkB,CAAC,OAAO;YAC5B,IAAI,SAAS,MAAM,MAAM;YACzB,MAAM,WAAW,GAAG;YACpB,MAAM,MAAM,GAAG;YAEf,IAAI,gBAAgB,WAAW,CAAC,MAAM,SAAS,IAAI,CAAC,QAClD;YAGF,MAAM,SAAS,GAAG;YAClB;YAEA,IAAI,YACF,WAAW;gBACT,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,aAA4B,CAAC;QAEjC,IAAI,OAAO,iBAAiB,aAAa;YACvC,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,yDAAmC,EAAE,WAAW,KAAK,SACvD;gBAGF,kBAAkB,GAAG,EAAE,WAAW;YACpC;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAClD,gBAAgB,GAAG,EAAE,WAAW;YAEpC;QACF,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;QAkB5C;QACA,OAAO;wBAAC;6BAAY;QAAe;IACrC,GAAG;QAAC;QAAc;QAAe;QAAY;QAAY;QAAO;QAAmB;KAAyB;IAE5G,CAAA,iKAAA,YAAQ,EAAE;QACR,iEAAiE;QACjE,qFAAqF;QACrF,IAAI,YACF,gBAAgB;YAAC,eAAe,MAAM,MAAM;QAAA,GAAG,MAAM,WAAW;IAEpE,uDAAuD;IACvD,GAAG;QAAC;KAAW;IAEf,OAAO;oBACL;mBACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5777, "column": 0}, "map": {"version": 3, "file": "focusSafely.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/focusSafely.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {\n  focusWithoutScrolling,\n  getActiveElement,\n  getOwnerDocument,\n  runAfterTransition\n} from '@react-aria/utils';\nimport {getInteractionModality} from './useFocusVisible';\n\n/**\n * A utility function that focuses an element while avoiding undesired side effects such\n * as page scrolling and screen reader issues with CSS transitions.\n */\nexport function focusSafely(element: FocusableElement): void {\n  // If the user is interacting with a virtual cursor, e.g. screen reader, then\n  // wait until after any animated transitions that are currently occurring on\n  // the page before shifting focus. This avoids issues with VoiceOver on iOS\n  // causing the page to scroll when moving focus if the element is transitioning\n  // from off the screen.\n  const ownerDocument = getOwnerDocument(element);\n  const activeElement = getActiveElement(ownerDocument);\n  if (getInteractionModality() === 'virtual') {\n    let lastFocusedElement = activeElement;\n    runAfterTransition(() => {\n      // If focus did not move and the element is still in the document, focus it.\n      if (getActiveElement(ownerDocument) === lastFocusedElement && element.isConnected) {\n        focusWithoutScrolling(element);\n      }\n    });\n  } else {\n    focusWithoutScrolling(element);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAeM,SAAS,0CAAY,OAAyB;IACnD,6EAA6E;IAC7E,4EAA4E;IAC5E,2EAA2E;IAC3E,+EAA+E;IAC/E,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE;IACvC,MAAM,gBAAgB,CAAA,wKAAA,mBAAe,EAAE;IACvC,IAAI,CAAA,kLAAA,yBAAqB,QAAQ,WAAW;QAC1C,IAAI,qBAAqB;QACzB,CAAA,8KAAA,qBAAiB,EAAE;YACjB,4EAA4E;YAC5E,IAAI,CAAA,wKAAA,mBAAe,EAAE,mBAAmB,sBAAsB,QAAQ,WAAW,EAC/E,CAAA,iLAAA,wBAAoB,EAAE;QAE1B;IACF,OACE,CAAA,iLAAA,wBAAoB,EAAE;AAE1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5821, "column": 0}, "map": {"version": 3, "file": "useFocusRing.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/useFocusRing.ts"], "sourcesContent": ["import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAoCO,SAAS,0CAAa,QAA4B,CAAC,CAAC;IACzD,IAAI,EAAA,WACF,YAAY,KAAA,EAAA,aACZ,WAAW,EAAA,QACX,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ,CAAA,iKAAA,SAAK,EAAE;QACjB,WAAW;QACX,gBAAgB,aAAa,CAAA,kLAAA,iBAAa;IAC5C;IACA,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvC,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,iKAAA,WAAO,EAAE,IAAM,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc;IAEnH,IAAI,cAAc,CAAA,iKAAA,cAAU,EAAE,IAAM,gBAAgB,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc,GAAG,EAAE;IAEhH,IAAI,gBAAgB,CAAA,iKAAA,cAAU,EAAE,CAAA;QAC9B,MAAM,OAAO,CAAC,SAAS,GAAG;QAC1B,WAAW;QACX;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,kLAAA,0BAAsB,EAAE,CAAC;QACvB,MAAM,OAAO,CAAC,cAAc,GAAG;QAC/B;IACF,GAAG,EAAE,EAAE;qBAAC;IAAW;IAEnB,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;QAC1B,YAAY;uBACZ;IACF;IAEA,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,CAAA,iLAAA,iBAAa,EAAE;QACtC,YAAY,CAAC;QACb,qBAAqB;IACvB;IAEA,OAAO;mBACL;QACA,gBAAgB;QAChB,YAAY,SAAS,mBAAmB;IAC1C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5874, "column": 0}, "map": {"version": 3, "file": "FocusScope.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/FocusScope.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  createShadowTreeWalker,\n  getActiveElement,\n  getEventTarget,\n  getOwnerDocument,\n  isAndroid,\n  isChrome,\n  isFocusable,\n  isTabbable,\n  ShadowTreeWalker,\n  useLayoutEffect\n} from '@react-aria/utils';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport React, {JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\nexport interface FocusScopeProps {\n  /** The contents of the focus scope. */\n  children: ReactNode,\n\n  /**\n   * Whether to contain focus inside the scope, so users cannot\n   * move focus outside, for example in a modal dialog.\n   */\n  contain?: boolean,\n\n  /**\n   * Whether to restore focus back to the element that was focused\n   * when the focus scope mounted, after the focus scope unmounts.\n   */\n  restoreFocus?: boolean,\n\n  /** Whether to auto focus the first focusable element in the focus scope on mount. */\n  autoFocus?: boolean\n}\n\nexport interface FocusManagerOptions {\n  /** The element to start searching from. The currently focused element by default. */\n  from?: Element,\n  /** Whether to only include tabbable elements, or all focusable elements. */\n  tabbable?: boolean,\n  /** Whether focus should wrap around when it reaches the end of the scope. */\n  wrap?: boolean,\n  /** A callback that determines whether the given element is focused. */\n  accept?: (node: Element) => boolean\n}\n\nexport interface FocusManager {\n  /** Moves focus to the next focusable or tabbable element in the focus scope. */\n  focusNext(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the previous focusable or tabbable element in the focus scope. */\n  focusPrevious(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the first focusable or tabbable element in the focus scope. */\n  focusFirst(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the last focusable or tabbable element in the focus scope. */\n  focusLast(opts?: FocusManagerOptions): FocusableElement | null\n}\n\ntype ScopeRef = RefObject<Element[] | null> | null;\ninterface IFocusContext {\n  focusManager: FocusManager,\n  parentNode: TreeNode | null\n}\n\nconst FocusContext = React.createContext<IFocusContext | null>(null);\nconst RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\n\nlet activeScope: ScopeRef = null;\n\n// This is a hacky DOM-based implementation of a FocusScope until this RFC lands in React:\n// https://github.com/reactjs/rfcs/pull/109\n\n/**\n * A FocusScope manages focus for its descendants. It supports containing focus inside\n * the scope, restoring focus to the previously focused element on unmount, and auto\n * focusing children on mount. It also acts as a container for a programmatic focus\n * management interface that can be used to move focus forward and back in response\n * to user events.\n */\nexport function FocusScope(props: FocusScopeProps): JSX.Element {\n  let {children, contain, restoreFocus, autoFocus} = props;\n  let startRef = useRef<HTMLSpanElement>(null);\n  let endRef = useRef<HTMLSpanElement>(null);\n  let scopeRef = useRef<Element[]>([]);\n  let {parentNode} = useContext(FocusContext) || {};\n\n  // Create a tree node here so we can add children to it even before it is added to the tree.\n  let node = useMemo(() => new TreeNode({scopeRef}), [scopeRef]);\n\n  useLayoutEffect(() => {\n    // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n    // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n    // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n    // that is being added should get the activeScope as its parent.\n    let parent = parentNode || focusScopeTree.root;\n    if (focusScopeTree.getTreeNode(parent.scopeRef) && activeScope && !isAncestorScope(activeScope, parent.scopeRef)) {\n      let activeNode = focusScopeTree.getTreeNode(activeScope);\n      if (activeNode) {\n        parent = activeNode;\n      }\n    }\n\n    // Add the node to the parent, and to the tree.\n    parent.addChild(node);\n    focusScopeTree.addNode(node);\n  }, [node, parentNode]);\n\n  useLayoutEffect(() => {\n    let node = focusScopeTree.getTreeNode(scopeRef);\n    if (node) {\n      node.contain = !!contain;\n    }\n  }, [contain]);\n\n  useLayoutEffect(() => {\n    // Find all rendered nodes between the sentinels and add them to the scope.\n    let node = startRef.current?.nextSibling!;\n    let nodes: Element[] = [];\n    let stopPropagation = e => e.stopPropagation();\n    while (node && node !== endRef.current) {\n      nodes.push(node as Element);\n      // Stop custom restore focus event from propagating to parent focus scopes.\n      node.addEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      node = node.nextSibling as Element;\n    }\n\n    scopeRef.current = nodes;\n\n    return () => {\n      for (let node of nodes) {\n        node.removeEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      }\n    };\n  }, [children]);\n\n  useActiveScopeTracker(scopeRef, restoreFocus, contain);\n  useFocusContainment(scopeRef, contain);\n  useRestoreFocus(scopeRef, restoreFocus, contain);\n  useAutoFocus(scopeRef, autoFocus);\n\n  // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n  // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n  useEffect(() => {\n    const activeElement = getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined));\n    let scope: TreeNode | null = null;\n\n    if (isElementInScope(activeElement, scopeRef.current)) {\n      // We need to traverse the focusScope tree and find the bottom most scope that\n      // contains the active element and set that as the activeScope.\n      for (let node of focusScopeTree.traverse()) {\n        if (node.scopeRef && isElementInScope(activeElement, node.scopeRef.current)) {\n          scope = node;\n        }\n      }\n\n      if (scope === focusScopeTree.getTreeNode(scopeRef)) {\n        activeScope = scope.scopeRef;\n      }\n    }\n  }, [scopeRef]);\n\n  // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n  // in useRestoreFocus cleanup runs.\n  useLayoutEffect(() => {\n    return () => {\n      // Scope may have been re-parented.\n      let parentScope = focusScopeTree.getTreeNode(scopeRef)?.parent?.scopeRef ?? null;\n\n      if (\n        (scopeRef === activeScope || isAncestorScope(scopeRef, activeScope)) &&\n        (!parentScope || focusScopeTree.getTreeNode(parentScope))\n      ) {\n        activeScope = parentScope;\n      }\n      focusScopeTree.removeTreeNode(scopeRef);\n    };\n  }, [scopeRef]);\n\n  let focusManager = useMemo(() => createFocusManagerForScope(scopeRef), []);\n  let value = useMemo(() => ({\n    focusManager,\n    parentNode: node\n  }), [node, focusManager]);\n\n  return (\n    <FocusContext.Provider value={value}>\n      <span data-focus-scope-start hidden ref={startRef} />\n      {children}\n      <span data-focus-scope-end hidden ref={endRef} />\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Returns a FocusManager interface for the parent FocusScope.\n * A FocusManager can be used to programmatically move focus within\n * a FocusScope, e.g. in response to user events like keyboard navigation.\n */\nexport function useFocusManager(): FocusManager | undefined {\n  return useContext(FocusContext)?.focusManager;\n}\n\nfunction createFocusManagerForScope(scopeRef: React.RefObject<Element[] | null>): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[0].previousElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node : sentinel;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[scope.length - 1].nextElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node  : sentinel;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = sentinel;\n        previousNode = walker.previousNode() as FocusableElement;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    },\n    focusFirst(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[0].previousElementSibling!;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[scope.length - 1].nextElementSibling!;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    }\n  };\n}\n\nfunction getScopeRoot(scope: Element[]) {\n  return scope[0].parentElement!;\n}\n\nfunction shouldContainFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.contain) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return true;\n}\n\nfunction isTabbableRadio(element: HTMLInputElement) {\n  if (element.checked) {\n    return true;\n  }\n  let radios: HTMLInputElement[] = [];\n  if (!element.form) {\n    radios = ([...getOwnerDocument(element).querySelectorAll(`input[type=\"radio\"][name=\"${CSS.escape(element.name)}\"]`)] as HTMLInputElement[]).filter(radio => !radio.form);\n  } else {\n    let radioList = element.form?.elements?.namedItem(element.name) as RadioNodeList;\n    radios = [...(radioList ?? [])] as HTMLInputElement[];\n  }\n  if (!radios) {\n    return false;\n  }\n  let anyChecked = radios.some(radio => radio.checked);\n\n  return !anyChecked;\n}\n\nfunction useFocusContainment(scopeRef: RefObject<Element[] | null>, contain?: boolean) {\n  let focusedNode = useRef<FocusableElement>(undefined);\n\n  let raf = useRef<ReturnType<typeof requestAnimationFrame>>(undefined);\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    if (!contain) {\n      // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n        raf.current = undefined;\n      }\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    // Handle the Tab key to contain focus within the scope\n    let onKeyDown = (e) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = getActiveElement(ownerDocument);\n      let scope = scopeRef.current;\n      if (!scope || !isElementInScope(focusedElement, scope)) {\n        return;\n      }\n\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable: true}, scope);\n      if (!focusedElement) {\n        return;\n      }\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      if (!nextElement) {\n        walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling! : scope[0].previousElementSibling!;\n        nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      }\n\n      e.preventDefault();\n      if (nextElement) {\n        focusElement(nextElement, true);\n      }\n    };\n\n    let onFocus: EventListener = (e) => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) && isElementInScope(getEventTarget(e) as Element, scopeRef.current)) {\n        activeScope = scopeRef;\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      } else if (shouldContainFocus(scopeRef) && !isElementInChildScope(getEventTarget(e) as Element, scopeRef)) {\n        // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n        // restore focus to the previously focused node or the first tabbable element in the active scope.\n        if (focusedNode.current) {\n          focusedNode.current.focus();\n        } else if (activeScope && activeScope.current) {\n          focusFirstInScope(activeScope.current);\n        }\n      } else if (shouldContainFocus(scopeRef)) {\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      }\n    };\n\n    let onBlur: EventListener = (e) => {\n      // Firefox doesn't shift focus back to the Dialog properly without this\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n      raf.current = requestAnimationFrame(() => {\n        // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n        // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n        // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n        let modality = getInteractionModality();\n        let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && isAndroid() && isChrome();\n\n        // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n        let activeElement = getActiveElement(ownerDocument);\n        if (!shouldSkipFocusRestore && activeElement && shouldContainFocus(scopeRef) && !isElementInChildScope(activeElement, scopeRef)) {\n          activeScope = scopeRef;\n          let target = getEventTarget(e) as FocusableElement;\n          if (target && target.isConnected) {\n            focusedNode.current = target;\n            focusedNode.current?.focus();\n          } else if (activeScope.current) {\n            focusFirstInScope(activeScope.current);\n          }\n        }\n      });\n    };\n\n    ownerDocument.addEventListener('keydown', onKeyDown, false);\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    scope?.forEach(element => element.addEventListener('focusout', onBlur, false));\n    return () => {\n      ownerDocument.removeEventListener('keydown', onKeyDown, false);\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n      scope?.forEach(element => element.removeEventListener('focusout', onBlur, false));\n    };\n  }, [scopeRef, contain]);\n\n  // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n\n  useLayoutEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, [raf]);\n}\n\nfunction isElementInAnyScope(element: Element) {\n  return isElementInChildScope(element);\n}\n\nfunction isElementInScope(element?: Element | null, scope?: Element[] | null) {\n  if (!element) {\n    return false;\n  }\n  if (!scope) {\n    return false;\n  }\n  return scope.some(node => node.contains(element));\n}\n\nfunction isElementInChildScope(element: Element, scope: ScopeRef = null) {\n  // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n  if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) {\n    return true;\n  }\n\n  // node.contains in isElementInScope covers child scopes that are also DOM children,\n  // but does not cover child scopes in portals.\n  for (let {scopeRef: s} of focusScopeTree.traverse(focusScopeTree.getTreeNode(scope))) {\n    if (s && isElementInScope(element, s.current)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/** @private */\nexport function isElementInChildOfActiveScope(element: Element): boolean {\n  return isElementInChildScope(element, activeScope);\n}\n\nfunction isAncestorScope(ancestor: ScopeRef, scope: ScopeRef) {\n  let parent = focusScopeTree.getTreeNode(scope)?.parent;\n  while (parent) {\n    if (parent.scopeRef === ancestor) {\n      return true;\n    }\n    parent = parent.parent;\n  }\n  return false;\n}\n\nfunction focusElement(element: FocusableElement | null, scroll = false) {\n  if (element != null && !scroll) {\n    try {\n      focusSafely(element);\n    } catch {\n      // ignore\n    }\n  } else if (element != null) {\n    try {\n      element.focus();\n    } catch {\n      // ignore\n    }\n  }\n}\n\nfunction getFirstInScope(scope: Element[], tabbable = true) {\n  let sentinel = scope[0].previousElementSibling!;\n  let scopeRoot = getScopeRoot(scope);\n  let walker = getFocusableTreeWalker(scopeRoot, {tabbable}, scope);\n  walker.currentNode = sentinel;\n  let nextNode = walker.nextNode();\n\n  // If the scope does not contain a tabbable element, use the first focusable element.\n  if (tabbable && !nextNode) {\n    scopeRoot = getScopeRoot(scope);\n    walker = getFocusableTreeWalker(scopeRoot, {tabbable: false}, scope);\n    walker.currentNode = sentinel;\n    nextNode = walker.nextNode();\n  }\n\n  return nextNode as FocusableElement;\n}\n\nfunction focusFirstInScope(scope: Element[], tabbable:boolean = true) {\n  focusElement(getFirstInScope(scope, tabbable));\n}\n\nfunction useAutoFocus(scopeRef: RefObject<Element[] | null>, autoFocus?: boolean) {\n  const autoFocusRef = React.useRef(autoFocus);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      activeScope = scopeRef;\n      const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n      if (!isElementInScope(getActiveElement(ownerDocument), activeScope.current) && scopeRef.current) {\n        focusFirstInScope(scopeRef.current);\n      }\n    }\n    autoFocusRef.current = false;\n  }, [scopeRef]);\n}\n\nfunction useActiveScopeTracker(scopeRef: RefObject<Element[] | null>, restore?: boolean, contain?: boolean) {\n  // tracks the active scope, in case restore and contain are both false.\n  // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n  useLayoutEffect(() => {\n    if (restore || contain) {\n      return;\n    }\n\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    let onFocus = (e) => {\n      let target = getEventTarget(e) as Element;\n      if (isElementInScope(target, scopeRef.current)) {\n        activeScope = scopeRef;\n      } else if (!isElementInAnyScope(target)) {\n        activeScope = null;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n  }, [scopeRef, restore, contain]);\n}\n\nfunction shouldRestoreFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.nodeToRestore) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return scope?.scopeRef === scopeRef;\n}\n\nfunction useRestoreFocus(scopeRef: RefObject<Element[] | null>, restoreFocus?: boolean, contain?: boolean) {\n  // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n  // eslint-disable-next-line no-restricted-globals\n  const nodeToRestoreRef = useRef(typeof document !== 'undefined' ? getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined)) as FocusableElement : null);\n\n  // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n  // restoring-non-containing scopes should only care if they become active so they can perform the restore\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n    if (!restoreFocus || contain) {\n      return;\n    }\n\n    let onFocus = () => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) &&\n        isElementInScope(getActiveElement(ownerDocument), scopeRef.current)\n      ) {\n        activeScope = scopeRef;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [scopeRef, contain]);\n\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    // Handle the Tab key so that tabbing out of the scope goes to the next element\n    // after the node that had focus when the scope mounted. This is important when\n    // using portals for overlays, so that focus goes to the expected element when\n    // tabbing out of the overlay.\n    let onKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = ownerDocument.activeElement as FocusableElement;\n      if (!isElementInChildScope(focusedElement, scopeRef) || !shouldRestoreFocus(scopeRef)) {\n        return;\n      }\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // Create a DOM tree walker that matches all tabbable elements\n      let walker = getFocusableTreeWalker(ownerDocument.body, {tabbable: true});\n\n      // Find the next tabbable element after the currently focused element\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n\n      if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n        nodeToRestore = undefined;\n        treeNode.nodeToRestore = undefined;\n      }\n\n      // If there is no next element, or it is outside the current scope, move focus to the\n      // next element after the node to restore to instead.\n      if ((!nextElement || !isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n        walker.currentNode = nodeToRestore;\n\n        // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n        do {\n          nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n        } while (isElementInChildScope(nextElement, scopeRef));\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (nextElement) {\n          focusElement(nextElement, true);\n        } else {\n          // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n          // then move focus to the body.\n          // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n          if (!isElementInAnyScope(nodeToRestore)) {\n            focusedElement.blur();\n          } else {\n            focusElement(nodeToRestore, true);\n          }\n        }\n      }\n    };\n\n    if (!contain) {\n      ownerDocument.addEventListener('keydown', onKeyDown as EventListener, true);\n    }\n\n    return () => {\n      if (!contain) {\n        ownerDocument.removeEventListener('keydown', onKeyDown as EventListener, true);\n      }\n    };\n  }, [scopeRef, restoreFocus, contain]);\n\n  // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    let treeNode = focusScopeTree.getTreeNode(scopeRef);\n    if (!treeNode) {\n      return;\n    }\n    treeNode.nodeToRestore = nodeToRestoreRef.current ?? undefined;\n    return () => {\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n      let activeElement = getActiveElement(ownerDocument);\n      if (\n        restoreFocus\n        && nodeToRestore\n        && (\n          ((activeElement && isElementInChildScope(activeElement, scopeRef)) || (activeElement === ownerDocument.body && shouldRestoreFocus(scopeRef)))\n        )\n      ) {\n        // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n        let clonedTree = focusScopeTree.clone();\n        requestAnimationFrame(() => {\n          // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n          if (ownerDocument.activeElement === ownerDocument.body) {\n            // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n            let treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                restoreFocusToElement(treeNode.nodeToRestore);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n\n            // If no nodeToRestore was found, focus the first element in the nearest\n            // ancestor scope that is still in the tree.\n            treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.scopeRef && treeNode.scopeRef.current && focusScopeTree.getTreeNode(treeNode.scopeRef)) {\n                let node = getFirstInScope(treeNode.scopeRef.current, true);\n                restoreFocusToElement(node);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n          }\n        });\n      }\n    };\n  }, [scopeRef, restoreFocus]);\n}\n\nfunction restoreFocusToElement(node: FocusableElement) {\n  // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n  // For example, virtualized collection components reuse DOM elements, so the original element\n  // might still exist in the DOM but representing a different item.\n  if (node.dispatchEvent(new CustomEvent(RESTORE_FOCUS_EVENT, {bubbles: true, cancelable: true}))) {\n    focusElement(node);\n  }\n}\n\n/**\n * Create a [TreeWalker]{@link https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker}\n * that matches all focusable/tabbable elements.\n */\nexport function getFocusableTreeWalker(root: Element, opts?: FocusManagerOptions, scope?: Element[]): ShadowTreeWalker | TreeWalker {\n  let filter = opts?.tabbable ? isTabbable : isFocusable;\n\n  // Ensure that root is an Element or fall back appropriately\n  let rootElement = root?.nodeType === Node.ELEMENT_NODE ? (root as Element) : null;\n\n  // Determine the document to use\n  let doc = getOwnerDocument(rootElement);\n\n  // Create a TreeWalker, ensuring the root is an Element or Document\n  let walker = createShadowTreeWalker(\n    doc,\n    root || doc,\n    NodeFilter.SHOW_ELEMENT,\n    {\n      acceptNode(node) {\n        // Skip nodes inside the starting node.\n        if (opts?.from?.contains(node)) {\n          return NodeFilter.FILTER_REJECT;\n        }\n\n        if (opts?.tabbable\n          && (node as Element).tagName === 'INPUT'\n          && (node as HTMLInputElement).getAttribute('type') === 'radio') {\n          // If the radio is in a form, we can get all the other radios by name\n          if (!isTabbableRadio(node as HTMLInputElement)) {\n            return NodeFilter.FILTER_REJECT;\n          }\n          // If the radio is in the same group as the current node and none are selected, we can skip it\n          if ((walker.currentNode as Element).tagName === 'INPUT'\n            && (walker.currentNode as HTMLInputElement).type === 'radio'\n            && (walker.currentNode as HTMLInputElement).name === (node as HTMLInputElement).name) {\n            return NodeFilter.FILTER_REJECT;\n          }\n        }\n\n        if (filter(node as Element)\n          && (!scope || isElementInScope(node as Element, scope))\n          && (!opts?.accept || opts.accept(node as Element))\n        ) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n\n        return NodeFilter.FILTER_SKIP;\n      }\n    }\n  );\n\n  if (opts?.from) {\n    walker.currentNode = opts.from;\n  }\n\n  return walker;\n}\n\n/**\n * Creates a FocusManager object that can be used to move focus within an element.\n */\nexport function createFocusManager(ref: RefObject<Element | null>, defaultOptions: FocusManagerOptions = {}): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      }\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = root;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      } else {\n        let next = last(walker);\n        if (next) {\n          focusElement(next, true);\n        }\n        return next ?? null;\n      }\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = root;\n        let lastNode = last(walker);\n        if (!lastNode) {\n          // couldn't wrap\n          return null;\n        }\n        previousNode = lastNode;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode ?? null;\n    },\n    focusFirst(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let next = last(walker);\n      if (next) {\n        focusElement(next, true);\n      }\n      return next ?? null;\n    }\n  };\n}\n\nfunction last(walker: ShadowTreeWalker | TreeWalker) {\n  let next: FocusableElement | undefined = undefined;\n  let last: FocusableElement;\n  do {\n    last = walker.lastChild() as FocusableElement;\n    if (last) {\n      next = last;\n    }\n  } while (last);\n  return next;\n}\n\n\nclass Tree {\n  root: TreeNode;\n  private fastMap = new Map<ScopeRef, TreeNode>();\n\n  constructor() {\n    this.root = new TreeNode({scopeRef: null});\n    this.fastMap.set(null, this.root);\n  }\n\n  get size(): number {\n    return this.fastMap.size;\n  }\n\n  getTreeNode(data: ScopeRef): TreeNode | undefined {\n    return this.fastMap.get(data);\n  }\n\n  addTreeNode(scopeRef: ScopeRef, parent: ScopeRef, nodeToRestore?: FocusableElement): void {\n    let parentNode = this.fastMap.get(parent ?? null);\n    if (!parentNode) {\n      return;\n    }\n    let node = new TreeNode({scopeRef});\n    parentNode.addChild(node);\n    node.parent = parentNode;\n    this.fastMap.set(scopeRef, node);\n    if (nodeToRestore) {\n      node.nodeToRestore = nodeToRestore;\n    }\n  }\n\n  addNode(node: TreeNode): void {\n    this.fastMap.set(node.scopeRef, node);\n  }\n\n  removeTreeNode(scopeRef: ScopeRef): void {\n    // never remove the root\n    if (scopeRef === null) {\n      return;\n    }\n    let node = this.fastMap.get(scopeRef);\n    if (!node) {\n      return;\n    }\n    let parentNode = node.parent;\n    // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n    // if we are, then replace the siblings restore with the restore from the scope we're removing\n    for (let current of this.traverse()) {\n      if (\n        current !== node &&\n        node.nodeToRestore &&\n        current.nodeToRestore &&\n        node.scopeRef &&\n        node.scopeRef.current &&\n        isElementInScope(current.nodeToRestore, node.scopeRef.current)\n      ) {\n        current.nodeToRestore = node.nodeToRestore;\n      }\n    }\n    let children = node.children;\n    if (parentNode) {\n      parentNode.removeChild(node);\n      if (children.size > 0) {\n        children.forEach(child => parentNode && parentNode.addChild(child));\n      }\n    }\n\n    this.fastMap.delete(node.scopeRef);\n  }\n\n  // Pre Order Depth First\n  *traverse(node: TreeNode = this.root): Generator<TreeNode> {\n    if (node.scopeRef != null) {\n      yield node;\n    }\n    if (node.children.size > 0) {\n      for (let child of node.children) {\n        yield* this.traverse(child);\n      }\n    }\n  }\n\n  clone(): Tree {\n    let newTree = new Tree();\n    for (let node of this.traverse()) {\n      newTree.addTreeNode(node.scopeRef, node.parent?.scopeRef ?? null, node.nodeToRestore);\n    }\n    return newTree;\n  }\n}\n\nclass TreeNode {\n  public scopeRef: ScopeRef;\n  public nodeToRestore?: FocusableElement;\n  public parent?: TreeNode;\n  public children: Set<TreeNode> = new Set();\n  public contain = false;\n\n  constructor(props: {scopeRef: ScopeRef}) {\n    this.scopeRef = props.scopeRef;\n  }\n  addChild(node: TreeNode): void {\n    this.children.add(node);\n    node.parent = this;\n  }\n  removeChild(node: TreeNode): void {\n    this.children.delete(node);\n    node.parent = undefined;\n  }\n}\n\nexport let focusScopeTree: Tree = new Tree();\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAkED,MAAM,qCAAA,WAAA,GAAe,CAAA,iKAAA,UAAI,EAAE,aAAa,CAAuB;AAC/D,MAAM,4CAAsB;AAE5B,IAAI,oCAAwB;AAYrB,SAAS,0CAAW,KAAsB;IAC/C,IAAI,EAAA,UAAC,QAAQ,EAAA,SAAE,OAAO,EAAA,cAAE,YAAY,EAAA,WAAE,SAAS,EAAC,GAAG;IACnD,IAAI,WAAW,CAAA,iKAAA,SAAK,EAAmB;IACvC,IAAI,SAAS,CAAA,iKAAA,SAAK,EAAmB;IACrC,IAAI,WAAW,CAAA,iKAAA,SAAK,EAAa,EAAE;IACnC,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,iKAAA,aAAS,EAAE,uCAAiB,CAAC;IAEhD,4FAA4F;IAC5F,IAAI,OAAO,CAAA,iKAAA,UAAM,EAAE,IAAM,IAAI,+BAAS;sBAAC;QAAQ,IAAI;QAAC;KAAS;IAE7D,CAAA,2KAAA,kBAAc,EAAE;QACd,+FAA+F;QAC/F,mGAAmG;QACnG,mGAAmG;QACnG,gEAAgE;QAChE,IAAI,SAAS,cAAc,0CAAe,IAAI;QAC9C,IAAI,0CAAe,WAAW,CAAC,OAAO,QAAQ,KAAK,qCAAe,CAAC,sCAAgB,mCAAa,OAAO,QAAQ,GAAG;YAChH,IAAI,aAAa,0CAAe,WAAW,CAAC;YAC5C,IAAI,YACF,SAAS;QAEb;QAEA,+CAA+C;QAC/C,OAAO,QAAQ,CAAC;QAChB,0CAAe,OAAO,CAAC;IACzB,GAAG;QAAC;QAAM;KAAW;IAErB,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,OAAO,0CAAe,WAAW,CAAC;QACtC,IAAI,MACF,KAAK,OAAO,GAAG,CAAC,CAAC;IAErB,GAAG;QAAC;KAAQ;IAEZ,CAAA,2KAAA,kBAAc,EAAE;YAEH;QADX,2EAA2E;QAC3E,IAAI,OAAA,CAAO,oBAAA,SAAS,OAAO,MAAA,QAAhB,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAkB,WAAW;QACxC,IAAI,QAAmB,EAAE;QACzB,IAAI,kBAAkB,CAAA,IAAK,EAAE,eAAe;QAC5C,MAAO,QAAQ,SAAS,OAAO,OAAO,CAAE;YACtC,MAAM,IAAI,CAAC;YACX,2EAA2E;YAC3E,KAAK,gBAAgB,CAAC,2CAAqB;YAC3C,OAAO,KAAK,WAAW;QACzB;QAEA,SAAS,OAAO,GAAG;QAEnB,OAAO;YACL,KAAK,IAAI,QAAQ,MACf,KAAK,mBAAmB,CAAC,2CAAqB;QAElD;IACF,GAAG;QAAC;KAAS;IAEb,4CAAsB,UAAU,cAAc;IAC9C,0CAAoB,UAAU;IAC9B,sCAAgB,UAAU,cAAc;IACxC,mCAAa,UAAU;IAEvB,mGAAmG;IACnG,uGAAuG;IACvG,CAAA,iKAAA,YAAQ,EAAE;QACR,MAAM,gBAAgB,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QACjG,IAAI,QAAyB;QAE7B,IAAI,uCAAiB,eAAe,SAAS,OAAO,GAAG;YACrD,8EAA8E;YAC9E,+DAA+D;YAC/D,KAAK,IAAI,QAAQ,0CAAe,QAAQ,GACtC,IAAI,KAAK,QAAQ,IAAI,uCAAiB,eAAe,KAAK,QAAQ,CAAC,OAAO,GACxE,QAAQ;YAIZ,IAAI,UAAU,0CAAe,WAAW,CAAC,WACvC,oCAAc,MAAM,QAAQ;QAEhC;IACF,GAAG;QAAC;KAAS;IAEb,yGAAyG;IACzG,mCAAmC;IACnC,CAAA,2KAAA,kBAAc,EAAE;QACd,OAAO;gBAEa,oCAAA;gBAAA;YADlB,mCAAmC;YACnC,IAAI,cAAc,CAAA,8CAAA,CAAA,8BAAA,0CAAe,WAAW,CAAC,SAAA,MAAA,QAA3B,gCAAA,KAAA,IAAA,KAAA,IAAA,CAAA,qCAAA,4BAAsC,MAAM,MAAA,QAA5C,uCAAA,KAAA,IAAA,KAAA,IAAA,mCAA8C,QAAQ,MAAA,QAAtD,gDAAA,KAAA,IAAA,8CAA0D;YAE5E,IACG,CAAA,aAAa,qCAAe,sCAAgB,UAAU,kCAAW,KACjE,CAAA,CAAC,eAAe,0CAAe,WAAW,CAAC,YAAW,GAEvD,oCAAc;YAEhB,0CAAe,cAAc,CAAC;QAChC;IACF,GAAG;QAAC;KAAS;IAEb,IAAI,eAAe,CAAA,iKAAA,UAAM,EAAE,IAAM,iDAA2B,WAAW,EAAE;IACzE,IAAI,QAAQ,CAAA,iKAAA,UAAM,EAAE,IAAO,CAAA;0BACzB;YACA,YAAY;QACd,CAAA,GAAI;QAAC;QAAM;KAAa;IAExB,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,mCAAa,QAAQ,EAAA;QAAC,OAAO;qBAC5B,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,QAAA;QAAK,0BAAA;QAAuB,QAAA;QAAO,KAAK;QACxC,UAAA,WAAA,GACD,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,QAAA;QAAK,wBAAA;QAAqB,QAAA;QAAO,KAAK;;AAG7C;AAOO,SAAS;QACP;IAAP,OAAA,CAAO,cAAA,CAAA,iKAAA,aAAS,EAAE,mCAAA,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,YAA0B,YAAY;AAC/C;AAEA,SAAS,iDAA2B,QAA2C;IAC7E,OAAO;QACL,WAAU,OAA4B,CAAC,CAAC;YACtC,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,QAAQ,EAAA,MAAE,IAAI,EAAA,QAAE,MAAM,EAAC,GAAG;gBACgB;YAArD,IAAI,OAAO,QAAQ,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,CAAA,UAAA,KAAK,CAAC,EAAE,MAAA,QAAR,YAAA,KAAA,IAAA,UAAY;YACjE,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,sBAAsB;YAC9C,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,uCAAiB,MAAM,SAAS,OAAO;YAC5D,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,CAAC,YAAY,MAAM;gBACrB,OAAO,WAAW,GAAG;gBACrB,WAAW,OAAO,QAAQ;YAC5B;YACA,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,eAAc,OAA4B,CAAC,CAAC;YAC1C,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,QAAQ,EAAA,MAAE,IAAI,EAAA,QAAE,MAAM,EAAC,GAAG;gBACgB;YAArD,IAAI,OAAO,QAAQ,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,CAAA,UAAA,KAAK,CAAC,EAAE,MAAA,QAAR,YAAA,KAAA,IAAA,UAAY;YACjE,IAAI,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB;YACzD,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,uCAAiB,MAAM,SAAS,OAAQ;YAC7D,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,CAAC,gBAAgB,MAAM;gBACzB,OAAO,WAAW,GAAG;gBACrB,eAAe,OAAO,YAAY;YACpC;YACA,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO;QACT;QACA,YAAW,OAAO,CAAC,CAAC;YAClB,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,UAAC,QAAQ,EAAA,QAAE,MAAM,EAAC,GAAG;YACzB,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,sBAAsB;YACpD,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,WAAU,OAAO,CAAC,CAAC;YACjB,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,UAAC,QAAQ,EAAA,QAAE,MAAM,EAAC,GAAG;YACzB,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB;YAC/D,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO;QACT;IACF;AACF;AAEA,SAAS,mCAAa,KAAgB;IACpC,OAAO,KAAK,CAAC,EAAE,CAAC,aAAa;AAC/B;AAEA,SAAS,yCAAmB,QAAkB;IAC5C,IAAI,QAAQ,0CAAe,WAAW,CAAC;IACvC,MAAO,SAAS,MAAM,QAAQ,KAAK,SAAU;QAC3C,IAAI,MAAM,OAAO,EACf,OAAO;QAGT,QAAQ,MAAM,MAAM;IACtB;IAEA,OAAO;AACT;AAEA,SAAS,sCAAgB,OAAyB;IAChD,IAAI,QAAQ,OAAO,EACjB,OAAO;IAET,IAAI,SAA6B,EAAE;IACnC,IAAI,CAAC,QAAQ,IAAI,EACf,SAAU;WAAI,CAAA,sKAAA,mBAAe,EAAE,SAAS,gBAAgB,CAAC,CAAC,0BAA0B,EAAE,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;KAAE,CAAwB,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,IAAI;SAClK;YACW,wBAAA;QAAhB,IAAI,YAAA,CAAY,gBAAA,QAAQ,IAAI,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,CAAA,yBAAA,cAAc,QAAQ,MAAA,QAAtB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAwB,SAAS,CAAC,QAAQ,IAAI;QAC9D,SAAS;eAAK,cAAA,QAAA,cAAA,KAAA,IAAA,YAAa,EAAE;SAAE;IACjC;IACA,IAAI,CAAC,QACH,OAAO;IAET,IAAI,aAAa,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,OAAO;IAEnD,OAAO,CAAC;AACV;AAEA,SAAS,0CAAoB,QAAqC,EAAE,OAAiB;IACnF,IAAI,cAAc,CAAA,iKAAA,SAAK,EAAoB;IAE3C,IAAI,MAAM,CAAA,iKAAA,SAAK,EAA4C;IAC3D,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,QAAQ,SAAS,OAAO;QAC5B,IAAI,CAAC,SAAS;YACZ,sGAAsG;YACtG,IAAI,IAAI,OAAO,EAAE;gBACf,qBAAqB,IAAI,OAAO;gBAChC,IAAI,OAAO,GAAG;YAChB;YACA;QACF;QAEA,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAE1D,uDAAuD;QACvD,IAAI,YAAY,CAAC;YACf,IAAI,EAAE,GAAG,KAAK,SAAS,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,CAAC,yCAAmB,aAAa,EAAE,WAAW,EACzG;YAGF,IAAI,iBAAiB,CAAA,wKAAA,mBAAe,EAAE;YACtC,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,CAAC,SAAS,CAAC,uCAAiB,gBAAgB,QAC9C;YAGF,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;gBAAC,UAAU;YAAI,GAAG;YACjE,IAAI,CAAC,gBACH;YAEF,OAAO,WAAW,GAAG;YACrB,IAAI,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YACvE,IAAI,CAAC,aAAa;gBAChB,OAAO,WAAW,GAAG,EAAE,QAAQ,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB,GAAI,KAAK,CAAC,EAAE,CAAC,sBAAsB;gBAC/G,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YACrE;YAEA,EAAE,cAAc;YAChB,IAAI,aACF,mCAAa,aAAa;QAE9B;QAEA,IAAI,UAAyB,CAAC;YAC5B,mGAAmG;YACnG,gEAAgE;YAChE,IAAK,CAAA,CAAC,qCAAe,sCAAgB,mCAAa,SAAQ,KAAM,uCAAiB,CAAA,wKAAA,iBAAa,EAAE,IAAe,SAAS,OAAO,GAAG;gBAChI,oCAAc;gBACd,YAAY,OAAO,GAAG,CAAA,wKAAA,iBAAa,EAAE;YACvC,OAAO,IAAI,yCAAmB,aAAa,CAAC,4CAAsB,CAAA,wKAAA,iBAAa,EAAE,IAAe,WAAW;gBACzG,+FAA+F;gBAC/F,kGAAkG;gBAClG,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC,KAAK;qBACpB,IAAI,qCAAe,kCAAY,OAAO,EAC3C,wCAAkB,kCAAY,OAAO;YAEzC,OAAO,IAAI,yCAAmB,WAC5B,YAAY,OAAO,GAAG,CAAA,wKAAA,iBAAa,EAAE;QAEzC;QAEA,IAAI,SAAwB,CAAC;YAC3B,uEAAuE;YACvE,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;YAElC,IAAI,OAAO,GAAG,sBAAsB;gBAClC,iHAAiH;gBACjH,kHAAkH;gBAClH,mHAAmH;gBACnH,IAAI,WAAW,CAAA,kLAAA,yBAAqB;gBACpC,IAAI,yBAA0B,CAAA,aAAa,aAAa,aAAa,IAAG,KAAM,CAAA,oKAAA,YAAQ,OAAO,CAAA,oKAAA,WAAO;gBAEpG,mGAAmG;gBACnG,IAAI,gBAAgB,CAAA,wKAAA,mBAAe,EAAE;gBACrC,IAAI,CAAC,0BAA0B,iBAAiB,yCAAmB,aAAa,CAAC,4CAAsB,eAAe,WAAW;oBAC/H,oCAAc;oBACd,IAAI,SAAS,CAAA,wKAAA,iBAAa,EAAE;oBAC5B,IAAI,UAAU,OAAO,WAAW,EAAE;4BAEhC;wBADA,YAAY,OAAO,GAAG;yBACtB,uBAAA,YAAY,OAAO,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAqB,KAAK;oBAC5B,OAAO,IAAI,kCAAY,OAAO,EAC5B,wCAAkB,kCAAY,OAAO;gBAEzC;YACF;QACF;QAEA,cAAc,gBAAgB,CAAC,WAAW,WAAW;QACrD,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,YAAY,QAAQ;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,WAAW;YACxD,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;YAC1E,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,YAAY,QAAQ;QAC5E;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,uFAAuF;IAEvF,CAAA,2KAAA,kBAAc,EAAE;QACd,OAAO;YACL,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;QAEpC;IACF,GAAG;QAAC;KAAI;AACV;AAEA,SAAS,0CAAoB,OAAgB;IAC3C,OAAO,4CAAsB;AAC/B;AAEA,SAAS,uCAAiB,OAAwB,EAAE,KAAwB;IAC1E,IAAI,CAAC,SACH,OAAO;IAET,IAAI,CAAC,OACH,OAAO;IAET,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;AAC1C;AAEA,SAAS,4CAAsB,OAAgB,EAAE,QAAkB,IAAI;IACrE,+FAA+F;IAC/F,IAAI,mBAAmB,WAAW,QAAQ,OAAO,CAAC,gCAChD,OAAO;IAGT,oFAAoF;IACpF,8CAA8C;IAC9C,KAAK,IAAI,EAAC,UAAU,CAAC,EAAC,IAAI,0CAAe,QAAQ,CAAC,0CAAe,WAAW,CAAC,QAAS;QACpF,IAAI,KAAK,uCAAiB,SAAS,EAAE,OAAO,GAC1C,OAAO;IAEX;IAEA,OAAO;AACT;AAGO,SAAS,0CAA8B,OAAgB;IAC5D,OAAO,4CAAsB,SAAS;AACxC;AAEA,SAAS,sCAAgB,QAAkB,EAAE,KAAe;QAC7C;IAAb,IAAI,SAAA,CAAS,8BAAA,0CAAe,WAAW,CAAC,MAAA,MAAA,QAA3B,gCAAA,KAAA,IAAA,KAAA,IAAA,4BAAmC,MAAM;IACtD,MAAO,OAAQ;QACb,IAAI,OAAO,QAAQ,KAAK,UACtB,OAAO;QAET,SAAS,OAAO,MAAM;IACxB;IACA,OAAO;AACT;AAEA,SAAS,mCAAa,OAAgC,EAAE,SAAS,KAAK;IACpE,IAAI,WAAW,QAAQ,CAAC,QACtB,IAAI;QACF,CAAA,8KAAA,cAAU,EAAE;IACd,EAAE,OAAM;IACN,SAAS;IACX;SACK,IAAI,WAAW,MACpB,IAAI;QACF,QAAQ,KAAK;IACf,EAAE,OAAM;IACN,SAAS;IACX;AAEJ;AAEA,SAAS,sCAAgB,KAAgB,EAAE,WAAW,IAAI;IACxD,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,sBAAsB;IAC9C,IAAI,YAAY,mCAAa;IAC7B,IAAI,SAAS,0CAAuB,WAAW;kBAAC;IAAQ,GAAG;IAC3D,OAAO,WAAW,GAAG;IACrB,IAAI,WAAW,OAAO,QAAQ;IAE9B,qFAAqF;IACrF,IAAI,YAAY,CAAC,UAAU;QACzB,YAAY,mCAAa;QACzB,SAAS,0CAAuB,WAAW;YAAC,UAAU;QAAK,GAAG;QAC9D,OAAO,WAAW,GAAG;QACrB,WAAW,OAAO,QAAQ;IAC5B;IAEA,OAAO;AACT;AAEA,SAAS,wCAAkB,KAAgB,EAAE,WAAmB,IAAI;IAClE,mCAAa,sCAAgB,OAAO;AACtC;AAEA,SAAS,mCAAa,QAAqC,EAAE,SAAmB;IAC9E,MAAM,eAAe,CAAA,iKAAA,UAAI,EAAE,MAAM,CAAC;IAClC,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,oCAAc;YACd,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;YAChF,IAAI,CAAC,uCAAiB,CAAA,wKAAA,mBAAe,EAAE,gBAAgB,kCAAY,OAAO,KAAK,SAAS,OAAO,EAC7F,wCAAkB,SAAS,OAAO;QAEtC;QACA,aAAa,OAAO,GAAG;IACzB,GAAG;QAAC;KAAS;AACf;AAEA,SAAS,4CAAsB,QAAqC,EAAE,OAAiB,EAAE,OAAiB;IACxG,uEAAuE;IACvE,iFAAiF;IACjF,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,WAAW,SACb;QAGF,IAAI,QAAQ,SAAS,OAAO;QAC5B,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAE1D,IAAI,UAAU,CAAC;YACb,IAAI,SAAS,CAAA,wKAAA,iBAAa,EAAE;YAC5B,IAAI,uCAAiB,QAAQ,SAAS,OAAO,GAC3C,oCAAc;iBACT,IAAI,CAAC,0CAAoB,SAC9B,oCAAc;QAElB;QAEA,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;QAC5E;IACF,GAAG;QAAC;QAAU;QAAS;KAAQ;AACjC;AAEA,SAAS,yCAAmB,QAAkB;IAC5C,IAAI,QAAQ,0CAAe,WAAW,CAAC;IACvC,MAAO,SAAS,MAAM,QAAQ,KAAK,SAAU;QAC3C,IAAI,MAAM,aAAa,EACrB,OAAO;QAGT,QAAQ,MAAM,MAAM;IACtB;IAEA,OAAO,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,QAAQ,MAAK;AAC7B;AAEA,SAAS,sCAAgB,QAAqC,EAAE,YAAsB,EAAE,OAAiB;IACvG,kIAAkI;IAClI,iDAAiD;IACjD,MAAM,mBAAmB,CAAA,iKAAA,SAAK,EAAE,OAAO,aAAa,cAAc,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG,cAAkC;IAE/K,4IAA4I;IAC5I,yGAAyG;IACzG,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,QAAQ,SAAS,OAAO;QAC5B,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAC1D,IAAI,CAAC,gBAAgB,SACnB;QAGF,IAAI,UAAU;YACZ,mGAAmG;YACnG,gEAAgE;YAChE,IAAK,CAAA,CAAC,qCAAe,sCAAgB,mCAAa,SAAQ,KACxD,uCAAiB,CAAA,wKAAA,mBAAe,EAAE,gBAAgB,SAAS,OAAO,GAElE,oCAAc;QAElB;QAEA,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;QAC5E;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAU;KAAQ;IAEtB,CAAA,2KAAA,kBAAc,EAAE;QACd,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QAEhF,IAAI,CAAC,cACH;QAGF,+EAA+E;QAC/E,+EAA+E;QAC/E,8EAA8E;QAC9E,8BAA8B;QAC9B,IAAI,YAAY,CAAC;YACf,IAAI,EAAE,GAAG,KAAK,SAAS,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,CAAC,yCAAmB,aAAa,EAAE,WAAW,EACzG;YAGF,IAAI,iBAAiB,cAAc,aAAa;YAChD,IAAI,CAAC,4CAAsB,gBAAgB,aAAa,CAAC,yCAAmB,WAC1E;YAEF,IAAI,WAAW,0CAAe,WAAW,CAAC;YAC1C,IAAI,CAAC,UACH;YAEF,IAAI,gBAAgB,SAAS,aAAa;YAE1C,8DAA8D;YAC9D,IAAI,SAAS,0CAAuB,cAAc,IAAI,EAAE;gBAAC,UAAU;YAAI;YAEvE,qEAAqE;YACrE,OAAO,WAAW,GAAG;YACrB,IAAI,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YAEvE,IAAI,CAAC,iBAAiB,CAAC,cAAc,WAAW,IAAI,kBAAkB,cAAc,IAAI,EAAE;gBACxF,gBAAgB;gBAChB,SAAS,aAAa,GAAG;YAC3B;YAEA,qFAAqF;YACrF,qDAAqD;YACrD,IAAK,CAAA,CAAC,eAAe,CAAC,4CAAsB,aAAa,SAAQ,KAAM,eAAe;gBACpF,OAAO,WAAW,GAAG;gBAErB,kGAAkG;gBAClG,GACE,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;uBAC5D,4CAAsB,aAAa,UAAW;gBAEvD,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,IAAI,aACF,mCAAa,aAAa;qBAG1B,+BAA+B;gBAC/B,0IAA0I;gBAC1I,IAAI,CAAC,0CAAoB,gBACvB,eAAe,IAAI;qBAEnB,mCAAa,eAAe;YAGlC;QACF;QAEA,IAAI,CAAC,SACH,cAAc,gBAAgB,CAAC,WAAW,WAA4B;QAGxE,OAAO;YACL,IAAI,CAAC,SACH,cAAc,mBAAmB,CAAC,WAAW,WAA4B;QAE7E;IACF,GAAG;QAAC;QAAU;QAAc;KAAQ;IAEpC,+GAA+G;IAC/G,CAAA,2KAAA,kBAAc,EAAE;QACd,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QAEhF,IAAI,CAAC,cACH;QAGF,IAAI,WAAW,0CAAe,WAAW,CAAC;QAC1C,IAAI,CAAC,UACH;YAEuB;QAAzB,SAAS,aAAa,GAAG,CAAA,4BAAA,iBAAiB,OAAO,MAAA,QAAxB,8BAAA,KAAA,IAAA,4BAA4B;QACrD,OAAO;YACL,IAAI,WAAW,0CAAe,WAAW,CAAC;YAC1C,IAAI,CAAC,UACH;YAEF,IAAI,gBAAgB,SAAS,aAAa;YAE1C,wGAAwG;YACxG,IAAI,gBAAgB,CAAA,wKAAA,mBAAe,EAAE;YACrC,IACE,gBACG,iBAEA,CAAC,iBAAiB,4CAAsB,eAAe,aAAe,kBAAkB,cAAc,IAAI,IAAI,yCAAmB,SAAS,GAE7I;gBACA,6GAA6G;gBAC7G,IAAI,aAAa,0CAAe,KAAK;gBACrC,sBAAsB;oBACpB,0HAA0H;oBAC1H,IAAI,cAAc,aAAa,KAAK,cAAc,IAAI,EAAE;wBACtD,oFAAoF;wBACpF,IAAI,WAAW,WAAW,WAAW,CAAC;wBACtC,MAAO,SAAU;4BACf,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,WAAW,EAAE;gCAChE,4CAAsB,SAAS,aAAa;gCAC5C;4BACF;4BACA,WAAW,SAAS,MAAM;wBAC5B;wBAEA,wEAAwE;wBACxE,4CAA4C;wBAC5C,WAAW,WAAW,WAAW,CAAC;wBAClC,MAAO,SAAU;4BACf,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,OAAO,IAAI,0CAAe,WAAW,CAAC,SAAS,QAAQ,GAAG;gCACnG,IAAI,OAAO,sCAAgB,SAAS,QAAQ,CAAC,OAAO,EAAE;gCACtD,4CAAsB;gCACtB;4BACF;4BACA,WAAW,SAAS,MAAM;wBAC5B;oBACF;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAa;AAC7B;AAEA,SAAS,4CAAsB,IAAsB;IACnD,6FAA6F;IAC7F,6FAA6F;IAC7F,kEAAkE;IAClE,IAAI,KAAK,aAAa,CAAC,IAAI,YAAY,2CAAqB;QAAC,SAAS;QAAM,YAAY;IAAI,KAC1F,mCAAa;AAEjB;AAMO,SAAS,0CAAuB,IAAa,EAAE,IAA0B,EAAE,KAAiB;IACjG,IAAI,SAAS,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,IAAG,CAAA,uKAAA,aAAS,IAAI,CAAA,uKAAA,cAAU;IAErD,4DAA4D;IAC5D,IAAI,cAAc,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,MAAK,KAAK,YAAY,GAAI,OAAmB;IAE7E,gCAAgC;IAChC,IAAI,MAAM,CAAA,sKAAA,mBAAe,EAAE;IAE3B,mEAAmE;IACnE,IAAI,SAAS,CAAA,4KAAA,yBAAqB,EAChC,KACA,QAAQ,KACR,WAAW,YAAY,EACvB;QACE,YAAW,IAAI;gBAET;YADJ,uCAAuC;YACvC,IAAI,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,aAAA,KAAM,IAAI,MAAA,QAAV,eAAA,KAAA,IAAA,KAAA,IAAA,WAAY,QAAQ,CAAC,OACvB,OAAO,WAAW,aAAa;YAGjC,IAAI,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,KACZ,KAAiB,OAAO,KAAK,WAC7B,KAA0B,YAAY,CAAC,YAAY,SAAS;gBAChE,qEAAqE;gBACrE,IAAI,CAAC,sCAAgB,OACnB,OAAO,WAAW,aAAa;gBAEjC,8FAA8F;gBAC9F,IAAK,OAAO,WAAW,CAAa,OAAO,KAAK,WAC1C,OAAO,WAAW,CAAsB,IAAI,KAAK,WACjD,OAAO,WAAW,CAAsB,IAAI,KAAM,KAA0B,IAAI,EACpF,OAAO,WAAW,aAAa;YAEnC;YAEA,IAAI,OAAO,SACL,CAAA,CAAC,SAAS,uCAAiB,MAAiB,MAAK,KACjD,CAAA,CAAA,CAAC,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,MAAM,KAAI,KAAK,MAAM,CAAC,KAAe,GAEhD,OAAO,WAAW,aAAa;YAGjC,OAAO,WAAW,WAAW;QAC/B;IACF;IAGF,IAAI,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,IAAI,EACZ,OAAO,WAAW,GAAG,KAAK,IAAI;IAGhC,OAAO;AACT;AAKO,SAAS,yCAAmB,GAA8B,EAAE,iBAAsC,CAAC,CAAC;IACzG,OAAO;QACL,WAAU,OAA4B,CAAC,CAAC;YACtC,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,WAAW,eAAe,QAAQ,EAAA,MAAE,OAAO,eAAe,IAAI,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC7G,IAAI,OAAO,QAAQ,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE;YACrD,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,KAAK,QAAQ,CAAC,OAChB,OAAO,WAAW,GAAG;YAEvB,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,CAAC,YAAY,MAAM;gBACrB,OAAO,WAAW,GAAG;gBACrB,WAAW,OAAO,QAAQ;YAC5B;YACA,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,eAAc,OAA4B,cAAc;YACtD,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,WAAW,eAAe,QAAQ,EAAA,MAAE,OAAO,eAAe,IAAI,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC7G,IAAI,OAAO,QAAQ,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE;YACrD,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,KAAK,QAAQ,CAAC,OAChB,OAAO,WAAW,GAAG;iBAChB;gBACL,IAAI,OAAO,2BAAK;gBAChB,IAAI,MACF,mCAAa,MAAM;gBAErB,OAAO,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ;YACjB;YACA,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,CAAC,gBAAgB,MAAM;gBACzB,OAAO,WAAW,GAAG;gBACrB,IAAI,WAAW,2BAAK;gBACpB,IAAI,CAAC,UACH,AACA,OAAO,SADS;gBAGlB,eAAe;YACjB;YACA,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO,iBAAA,QAAA,iBAAA,KAAA,IAAA,eAAgB;QACzB;QACA,YAAW,OAAO,cAAc;YAC9B,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,UAAC,WAAW,eAAe,QAAQ,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC3E,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA,WAAU,OAAO,cAAc;YAC7B,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,UAAC,WAAW,eAAe,QAAQ,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC3E,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,OAAO,2BAAK;YAChB,IAAI,MACF,mCAAa,MAAM;YAErB,OAAO,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ;QACjB;IACF;AACF;AAEA,SAAS,2BAAK,MAAqC;IACjD,IAAI,OAAqC;IACzC,IAAI;IACJ,GAAG;QACD,OAAO,OAAO,SAAS;QACvB,IAAI,MACF,OAAO;IAEX,QAAS,KAAM;IACf,OAAO;AACT;AAGA,MAAM;IASJ,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IAC1B;IAEA,YAAY,IAAc,EAAwB;QAChD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B;IAEA,YAAY,QAAkB,EAAE,MAAgB,EAAE,aAAgC,EAAQ;QACxF,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAA,QAAA,WAAA,KAAA,IAAA,SAAU;QAC5C,IAAI,CAAC,YACH;QAEF,IAAI,OAAO,IAAI,+BAAS;sBAAC;QAAQ;QACjC,WAAW,QAAQ,CAAC;QACpB,KAAK,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;QAC3B,IAAI,eACF,KAAK,aAAa,GAAG;IAEzB;IAEA,QAAQ,IAAc,EAAQ;QAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;IAClC;IAEA,eAAe,QAAkB,EAAQ;QACvC,wBAAwB;QACxB,IAAI,aAAa,MACf;QAEF,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,MACH;QAEF,IAAI,aAAa,KAAK,MAAM;QAC5B,+HAA+H;QAC/H,8FAA8F;QAC9F,KAAK,IAAI,WAAW,IAAI,CAAC,QAAQ,GAC/B,IACE,YAAY,QACZ,KAAK,aAAa,IAClB,QAAQ,aAAa,IACrB,KAAK,QAAQ,IACb,KAAK,QAAQ,CAAC,OAAO,IACrB,uCAAiB,QAAQ,aAAa,EAAE,KAAK,QAAQ,CAAC,OAAO,GAE7D,QAAQ,aAAa,GAAG,KAAK,aAAa;QAG9C,IAAI,WAAW,KAAK,QAAQ;QAC5B,IAAI,YAAY;YACd,WAAW,WAAW,CAAC;YACvB,IAAI,SAAS,IAAI,GAAG,GAClB,SAAS,OAAO,CAAC,CAAA,QAAS,cAAc,WAAW,QAAQ,CAAC;QAEhE;QAEA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ;IACnC;IAEA,wBAAwB;IACxB,CAAC,SAAS,OAAiB,IAAI,CAAC,IAAI,EAAuB;QACzD,IAAI,KAAK,QAAQ,IAAI,MACnB,MAAM;QAER,IAAI,KAAK,QAAQ,CAAC,IAAI,GAAG,GACvB,KAAK,IAAI,SAAS,KAAK,QAAQ,CAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC;IAG3B;IAEA,QAAc;YAGyB;QAFrC,IAAI,UAAU,IAAI;YAEmB;QADrC,KAAK,IAAI,QAAQ,IAAI,CAAC,QAAQ,GAC5B,QAAQ,WAAW,CAAC,KAAK,QAAQ,EAAE,CAAA,wBAAA,CAAA,eAAA,KAAK,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,QAAQ,MAAA,QAArB,0BAAA,KAAA,IAAA,wBAAyB,MAAM,KAAK,aAAa;QAEtF,OAAO;IACT;IApFA,aAAc;aAFN,OAAA,GAAU,IAAI;QAGpB,IAAI,CAAC,IAAI,GAAG,IAAI,+BAAS;YAAC,UAAU;QAAI;QACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI;IAClC;AAkFF;AAEA,MAAM;IAUJ,SAAS,IAAc,EAAQ;QAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAClB,KAAK,MAAM,GAAG,IAAI;IACpB;IACA,YAAY,IAAc,EAAQ;QAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrB,KAAK,MAAM,GAAG;IAChB;IAVA,YAAY,KAA2B,CAAE;aAHlC,QAAA,GAA0B,IAAI;aAC9B,OAAA,GAAU;QAGf,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;IAChC;AASF;AAEO,IAAI,4CAAuB,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6636, "column": 0}, "map": {"version": 3, "file": "useLabel.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/label/dist/packages/%40react-aria/label/src/useLabel.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMAttributes, DOMProps, LabelableProps} from '@react-types/shared';\nimport {ElementType, LabelHTMLAttributes} from 'react';\nimport {useId, useLabels} from '@react-aria/utils';\n\nexport interface LabelAriaProps extends LabelableProps, DOMProps, AriaLabelingProps {\n  /**\n   * The HTML element used to render the label, e.g. 'label', or 'span'.\n   * @default 'label'\n   */\n  labelElementType?: ElementType\n}\n\nexport interface LabelAria {\n  /** Props to apply to the label container element. */\n  labelProps: DOMAttributes | LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props to apply to the field container element being labeled. */\n  fieldProps: AriaLabelingProps & DOMProps\n}\n\n/**\n * Provides the accessibility implementation for labels and their associated elements.\n * Labels provide context for user inputs.\n * @param props - The props for labels and fields.\n */\nexport function useLabel(props: LabelAriaProps): LabelAria {\n  let {\n    id,\n    label,\n    'aria-labelledby': ariaLabelledby,\n    'aria-label': ariaLabel,\n    labelElementType = 'label'\n  } = props;\n\n  id = useId(id);\n  let labelId = useId();\n  let labelProps = {};\n  if (label) {\n    ariaLabelledby = ariaLabelledby ? `${labelId} ${ariaLabelledby}` : labelId;\n    labelProps = {\n      id: labelId,\n      htmlFor: labelElementType === 'label' ? id : undefined\n    };\n  } else if (!ariaLabelledby && !ariaLabel && process.env.NODE_ENV !== 'production') {\n    console.warn('If you do not provide a visible label, you must specify an aria-label or aria-labelledby attribute for accessibility');\n  }\n\n  let fieldProps = useLabels({\n    id,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby\n  });\n\n  return {\n    labelProps,\n    fieldProps\n  };\n}\n"], "names": [], "mappings": ";;;AAsD8C,QAAQ,GAAG,CAAC,QAAQ;;;;AAtDlE;;;;;;;;;;CAUC,GA0BM,SAAS,0CAAS,KAAqB;IAC5C,IAAI,EAAA,IACF,EAAE,EAAA,OACF,KAAK,EACL,mBAAmB,cAAc,EACjC,cAAc,SAAS,EAAA,kBACvB,mBAAmB,OAAA,EACpB,GAAG;IAEJ,KAAK,CAAA,iKAAA,QAAI,EAAE;IACX,IAAI,UAAU,CAAA,iKAAA,QAAI;IAClB,IAAI,aAAa,CAAC;IAClB,IAAI,OAAO;QACT,iBAAiB,iBAAiB,GAAG,QAAQ,CAAC,EAAE,gBAAgB,GAAG;QACnE,aAAa;YACX,IAAI;YACJ,SAAS,qBAAqB,UAAU,KAAK;QAC/C;IACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,iEAAsC,cACnE,QAAQ,IAAI,CAAC;IAGf,IAAI,aAAa,CAAA,qKAAA,YAAQ,EAAE;YACzB;QACA,cAAc;QACd,mBAAmB;IACrB;IAEA,OAAO;oBACL;oBACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6683, "column": 0}, "map": {"version": 3, "file": "useField.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/label/dist/packages/%40react-aria/label/src/useField.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, HelpTextProps, Validation} from '@react-types/shared';\nimport {LabelAria, LabelAriaProps, useLabel} from './useLabel';\nimport {mergeProps, useSlotId} from '@react-aria/utils';\n\nexport interface AriaFieldProps extends LabelAriaProps, HelpTextProps, Omit<Validation<any>, 'isRequired'> {}\n\nexport interface FieldAria extends LabelAria {\n  /** Props for the description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for input fields.\n * Fields accept user input, gain context from their label, and may display a description or error message.\n * @param props - Props for the Field.\n */\nexport function useField(props: AriaFieldProps): FieldAria {\n  let {description, errorMessage, isInvalid, validationState} = props;\n  let {labelProps, fieldProps} = useLabel(props);\n\n  let descriptionId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n  let errorMessageId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n\n  fieldProps = mergeProps(fieldProps, {\n    'aria-describedby': [\n      descriptionId,\n      // Use aria-describedby for error message because aria-errormessage is unsupported using VoiceOver or NVDA. See https://github.com/adobe/react-spectrum/issues/1346#issuecomment-740136268\n      errorMessageId,\n      props['aria-describedby']\n    ].filter(Boolean).join(' ') || undefined\n  });\n\n  return {\n    labelProps,\n    fieldProps,\n    descriptionProps: {\n      id: descriptionId\n    },\n    errorMessageProps: {\n      id: errorMessageId\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC,GAoBM,SAAS,0CAAS,KAAqB;IAC5C,IAAI,EAAA,aAAC,WAAW,EAAA,cAAE,YAAY,EAAA,WAAE,SAAS,EAAA,iBAAE,eAAe,EAAC,GAAG;IAC9D,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,oKAAA,WAAO,EAAE;IAExC,IAAI,gBAAgB,CAAA,iKAAA,YAAQ,EAAE;QAAC,QAAQ;QAAc,QAAQ;QAAe;QAAW;KAAgB;IACvG,IAAI,iBAAiB,CAAA,iKAAA,YAAQ,EAAE;QAAC,QAAQ;QAAc,QAAQ;QAAe;QAAW;KAAgB;IAExG,aAAa,CAAA,sKAAA,aAAS,EAAE,YAAY;QAClC,oBAAoB;YAClB;YACA,0LAA0L;YAC1L;YACA,KAAK,CAAC,mBAAmB;SAC1B,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;IACjC;IAEA,OAAO;oBACL;oBACA;QACA,kBAAkB;YAChB,IAAI;QACN;QACA,mBAAmB;YACjB,IAAI;QACN;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6743, "column": 0}, "map": {"version": 3, "file": "useFormValidation.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/form/dist/packages/%40react-aria/form/src/useFormValidation.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FormValidationState} from '@react-stately/form';\nimport {RefObject, Validation, ValidationResult} from '@react-types/shared';\nimport {setInteractionModality} from '@react-aria/interactions';\nimport {useEffect, useRef} from 'react';\nimport {useEffectEvent, useLayoutEffect} from '@react-aria/utils';\n\ntype ValidatableElement = HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;\n\ninterface FormValidationProps<T> extends Validation<T> {\n  focus?: () => void\n}\n\nexport function useFormValidation<T>(props: FormValidationProps<T>, state: FormValidationState, ref: RefObject<ValidatableElement | null> | undefined): void {\n  let {validationBehavior, focus} = props;\n\n  // This is a useLayoutEffect so that it runs before the useEffect in useFormValidationState, which commits the validation change.\n  useLayoutEffect(() => {\n    if (validationBehavior === 'native' && ref?.current && !ref.current.disabled) {\n      let errorMessage = state.realtimeValidation.isInvalid ? state.realtimeValidation.validationErrors.join(' ') || 'Invalid value.' : '';\n      ref.current.setCustomValidity(errorMessage);\n\n      // Prevent default tooltip for validation message.\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=605277\n      if (!ref.current.hasAttribute('title')) {\n        ref.current.title = '';\n      }\n\n      if (!state.realtimeValidation.isInvalid) {\n        state.updateValidation(getNativeValidity(ref.current));\n      }\n    }\n  });\n\n  let isIgnoredReset = useRef(false);\n  let onReset = useEffectEvent(() => {\n    if (!isIgnoredReset.current) {\n      state.resetValidation();\n    }\n  });\n\n  let onInvalid = useEffectEvent((e: Event) => {\n    // Only commit validation if we are not already displaying one.\n    // This avoids clearing server errors that the user didn't actually fix.\n    if (!state.displayValidation.isInvalid) {\n      state.commitValidation();\n    }\n\n    // Auto focus the first invalid input in a form, unless the error already had its default prevented.\n    let form = ref?.current?.form;\n    if (!e.defaultPrevented && ref && form && getFirstInvalidInput(form) === ref.current) {\n      if (focus) {\n        focus();\n      } else {\n        ref.current?.focus();\n      }\n\n      // Always show focus ring.\n      setInteractionModality('keyboard');\n    }\n\n    // Prevent default browser error UI from appearing.\n    e.preventDefault();\n  });\n\n  let onChange = useEffectEvent(() => {\n    state.commitValidation();\n  });\n\n  useEffect(() => {\n    let input = ref?.current;\n    if (!input) {\n      return;\n    }\n\n    let form = input.form;\n\n    let reset = form?.reset;\n    if (form) {\n      // Try to detect React's automatic form reset behavior so we don't clear\n      // validation errors that are returned by server actions.\n      // To do this, we ignore programmatic form resets that occur outside a user event.\n      // This is best-effort. There may be false positives, e.g. setTimeout.\n      form.reset = () => {\n        // React uses MessageChannel for scheduling, so ignore 'message' events.\n        isIgnoredReset.current = !window.event || (window.event.type === 'message' && window.event.target instanceof MessagePort);\n        reset?.call(form);\n        isIgnoredReset.current = false;\n      };\n    }\n\n    input.addEventListener('invalid', onInvalid);\n    input.addEventListener('change', onChange);\n    form?.addEventListener('reset', onReset);\n    return () => {\n      input!.removeEventListener('invalid', onInvalid);\n      input!.removeEventListener('change', onChange);\n      form?.removeEventListener('reset', onReset);\n      if (form) {\n        // @ts-ignore\n        form.reset = reset;\n      }\n    };\n  }, [ref, onInvalid, onChange, onReset, validationBehavior]);\n}\n\nfunction getValidity(input: ValidatableElement) {\n  // The native ValidityState object is live, meaning each property is a getter that returns the current state.\n  // We need to create a snapshot of the validity state at the time this function is called to avoid unpredictable React renders.\n  let validity = input.validity;\n  return {\n    badInput: validity.badInput,\n    customError: validity.customError,\n    patternMismatch: validity.patternMismatch,\n    rangeOverflow: validity.rangeOverflow,\n    rangeUnderflow: validity.rangeUnderflow,\n    stepMismatch: validity.stepMismatch,\n    tooLong: validity.tooLong,\n    tooShort: validity.tooShort,\n    typeMismatch: validity.typeMismatch,\n    valueMissing: validity.valueMissing,\n    valid: validity.valid\n  };\n}\n\nfunction getNativeValidity(input: ValidatableElement): ValidationResult {\n  return {\n    isInvalid: !input.validity.valid,\n    validationDetails: getValidity(input),\n    validationErrors: input.validationMessage ? [input.validationMessage] : []\n  };\n}\n\nfunction getFirstInvalidInput(form: HTMLFormElement): ValidatableElement | null {\n  for (let i = 0; i < form.elements.length; i++) {\n    let element = form.elements[i] as ValidatableElement;\n    if (!element.validity.valid) {\n      return element;\n    }\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAcM,SAAS,0CAAqB,KAA6B,EAAE,KAA0B,EAAE,GAAqD;IACnJ,IAAI,EAAA,oBAAC,kBAAkB,EAAA,OAAE,KAAK,EAAC,GAAG;IAElC,iIAAiI;IACjI,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,uBAAuB,YAAA,CAAY,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,IAAK,OAAO,KAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;YAC5E,IAAI,eAAe,MAAM,kBAAkB,CAAC,SAAS,GAAG,MAAM,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,mBAAmB;YAClI,IAAI,OAAO,CAAC,iBAAiB,CAAC;YAE9B,kDAAkD;YAClD,sDAAsD;YACtD,IAAI,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,UAC5B,IAAI,OAAO,CAAC,KAAK,GAAG;YAGtB,IAAI,CAAC,MAAM,kBAAkB,CAAC,SAAS,EACrC,MAAM,gBAAgB,CAAC,wCAAkB,IAAI,OAAO;QAExD;IACF;IAEA,IAAI,iBAAiB,CAAA,iKAAA,SAAK,EAAE;IAC5B,IAAI,UAAU,CAAA,0KAAA,iBAAa,EAAE;QAC3B,IAAI,CAAC,eAAe,OAAO,EACzB,MAAM,eAAe;IAEzB;IAEA,IAAI,YAAY,CAAA,0KAAA,iBAAa,EAAE,CAAC;YAQnB;QAPX,+DAA+D;QAC/D,wEAAwE;QACxE,IAAI,CAAC,MAAM,iBAAiB,CAAC,SAAS,EACpC,MAAM,gBAAgB;QAGxB,oGAAoG;QACpG,IAAI,OAAO,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,CAAA,eAAA,IAAK,OAAO,MAAA,QAAZ,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAc,IAAI;QAC7B,IAAI,CAAC,EAAE,gBAAgB,IAAI,OAAO,QAAQ,2CAAqB,UAAU,IAAI,OAAO,EAAE;gBAIlF;YAHF,IAAI,OACF;kBAEA,gBAAA,IAAI,OAAO,MAAA,QAAX,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAa,KAAK;YAGpB,0BAA0B;YAC1B,CAAA,kLAAA,yBAAqB,EAAE;QACzB;QAEA,mDAAmD;QACnD,EAAE,cAAc;IAClB;IAEA,IAAI,WAAW,CAAA,0KAAA,iBAAa,EAAE;QAC5B,MAAM,gBAAgB;IACxB;IAEA,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,QAAQ,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,IAAK,OAAO;QACxB,IAAI,CAAC,OACH;QAGF,IAAI,OAAO,MAAM,IAAI;QAErB,IAAI,QAAQ,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;QACvB,IAAI,MACF,AACA,yDAAyD,eADe;QAExE,kFAAkF;QAClF,sEAAsE;QACtE,KAAK,KAAK,GAAG;YACX,wEAAwE;YACxE,eAAe,OAAO,GAAG,CAAC,OAAO,KAAK,IAAK,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,YAAY;YAC7G,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,IAAI,CAAC;YACZ,eAAe,OAAO,GAAG;QAC3B;QAGF,MAAM,gBAAgB,CAAC,WAAW;QAClC,MAAM,gBAAgB,CAAC,UAAU;QACjC,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,gBAAgB,CAAC,SAAS;QAChC,OAAO;YACL,MAAO,mBAAmB,CAAC,WAAW;YACtC,MAAO,mBAAmB,CAAC,UAAU;YACrC,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,mBAAmB,CAAC,SAAS;YACnC,IAAI,MACF,AACA,KAAK,KAAK,GADG,AACA;QAEjB;IACF,GAAG;QAAC;QAAK;QAAW;QAAU;QAAS;KAAmB;AAC5D;AAEA,SAAS,kCAAY,KAAyB;IAC5C,6GAA6G;IAC7G,+HAA+H;IAC/H,IAAI,WAAW,MAAM,QAAQ;IAC7B,OAAO;QACL,UAAU,SAAS,QAAQ;QAC3B,aAAa,SAAS,WAAW;QACjC,iBAAiB,SAAS,eAAe;QACzC,eAAe,SAAS,aAAa;QACrC,gBAAgB,SAAS,cAAc;QACvC,cAAc,SAAS,YAAY;QACnC,SAAS,SAAS,OAAO;QACzB,UAAU,SAAS,QAAQ;QAC3B,cAAc,SAAS,YAAY;QACnC,cAAc,SAAS,YAAY;QACnC,OAAO,SAAS,KAAK;IACvB;AACF;AAEA,SAAS,wCAAkB,KAAyB;IAClD,OAAO;QACL,WAAW,CAAC,MAAM,QAAQ,CAAC,KAAK;QAChC,mBAAmB,kCAAY;QAC/B,kBAAkB,MAAM,iBAAiB,GAAG;YAAC,MAAM,iBAAiB;SAAC,GAAG,EAAE;IAC5E;AACF;AAEA,SAAS,2CAAqB,IAAqB;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,IAAK;QAC7C,IAAI,UAAU,KAAK,QAAQ,CAAC,EAAE;QAC9B,IAAI,CAAC,QAAQ,QAAQ,CAAC,KAAK,EACzB,OAAO;IAEX;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6873, "column": 0}, "map": {"version": 3, "file": "ar-AE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/ar-AE.json"], "sourcesContent": ["{\n  \"Empty\": \"فارغ\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,4BAAI,CAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6888, "column": 0}, "map": {"version": 3, "file": "bg-BG.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/bg-BG.json"], "sourcesContent": ["{\n  \"Empty\": \"Изпразни\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,wDAAQ,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6903, "column": 0}, "map": {"version": 3, "file": "cs-CZ.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/cs-CZ.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,aAAO,CAAC;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6918, "column": 0}, "map": {"version": 3, "file": "da-DK.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/da-DK.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,GAAG,CAAC;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6933, "column": 0}, "map": {"version": 3, "file": "de-DE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/de-DE.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,IAAI,CAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6948, "column": 0}, "map": {"version": 3, "file": "el-GR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/el-GR.json"], "sourcesContent": ["{\n  \"Empty\": \"Άδειο\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,mCAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6963, "column": 0}, "map": {"version": 3, "file": "en-US.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/en-US.json"], "sourcesContent": ["{\n  \"Empty\": \"Empty\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,KAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6978, "column": 0}, "map": {"version": 3, "file": "es-ES.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/es-ES.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>ac<PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,QAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6993, "column": 0}, "map": {"version": 3, "file": "et-EE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/et-EE.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,WAAQ,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7008, "column": 0}, "map": {"version": 3, "file": "fi-FI.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/fi-FI.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,QAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7023, "column": 0}, "map": {"version": 3, "file": "fr-FR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/fr-FR.json"], "sourcesContent": ["{\n  \"Empty\": \"Vide\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,IAAI,CAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7038, "column": 0}, "map": {"version": 3, "file": "he-IL.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/he-IL.json"], "sourcesContent": ["{\n  \"Empty\": \"ריק\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,qBAAG,CAAC;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7053, "column": 0}, "map": {"version": 3, "file": "hr-HR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/hr-HR.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>razno\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,MAAM,CAAC;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7068, "column": 0}, "map": {"version": 3, "file": "hu-HU.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/hu-HU.json"], "sourcesContent": ["{\n  \"Empty\": \"Üres\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,OAAI,CAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7083, "column": 0}, "map": {"version": 3, "file": "it-IT.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/it-IT.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,KAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7098, "column": 0}, "map": {"version": 3, "file": "ja-JP.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/ja-JP.json"], "sourcesContent": ["{\n  \"Empty\": \"空\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,QAAC,CAAC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7113, "column": 0}, "map": {"version": 3, "file": "ko-KR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/ko-KR.json"], "sourcesContent": ["{\n  \"Empty\": \"비어 있음\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,iCAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7128, "column": 0}, "map": {"version": 3, "file": "lt-LT.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/lt-LT.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>š<PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,mBAAO,CAAC;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7143, "column": 0}, "map": {"version": 3, "file": "lv-LV.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/lv-LV.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,WAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7158, "column": 0}, "map": {"version": 3, "file": "nb-NO.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/nb-NO.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,GAAG,CAAC;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7173, "column": 0}, "map": {"version": 3, "file": "nl-NL.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/nl-NL.json"], "sourcesContent": ["{\n  \"Empty\": \"Leeg\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,IAAI,CAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7188, "column": 0}, "map": {"version": 3, "file": "pl-PL.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/pl-PL.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,KAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7203, "column": 0}, "map": {"version": 3, "file": "pt-BR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/pt-BR.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>azio\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,KAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7218, "column": 0}, "map": {"version": 3, "file": "pt-PT.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/pt-PT.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>azio\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,KAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7233, "column": 0}, "map": {"version": 3, "file": "ro-RO.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/ro-RO.json"], "sourcesContent": ["{\n  \"Empty\": \"Gol\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,GAAG,CAAC;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7248, "column": 0}, "map": {"version": 3, "file": "ru-RU.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/ru-RU.json"], "sourcesContent": ["{\n  \"Empty\": \"Не заполнено\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,8EAAY,CAAC;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7263, "column": 0}, "map": {"version": 3, "file": "sk-SK.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/sk-SK.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,UAAO,CAAC;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7278, "column": 0}, "map": {"version": 3, "file": "sl-SI.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/sl-SI.json"], "sourcesContent": ["{\n  \"Empty\": \"Prazen\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,MAAM,CAAC;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7293, "column": 0}, "map": {"version": 3, "file": "sr-SP.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/sr-SP.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON>razno\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,MAAM,CAAC;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7308, "column": 0}, "map": {"version": 3, "file": "sv-SE.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/sv-SE.json"], "sourcesContent": ["{\n  \"Empty\": \"<PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,IAAI,CAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7323, "column": 0}, "map": {"version": 3, "file": "tr-TR.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/tr-TR.json"], "sourcesContent": ["{\n  \"Empty\": \"Boş\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,SAAG,CAAC;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7338, "column": 0}, "map": {"version": 3, "file": "uk-UA.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/uk-UA.json"], "sourcesContent": ["{\n  \"Empty\": \"Пусто\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,mCAAK,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7353, "column": 0}, "map": {"version": 3, "file": "zh-CN.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/zh-CN.json"], "sourcesContent": ["{\n  \"Empty\": \"空\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,QAAC,CAAC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7368, "column": 0}, "map": {"version": 3, "file": "zh-TW.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/intl/zh-TW.json"], "sourcesContent": ["{\n  \"Empty\": \"空白\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,SAAS,CAAC,gBAAE,CAAC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7383, "column": 0}, "map": {"version": 3, "file": "intlStrings.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/src/%2A.js"], "sourcesContent": ["const _temp0 = require(\"../intl/ar-AE.json\");\nconst _temp1 = require(\"../intl/bg-BG.json\");\nconst _temp2 = require(\"../intl/cs-CZ.json\");\nconst _temp3 = require(\"../intl/da-DK.json\");\nconst _temp4 = require(\"../intl/de-DE.json\");\nconst _temp5 = require(\"../intl/el-GR.json\");\nconst _temp6 = require(\"../intl/en-US.json\");\nconst _temp7 = require(\"../intl/es-ES.json\");\nconst _temp8 = require(\"../intl/et-EE.json\");\nconst _temp9 = require(\"../intl/fi-FI.json\");\nconst _temp10 = require(\"../intl/fr-FR.json\");\nconst _temp11 = require(\"../intl/he-IL.json\");\nconst _temp12 = require(\"../intl/hr-HR.json\");\nconst _temp13 = require(\"../intl/hu-HU.json\");\nconst _temp14 = require(\"../intl/it-IT.json\");\nconst _temp15 = require(\"../intl/ja-JP.json\");\nconst _temp16 = require(\"../intl/ko-KR.json\");\nconst _temp17 = require(\"../intl/lt-LT.json\");\nconst _temp18 = require(\"../intl/lv-LV.json\");\nconst _temp19 = require(\"../intl/nb-NO.json\");\nconst _temp20 = require(\"../intl/nl-NL.json\");\nconst _temp21 = require(\"../intl/pl-PL.json\");\nconst _temp22 = require(\"../intl/pt-BR.json\");\nconst _temp23 = require(\"../intl/pt-PT.json\");\nconst _temp24 = require(\"../intl/ro-RO.json\");\nconst _temp25 = require(\"../intl/ru-RU.json\");\nconst _temp26 = require(\"../intl/sk-SK.json\");\nconst _temp27 = require(\"../intl/sl-SI.json\");\nconst _temp28 = require(\"../intl/sr-SP.json\");\nconst _temp29 = require(\"../intl/sv-SE.json\");\nconst _temp30 = require(\"../intl/tr-TR.json\");\nconst _temp31 = require(\"../intl/uk-UA.json\");\nconst _temp32 = require(\"../intl/zh-CN.json\");\nconst _temp33 = require(\"../intl/zh-TW.json\");\nmodule.exports = {\n  \"ar-AE\": _temp0,\n  \"bg-BG\": _temp1,\n  \"cs-CZ\": _temp2,\n  \"da-DK\": _temp3,\n  \"de-DE\": _temp4,\n  \"el-GR\": _temp5,\n  \"en-US\": _temp6,\n  \"es-ES\": _temp7,\n  \"et-EE\": _temp8,\n  \"fi-FI\": _temp9,\n  \"fr-FR\": _temp10,\n  \"he-IL\": _temp11,\n  \"hr-HR\": _temp12,\n  \"hu-HU\": _temp13,\n  \"it-IT\": _temp14,\n  \"ja-JP\": _temp15,\n  \"ko-KR\": _temp16,\n  \"lt-LT\": _temp17,\n  \"lv-LV\": _temp18,\n  \"nb-NO\": _temp19,\n  \"nl-NL\": _temp20,\n  \"pl-PL\": _temp21,\n  \"pt-BR\": _temp22,\n  \"pt-PT\": _temp23,\n  \"ro-RO\": _temp24,\n  \"ru-RU\": _temp25,\n  \"sk-SK\": _temp26,\n  \"sl-SI\": _temp27,\n  \"sr-SP\": _temp28,\n  \"sv-SE\": _temp29,\n  \"tr-TR\": _temp30,\n  \"uk-UA\": _temp31,\n  \"zh-CN\": _temp32,\n  \"zh-TW\": _temp33\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,4BAAiB;IACf,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;IACT,+KAAS,UAAA;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7499, "column": 0}, "map": {"version": 3, "file": "useSpinButton.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-aria/spinbutton/dist/packages/%40react-aria/spinbutton/src/useSpinButton.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {announce, clearAnnouncer} from '@react-aria/live-announcer';\nimport {AriaButtonProps} from '@react-types/button';\nimport {DOMAttributes, InputBase, RangeInputBase, Validation, ValueBase} from '@react-types/shared';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {useEffect, useRef} from 'react';\nimport {useEffectEvent, useGlobalListeners} from '@react-aria/utils';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\n\nexport interface SpinButtonProps extends InputBase, Validation<number>, ValueBase<number>, RangeInputBase<number> {\n  textValue?: string,\n  onIncrement?: () => void,\n  onIncrementPage?: () => void,\n  onDecrement?: () => void,\n  onDecrementPage?: () => void,\n  onDecrementToMin?: () => void,\n  onIncrementToMax?: () => void\n}\n\nexport interface SpinbuttonAria {\n  spinButtonProps: DOMAttributes,\n  incrementButtonProps: AriaButtonProps,\n  decrementButtonProps: AriaButtonProps\n}\n\nexport function useSpinButton(\n  props: SpinButtonProps\n): SpinbuttonAria {\n  const _async = useRef<number>(undefined);\n  let {\n    value,\n    textValue,\n    minValue,\n    maxValue,\n    isDisabled,\n    isReadOnly,\n    isRequired,\n    onIncrement,\n    onIncrementPage,\n    onDecrement,\n    onDecrementPage,\n    onDecrementToMin,\n    onIncrementToMax\n  } = props;\n  const stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/spinbutton');\n\n  const clearAsync = () => clearTimeout(_async.current);\n\n\n  useEffect(() => {\n    return () => clearAsync();\n  }, []);\n\n  let onKeyDown = (e) => {\n    if (e.ctrlKey || e.metaKey || e.shiftKey || e.altKey || isReadOnly || e.nativeEvent.isComposing) {\n      return;\n    }\n\n    switch (e.key) {\n      case 'PageUp':\n        if (onIncrementPage) {\n          e.preventDefault();\n          onIncrementPage?.();\n          break;\n        }\n      // fallthrough!\n      case 'ArrowUp':\n      case 'Up':\n        if (onIncrement) {\n          e.preventDefault();\n          onIncrement?.();\n        }\n        break;\n      case 'PageDown':\n        if (onDecrementPage) {\n          e.preventDefault();\n          onDecrementPage?.();\n          break;\n        }\n      // fallthrough\n      case 'ArrowDown':\n      case 'Down':\n        if (onDecrement) {\n          e.preventDefault();\n          onDecrement?.();\n        }\n        break;\n      case 'Home':\n        if (onDecrementToMin) {\n          e.preventDefault();\n          onDecrementToMin?.();\n        }\n        break;\n      case 'End':\n        if (onIncrementToMax) {\n          e.preventDefault();\n          onIncrementToMax?.();\n        }\n        break;\n    }\n  };\n\n  let isFocused = useRef(false);\n  let onFocus = () => {\n    isFocused.current = true;\n  };\n\n  let onBlur = () => {\n    isFocused.current = false;\n  };\n\n  // Replace Unicode hyphen-minus (U+002D) with minus sign (U+2212).\n  // This ensures that macOS VoiceOver announces it as \"minus\" even with other characters between the minus sign\n  // and the number (e.g. currency symbol). Otherwise it announces nothing because it assumes the character is a hyphen.\n  // In addition, replace the empty string with the word \"Empty\" so that iOS VoiceOver does not read \"50%\" for an empty field.\n  let ariaTextValue = textValue === '' ? stringFormatter.format('Empty') : (textValue || `${value}`).replace('-', '\\u2212');\n\n  useEffect(() => {\n    if (isFocused.current) {\n      clearAnnouncer('assertive');\n      announce(ariaTextValue, 'assertive');\n    }\n  }, [ariaTextValue]);\n\n  const onIncrementPressStart = useEffectEvent(\n    (initialStepDelay: number) => {\n      clearAsync();\n      onIncrement?.();\n      // Start spinning after initial delay\n      _async.current = window.setTimeout(\n        () => {\n          if ((maxValue === undefined || isNaN(maxValue)) || (value === undefined || isNaN(value)) || value < maxValue) {\n            onIncrementPressStart(60);\n          }\n        },\n        initialStepDelay\n      );\n    }\n  );\n\n  const onDecrementPressStart = useEffectEvent(\n    (initialStepDelay: number) => {\n      clearAsync();\n      onDecrement?.();\n      // Start spinning after initial delay\n      _async.current = window.setTimeout(\n        () => {\n          if ((minValue === undefined || isNaN(minValue)) || (value === undefined || isNaN(value)) || value > minValue) {\n            onDecrementPressStart(60);\n          }\n        },\n        initialStepDelay\n      );\n    }\n  );\n\n  let cancelContextMenu = (e) => {\n    e.preventDefault();\n  };\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  return {\n    spinButtonProps: {\n      role: 'spinbutton',\n      'aria-valuenow': value !== undefined && !isNaN(value) ? value : undefined,\n      'aria-valuetext': ariaTextValue,\n      'aria-valuemin': minValue,\n      'aria-valuemax': maxValue,\n      'aria-disabled': isDisabled || undefined,\n      'aria-readonly': isReadOnly || undefined,\n      'aria-required': isRequired || undefined,\n      onKeyDown,\n      onFocus,\n      onBlur\n    },\n    incrementButtonProps: {\n      onPressStart: () => {\n        onIncrementPressStart(400);\n        addGlobalListener(window, 'contextmenu', cancelContextMenu);\n      },\n      onPressEnd: () => {\n        clearAsync();\n        removeAllGlobalListeners();\n      },\n      onFocus,\n      onBlur\n    },\n    decrementButtonProps: {\n      onPressStart: () => {\n        onDecrementPressStart(400);\n        addGlobalListener(window, 'contextmenu', cancelContextMenu);\n      },\n      onPressEnd: () => {\n        clearAsync();\n        removeAllGlobalListeners();\n      },\n      onFocus,\n      onBlur\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA4BM,SAAS,0CACd,KAAsB;IAEtB,MAAM,SAAS,CAAA,iKAAA,SAAK,EAAU;IAC9B,IAAI,EAAA,OACF,KAAK,EAAA,WACL,SAAS,EAAA,UACT,QAAQ,EAAA,UACR,QAAQ,EAAA,YACR,UAAU,EAAA,YACV,UAAU,EAAA,YACV,UAAU,EAAA,aACV,WAAW,EAAA,iBACX,eAAe,EAAA,aACf,WAAW,EAAA,iBACX,eAAe,EAAA,kBACf,gBAAgB,EAAA,kBAChB,gBAAgB,EACjB,GAAG;IACJ,MAAM,kBAAkB,CAAA,sLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,wKAAA,CAAA,UAAA,CAAW,GAAG;IAElE,MAAM,aAAa,IAAM,aAAa,OAAO,OAAO;IAGpD,CAAA,iKAAA,YAAQ,EAAE;QACR,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,IAAI,YAAY,CAAC;QACf,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,IAAI,EAAE,MAAM,IAAI,cAAc,EAAE,WAAW,CAAC,WAAW,EAC7F;QAGF,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,IAAI,iBAAiB;oBACnB,EAAE,cAAc;oBAChB,oBAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA;oBACA;gBACF;YACF,eAAe;YACf,KAAK;YACL,KAAK;gBACH,IAAI,aAAa;oBACf,EAAE,cAAc;oBAChB,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,iBAAiB;oBACnB,EAAE,cAAc;oBAChB,oBAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA;oBACA;gBACF;YACF,cAAc;YACd,KAAK;YACL,KAAK;gBACH,IAAI,aAAa;oBACf,EAAE,cAAc;oBAChB,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,kBAAkB;oBACpB,EAAE,cAAc;oBAChB,qBAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAA;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,kBAAkB;oBACpB,EAAE,cAAc;oBAChB,qBAAA,QAAA,qBAAA,KAAA,IAAA,KAAA,IAAA;gBACF;gBACA;QACJ;IACF;IAEA,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE;IACvB,IAAI,UAAU;QACZ,UAAU,OAAO,GAAG;IACtB;IAEA,IAAI,SAAS;QACX,UAAU,OAAO,GAAG;IACtB;IAEA,kEAAkE;IAClE,8GAA8G;IAC9G,sHAAsH;IACtH,4HAA4H;IAC5H,IAAI,gBAAgB,cAAc,KAAK,gBAAgB,MAAM,CAAC,WAAY,CAAA,aAAa,GAAG,OAAM,EAAG,OAAO,CAAC,KAAK;IAEhH,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,CAAA,qLAAA,iBAAa,EAAE;YACf,CAAA,qLAAA,WAAO,EAAE,eAAe;QAC1B;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAA,0KAAA,iBAAa,EACzC,CAAC;QACC;QACA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA;QACA,qCAAqC;QACrC,OAAO,OAAO,GAAG,OAAO,UAAU,CAChC;YACE,IAAK,aAAa,aAAa,MAAM,aAAe,UAAU,aAAa,MAAM,UAAW,QAAQ,UAClG,sBAAsB;QAE1B,GACA;IAEJ;IAGF,MAAM,wBAAwB,CAAA,0KAAA,iBAAa,EACzC,CAAC;QACC;QACA,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA;QACA,qCAAqC;QACrC,OAAO,OAAO,GAAG,OAAO,UAAU,CAChC;YACE,IAAK,aAAa,aAAa,MAAM,aAAe,UAAU,aAAa,MAAM,UAAW,QAAQ,UAClG,sBAAsB;QAE1B,GACA;IAEJ;IAGF,IAAI,oBAAoB,CAAC;QACvB,EAAE,cAAc;IAClB;IAEA,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,8KAAA,qBAAiB;IAErE,OAAO;QACL,iBAAiB;YACf,MAAM;YACN,iBAAiB,UAAU,aAAa,CAAC,MAAM,SAAS,QAAQ;YAChE,kBAAkB;YAClB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB,cAAc;YAC/B,iBAAiB,cAAc;YAC/B,iBAAiB,cAAc;uBAC/B;qBACA;oBACA;QACF;QACA,sBAAsB;YACpB,cAAc;gBACZ,sBAAsB;gBACtB,kBAAkB,QAAQ,eAAe;YAC3C;YACA,YAAY;gBACV;gBACA;YACF;qBACA;oBACA;QACF;QACA,sBAAsB;YACpB,cAAc;gBACZ,sBAAsB;gBACtB,kBAAkB,QAAQ,eAAe;YAC3C;YACA,YAAY;gBACV;gBACA;YACF;qBACA;oBACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}]}